﻿using goodkey_common.Context;
using goodkey_common.Models;

namespace goodkey_common.Repositories
{
	public interface IUnitRepository
	{
		IEnumerable<Unit> GetAll();
	}

	public class UnitRepository : IUnitRepository
	{
		private readonly GoodkeyContext _context;

		public UnitRepository(GoodkeyContext context)
		{
			_context = context;
		}
		public IEnumerable<Unit> GetAll()
		{
			return _context.Unit;
		}
	}
}

