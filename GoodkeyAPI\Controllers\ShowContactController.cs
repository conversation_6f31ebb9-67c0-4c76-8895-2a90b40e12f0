﻿
using goodkey_cms.DTO.Contact;
using goodkey_cms.Services;
using goodkey_common.DTO;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class ShowContactController : ControllerBase
	{
		private readonly IContactRepository _contactRepository;
		private readonly AuthService _authService;

		public ShowContactController(IContactRepository contactRepository, AuthService authService)
		{
			_contactRepository = contactRepository;
			_authService = authService;
		}

		[HttpGet("{locationId}")]
		public async Task<GenericRespond<IEnumerable<ContactDto>>> GetAllByLocation(int locationId)
		{
			var contacts = await _contactRepository.GetByLocationIdAsync(locationId);

			var result = contacts.OrderByDescending(x => x.ContactId).Select(c => new ContactDto
			{
				Id = c.ContactId,
				LocationId = c.LocationId,
				CompanyId = c.CompanyId,
				ContactTypeId = c.ContactTypeId,
				FirstName = c.FirstName,
				LastName = c.LastName,
				Email = c.Email,
				Telephone = c.Telephone,
				Ext = c.Ext,
				Cellphone = c.Cellphone,
				IsArchived = c.IsArchived
			});


			return new GenericRespond<IEnumerable<ContactDto>>
			{
				Data = result,
				StatusCode = 200,
				Message = "Contacts retrieved successfully"
			};
		}

		[HttpGet("contact/{contactId}")]
		public async Task<GenericRespond<ContactDto>> GetById(int contactId)
		{
			var contact = await _contactRepository.GetByIdAsync(contactId);
			if (contact == null)
			{
				return new GenericRespond<ContactDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Contact not found"
				};
			}

			return new GenericRespond<ContactDto>
			{
				Data = new ContactDto
				{
					Id = contact.ContactId,
					LocationId = contact.LocationId,
					CompanyId = contact.CompanyId,
					ContactTypeId = contact.ContactTypeId,
					FirstName = contact.FirstName,
					LastName = contact.LastName,
					Email = contact.Email,
					Telephone = contact.Telephone,
					Ext = contact.Ext,
					Cellphone = contact.Cellphone,
					IsArchived = contact.IsArchived
				},
				StatusCode = 200,
				Message = "Contact retrieved successfully"
			};
		}

		[HttpPost]
		public async Task<GenericRespond<bool>> Create([FromBody] CreateContactDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool> { Data = false, StatusCode = 401, Message = "Unauthorized" };
			}

			try
			{
				var contact = new Contact
				{
					ContactTypeId = dto.ContactTypeId == null ? 0 : (int)dto.ContactTypeId,
					LocationId = dto.LocationId,
					CompanyId = dto.CompanyId,
					FirstName = dto.FirstName,
					LastName = dto.LastName,
					Email = dto.Email,
					Telephone = dto.Telephone,
					Ext = dto.Ext,
					Cellphone = dto.Cellphone,
					CreatedById = user.UserId,
					CreatedAt = DateTime.Now
				};

				await _contactRepository.AddAsync(contact);

				return new GenericRespond<bool>
				{
					Data = true,
					StatusCode = 200,
					Message = "Contact created successfully"
				};
			}
			catch (Exception ex)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 500,
					Message = $"Error creating contact: {ex.Message}"
				};
			}
		}

		[HttpPatch("{contactId}")]
		public async Task<GenericRespond<bool>> Update(int contactId, [FromBody] UpdateContactDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool> { Data = false, StatusCode = 401, Message = "Unauthorized" };
			}

			try
			{
				var updated = new Contact
				{
					FirstName = dto.FirstName,
					LastName = dto.LastName,
					Email = dto.Email,
					Telephone = dto.Telephone,
					Ext = dto.Ext,
					Cellphone = dto.Cellphone,
					UpdatedById = user.UserId,
					UpdatedAt = DateTime.Now,
					IsArchived = dto.IsArchived,
					ArchivedById = dto.IsArchived == true ? user.UserId : null,
					ArchivedAt = dto.IsArchived == true ? DateTime.Now : null
				};

				await _contactRepository.UpdateAsync(contactId, updated);

				return new GenericRespond<bool>
				{
					Data = true,
					StatusCode = 200,
					Message = "Contact updated successfully"
				};
			}
			catch (Exception ex)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 500,
					Message = $"Error updating contact: {ex.Message}"
				};
			}
		}
	}
}
