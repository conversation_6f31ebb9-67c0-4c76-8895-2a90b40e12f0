﻿using Microsoft.AspNetCore.Http;

namespace goodkey_common.DTO.Menu
{
    public class MenuData
    {
        public int? ParentId { get; set; }
        public string Name { get; set; } = null!;
        public string? Description { get; set; }
        public string? Keywords { get; set; }
        public string? MetaDescription { get; set; }
        public string? Url { get; set; }
        public int DisplayOrder { get; set; }
        public string? PermissionKey { get; set; }
        public string? IconName { get; set; }
        public string? Target { get; set; }
        public bool? IsVisible { get; set; }
        public bool? IsDashboard { get; set; }
        public string? Direction { get; set; }
        public bool? IsStatic { get; set; }
        public IFormFile? Image { get; set; }
        public string? ImagePath { get; set; }
        public int? Level { get; set; }
        public int? RoleId { get; set; }
        public bool IsParent { get; set; }
        public int SectionId { get; set; }
    }
}
