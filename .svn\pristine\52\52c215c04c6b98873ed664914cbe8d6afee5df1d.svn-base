﻿namespace goodkey_cms.DTO.ShowLocation
{
    public class ShowLocationInList
    {
        public int Id { get; set; }

        public string? Name { get; set; }

        public string? LocationCode { get; set; }

        public string? City { get; set; }

        public string? Province { get; set; }

        public bool? IsArchived { get; set; }
    }

    public class ShowLocationGeneralDto
    {
        public int? Id { get; set; }
        public string? LocationCode { get; set; }
        public string? Name { get; set; }
        public string? Telephone { get; set; }
        public string? Tollfree { get; set; }
        public string? Fax { get; set; }
        public string? MapLink { get; set; }
        public string? Website { get; set; }
        public string? Email { get; set; }
        public string? AccessPlan { get; set; }
        public bool? IsArchived { get; set; }
    }

    public class ShowLocationAddressDto
    {
        public int Id { get; set; }
        public string? Address1 { get; set; }
        public string? Address2 { get; set; }
        public string? PostalCode { get; set; }
        public string? City { get; set; }
        public int? ProvinceId { get; set; }
        public int? CountryId { get; set; }
        public bool? SameForShipping { get; set; }
        public string? ShippingAddress1 { get; set; }
        public string? ShippingAddress2 { get; set; }
        public string? ShippingPostalCode { get; set; }
        public string? ShippingCity { get; set; }
        public int? ShippingProvinceId { get; set; }
        public int? ShippingCountryId { get; set; }
    }
}
