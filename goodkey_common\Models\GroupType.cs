﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class GroupType
    {
        public GroupType()
        {
            Group = new HashSet<Group>();
            Offering = new HashSet<Offering>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }

        public virtual ICollection<Group> Group { get; set; }
        public virtual ICollection<Offering> Offering { get; set; }
    }
}