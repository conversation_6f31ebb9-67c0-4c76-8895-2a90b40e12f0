﻿
using goodkey_common.Context;
using goodkey_common.Models;
using goodkey_common.DTO;
using Microsoft.EntityFrameworkCore;
using goodkey_cms.DTO;
using goodkey_cms.DTO.User;

namespace goodkey_cms.Repositories
{
    public interface IUserRepository
    {
        AuthUser? GetUserByUsername(string username);
        IEnumerable<AuthUser> GetAll();
        AuthUser? GetUserByToken(string token);
        AuthUser? GetById(int id);


        IEnumerable<BasicDetail> GetAllStatuses();
        bool SetVerificationToken(int id, string token);
        bool SetPassword(int id, string hashedPassword);
        bool SetVerified(int id, string password);
        bool SetRole(int userId, int roleId, string updatedByUsername);
        bool UpdateCurrentUserInfo(AuthUser user, string firstName, string lastName, string? workPhoneNumber, string? mobileNumber);

        int? CreateUser(string username, CreateUserDto dto);
        bool UpdateUser(int userId, string updatedByUsername, UpdateUserDto dto);
        bool ToggleArchiveStatus(int userId, string updatedByUsername);

        string? SetInvitationTokenForUser(int authUserId);

    }
    public class UserRepository : IUserRepository
    {
        private readonly GoodkeyContext _context;

        public UserRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public IEnumerable<AuthUser> GetAll()
        {
            return _context.AuthUser
                .Include(r => r.Role)
                    .ThenInclude(r => r.AuthGroupRole);
        }

        public AuthUser? GetEmployee(int id)
        {
            return _context.AuthUser
                .Include(x => x.Role)
                .FirstOrDefault(x => x.UserId == id);
        }



        public AuthUser? GetUserByUsername(string username)
        {
            var user = _context.AuthUser
                .Include(c => c.Role)
                    .ThenInclude(c => c.Permission)

                .Include(x => x.Role)

                .Include(x => x.Role)
                    .ThenInclude(x => x.MenuItem)

                .FirstOrDefault(u => u.Username.ToLower().Equals(username.ToLower()));
            return user;
        }

        public AuthUser? GetUserByToken(string token)
        {
            return _context.AuthUser.FirstOrDefault(u => u.VerificationToken == token);
        }

        public AuthUser? GetById(int id)
        {
            return _context.AuthUser
                .Include(r => r.Role)
                .Include(r => r.Department)
                .Include(r => r.Status)
                .Include(r => r.Salutation)
                .Include(r => r.Company)
                .Include(r => r.CreatedBy)
                .Include(r => r.UpdatedBy)
                .FirstOrDefault(u => u.UserId == id);
        }


        public bool SetVerificationToken(int id, string token)
        {
            var user = _context.AuthUser.FirstOrDefault(c => c.UserId == id);
            if (user == null) return false;
            user.VerificationToken = token;
            user.VerificationSentDate = DateTime.Now;
            _context.AuthUser.Update(user);
            _context.SaveChanges();
            return true;
        }

        public bool SetVerified(int id, string password)
        {
            var user = _context.AuthUser.FirstOrDefault(c => c.UserId == id);
            if (user == null) return false;
            user.IsVerified = true;
            user.VerificationToken = null;
            user.VerificationSentDate = null;
            user.IsActive = true;
            user.PasswordHash = password;
            _context.AuthUser.Update(user);
            _context.SaveChanges();
            return true;
        }

        public bool SetPassword(int id, string hashedPassword)
        {
            var user = _context.AuthUser.FirstOrDefault(c => c.UserId == id);
            if (user == null) return false;
            user.PasswordHash = hashedPassword;
            _context.AuthUser.Update(user);
            _context.SaveChanges();
            return true;
        }

        public bool SetRole(int userId, int roleId, string updatedByUsername)
        {

            var user = _context.AuthUser
                .FirstOrDefault(u => u.UserId == userId);
            var role = _context.AuthRole.FirstOrDefault(r => r.RoleId == roleId);
            var updatedBy = _context.AuthUser.FirstOrDefault(u => u.Username.ToLower() == updatedByUsername.ToLower());

            if (user == null || role == null || updatedBy == null)
                return false;


            user.RoleId = roleId;
            user.UpdatedById = updatedBy.UserId;
            // user.UpdatedAt = DateTime.UtcNow;

            _context.SaveChanges();
            return true;
        }

        public bool UpdateCurrentUserInfo(AuthUser user, string firstName, string lastName, string? workPhoneNumber, string? mobileNumber)
        {
            // Update user properties
            user.FirstName = firstName;
            user.LastName = lastName;
            if (!string.IsNullOrEmpty(workPhoneNumber))
            {
                user.WorkPhoneNumber = workPhoneNumber;
            }
            if (!string.IsNullOrEmpty(mobileNumber))
            {
                user.MobileNumber = mobileNumber;
            }

            _context.AuthUser.Update(user);
            _context.SaveChanges();
            return true;

        }
        public IEnumerable<BasicDetail> GetAllStatuses()
        {

            return _context.AuthStatus

                .Select(s => new BasicDetail
                {
                    Id = s.StatusId,
                    Name = s.StatusName
                })
                .ToList();
        }

        public int? CreateUser(string username, CreateUserDto dto)
        {
            var user = _context.AuthUser.FirstOrDefault(u => u.Username == username);
            var Status = _context.AuthStatus.FirstOrDefault(s => s.StatusId == dto.StatusId);
            var Salutation = _context.Salutation.FirstOrDefault(s => s.SalutationId == dto.SalutationId);
            var Department = _context.Department.FirstOrDefault(d => d.DepartmentId == dto.DepartmentId);
            if (user == null || Status == null || Salutation == null || Department == null) return null;

            var WorkEmail = _context.AuthUser.FirstOrDefault(e => e.WorkEmail == dto.WorkEmail);
            var VerificationEmail = _context.AuthUser.FirstOrDefault(e => e.VerificationEmail == dto.VerificationEmail);
            var MobileNumber = _context.AuthUser.FirstOrDefault(e => e.MobileNumber == dto.MobileNumber);
            var WorkPhoneNumber = _context.AuthUser.FirstOrDefault(e => e.WorkPhoneNumber == dto.WorkPhoneNumber);
            if (WorkEmail != null || MobileNumber != null || WorkPhoneNumber != null || VerificationEmail != null) return null;


            var newUser = new AuthUser
            {
                StatusId = dto.StatusId,
                SalutationId = dto.SalutationId,
                FirstName = dto.FirstName ?? "",
                LastName = dto.LastName ?? "",
                MobileNumber = dto.MobileNumber,
                DepartmentId = dto.DepartmentId,
                WorkEmail = dto.WorkEmail,
                WorkPhoneNumber = dto.WorkPhoneNumber,
                VerificationEmail = dto.VerificationEmail,

                // Set username - use WorkEmail or PersonalEmail if available, otherwise generate a unique username
                Username = dto.VerificationEmail ?? $"user_{Guid.NewGuid().ToString().Substring(0, 8)}",

                CreatedById = user.UserId,
                CreatedDate = DateTime.Now, // Using local time instead of UTC to avoid PostgreSQL timestamp issue
                IsArchived = false // default
            };

            _context.AuthUser.Add(newUser);
            _context.SaveChanges();

            return newUser.UserId;
        }

        public bool UpdateUser(int userId, string updatedByUsername, UpdateUserDto dto)
        {
            var user = _context.AuthUser.FirstOrDefault(u => u.UserId == userId);


            if (user == null)
                return false;


            if (dto.StatusId.HasValue)
            {
                var status = _context.AuthStatus.FirstOrDefault(s => s.StatusId == dto.StatusId);
                if (status == null) return false;
                user.StatusId = dto.StatusId;
            }

            if (dto.SalutationId.HasValue)
            {
                var salutation = _context.Salutation.FirstOrDefault(s => s.SalutationId == dto.SalutationId);
                if (salutation == null) return false;
                user.SalutationId = dto.SalutationId;
            }

            if (dto.DepartmentId.HasValue)
            {
                var department = _context.Department.FirstOrDefault(d => d.DepartmentId == dto.DepartmentId);
                if (department == null) return false;
                user.DepartmentId = dto.DepartmentId;
            }


            if (!string.IsNullOrEmpty(dto.FirstName))
                user.FirstName = dto.FirstName;

            if (!string.IsNullOrEmpty(dto.LastName))
                user.LastName = dto.LastName;

            if (dto.MobileNumber != null)
                user.MobileNumber = dto.MobileNumber;

            if (dto.WorkEmail != null)
                user.WorkEmail = dto.WorkEmail;

            if (dto.WorkPhoneNumber != null)
                user.WorkPhoneNumber = dto.WorkPhoneNumber;

            if (dto.VerificationEmail != null)
            {
                var existingUser = _context.AuthUser.FirstOrDefault(u => u.VerificationEmail == dto.VerificationEmail);
                if (existingUser != null && existingUser.UserId != userId) return false;
                user.VerificationEmail = dto.VerificationEmail;
            }



            user.UpdateDate = DateTime.Now;

            _context.AuthUser.Update(user);
            _context.SaveChanges();
            return true;
        }

        public bool ToggleArchiveStatus(int userId, string updatedByUsername)
        {

            var user = _context.AuthUser.FirstOrDefault(u => u.UserId == userId);
            var updatedBy = _context.AuthUser.FirstOrDefault(u => u.Username.ToLower() == updatedByUsername.ToLower());

            if (user == null || updatedBy == null)
                return false;

            // Toggle the archive status
            user.IsArchived = !(user.IsArchived ?? false);

            // If archiving, set the archived metadata
            if (user.IsArchived == true)
            {
                user.ArchivedById = updatedBy.UserId;
                user.ArchivedDate = DateTime.Now;
            }
            else
            {
                // If unarchiving, clear the archived metadata
                user.ArchivedById = null;
                user.ArchivedDate = null;
            }

            // Update the updated metadata
            user.UpdatedById = updatedBy.UserId;
            user.UpdateDate = DateTime.Now;

            _context.AuthUser.Update(user);
            _context.SaveChanges();
            return true;


        }


        public string? SetInvitationTokenForUser(int authUserId)
        {
            var user = _context.AuthUser.FirstOrDefault(u => u.UserId == authUserId);
            if (user == null) return null;


            var verificationToken = Guid.NewGuid().ToString("N");
            var sentDate = DateTime.Now;

            user.VerificationToken = verificationToken;
            user.VerificationSentDate = sentDate;
            _context.SaveChanges();
            return verificationToken;
        }

    }
}
