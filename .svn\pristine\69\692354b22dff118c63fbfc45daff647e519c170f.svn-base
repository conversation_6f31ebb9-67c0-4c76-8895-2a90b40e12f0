﻿using goodkey_cms.DTO;
using goodkey_cms.Infrastructure.Extensions;
using goodkey_cms.Repositories;
using goodkey_common.DTO;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class RolesController : ControllerBase
    {
        private readonly IRoleRepository _repo;
        public RolesController(IRoleRepository repo)
        {
            _repo = repo;
        }

        [HttpGet]
        public GenericRespond<IEnumerable<BasicDetail>> Get()
        {
            return new()
            {
                Data = _repo.GetAll().Select(item =>
                    new BasicDetail()
                    {
                        Id = item.RoleId,
                        Name = item.Name
                    }
                )
            };
        }

        [HttpGet("{id}")]
        public GenericRespond<RoleDetail?> Get(int id)
        {
            var item = _repo.Get(id);
            return new()
            {
                Data = item == null ? null :
                new RoleDetail()
                {
                    Id = item.RoleId,
                    Name = item.Name,
                    Description = item.Description,
                    Level = item.Level ?? 1,
                    RoleGroupId = item.AuthGroupRole?.FirstOrDefault()?.GroupId,
                    RoleGroupName = item.AuthGroupRole?.FirstOrDefault()?.Group?.GroupName,
                    Permission = item.Permission?.Select(x => x.Code)?.ToList(),
                }
            };
        }

        [HttpPost]
        public GenericRespond<int?> Create([FromBody] RoleData data)
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<int?>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            var item = _repo.Create(data.Name, data.Description, data.Level, username);

            if (item == null)
            {
                return new GenericRespond<int?>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Failed to create role. A role with this name may already exist, no role group found for the specified level, or user not found."
                };
            }

            return new GenericRespond<int?>
            {
                Data = item,
                StatusCode = 201,
                Message = "Role created successfully."
            };
        }

        [HttpPatch("{id}")]
        public GenericRespond<bool> Update(int id, [FromBody] RoleData data)
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            var success = _repo.Update(id, data.Name, data.Description, data.Level, username);
            return new GenericRespond<bool>
            {
                Data = success,
                StatusCode = success ? 200 : 400,
                Message = success ? "Role updated successfully." : "Failed to update role. Role name may already exist, no role group found for the specified level, or user not found."
            };
        }
        [HttpPatch("{id}/[action]")]
        public GenericRespond<bool> Permission(int id, [FromBody] PermissionData data)
        {
            var item = _repo.SetPermission(id, data.Permission);
            return new()
            {
                Data = item

            };
        }

        [HttpGet("{id}/[action]")]
        public GenericRespond<IEnumerable<int>> Menu(int id)
        {
            var item = _repo.GetMenu(id);
            return new()
            {
                Data = item
            };
        }

        [HttpPatch("{id}/[action]")]
        public GenericRespond<bool> Menu(int id, [FromBody] IEnumerable<int> data)
        {
            var item = _repo.SetMenu(id, data);
            return new()
            {
                Data = item
            };
        }
    }
}
