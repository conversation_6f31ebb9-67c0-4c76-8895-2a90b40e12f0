using Microsoft.AspNetCore.Http;

namespace goodkey_common.DTO.Show
{
    public class ShowDocumentDto
    {
        public int Id { get; set; }
        public int ShowId { get; set; }
        public int DocumentTypeId { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public string? FileName { get; set; }
        public string? OriginalFileName { get; set; }
        public string? FilePath { get; set; }
        public long? FileSize { get; set; }
        public string? MimeType { get; set; }
        public bool? IsRequired { get; set; }
        public bool? IsPublic { get; set; }
        public int? Version { get; set; }
        public string? Status { get; set; }
        public DateTime? UploadedAt { get; set; }
        public int? UploadedById { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? ArchivedAt { get; set; }
        public int? ArchivedById { get; set; }
        public bool? IsArchived { get; set; }

        // Navigation properties
        public string? ShowName { get; set; }
        public string? ShowCode { get; set; }
        public string? DocumentTypeName { get; set; }
        public string? UploadedByName { get; set; }
        public string? UpdatedByName { get; set; }
        public string? ArchivedByName { get; set; }
    }

    public class ShowDocumentListDto
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public string? OriginalFileName { get; set; }
        public long? FileSize { get; set; }
        public string? MimeType { get; set; }
        public bool? IsRequired { get; set; }
        public bool? IsPublic { get; set; }
        public int? Version { get; set; }
        public string? Status { get; set; }
        public DateTime? UploadedAt { get; set; }
        public string? DocumentTypeName { get; set; }
        public string? UploadedByName { get; set; }
        public bool? IsArchived { get; set; }
    }

    public class CreateShowDocumentDto
    {
        public int ShowId { get; set; }
        public int DocumentTypeId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool? IsRequired { get; set; }
        public bool? IsPublic { get; set; }
        public IFormFile File { get; set; } = null!;
    }

    public class UpdateShowDocumentDto
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public bool? IsRequired { get; set; }
        public bool? IsPublic { get; set; }
        public string? Status { get; set; }
        public IFormFile? File { get; set; } // Optional - only if replacing file
    }

    public class ShowDocumentFilterDto
    {
        public int? DocumentTypeId { get; set; }
        public string? Status { get; set; }
        public bool? IsRequired { get; set; }
        public bool? IsPublic { get; set; }
        public bool? IsArchived { get; set; }
        public DateTime? UploadedAfter { get; set; }
        public DateTime? UploadedBefore { get; set; }
        public string? SearchTerm { get; set; }
    }

    public class DocumentTypeDto
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? ExtensionCode { get; set; }
        public string? Extension { get; set; }
        public bool? IsAvailable { get; set; }
        public bool IsImage { get; set; }
    }

    public class ShowDocumentDownloadDto
    {
        public byte[] FileBytes { get; set; } = Array.Empty<byte>();
        public string ContentType { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
    }

    public class ShowDocumentStatsDto
    {
        public int TotalDocuments { get; set; }
        public int ActiveDocuments { get; set; }
        public int ArchivedDocuments { get; set; }
        public int RequiredDocuments { get; set; }
        public int PublicDocuments { get; set; }
        public long TotalFileSize { get; set; }
        public Dictionary<string, int> DocumentsByType { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> DocumentsByStatus { get; set; } = new Dictionary<string, int>();
    }

    public class ArchiveShowDocumentDto
    {
        public int Id { get; set; }
        public string? ArchiveReason { get; set; }
    }

    public class RestoreShowDocumentDto
    {
        public int Id { get; set; }
        public string? RestoreReason { get; set; }
    }

    public class BulkDocumentActionDto
    {
        public List<int> DocumentIds { get; set; } = new List<int>();
        public string Action { get; set; } = string.Empty; // "archive", "restore", "delete", "changeStatus"
        public string? NewStatus { get; set; }
        public string? Reason { get; set; }
    }
}
