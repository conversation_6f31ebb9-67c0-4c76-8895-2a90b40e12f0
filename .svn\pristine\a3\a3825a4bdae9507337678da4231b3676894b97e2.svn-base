﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
	public interface IContactRepository
	{
		Task AddAsync(Contact contact);
		Task<IEnumerable<Contact>> GetByLocationIdAsync(int locationId);
		Task<Contact> GetByIdAsync(int contactId);
		Task UpdateAsync(int contactId, Contact updated);
	}

	public class ContactRepository : IContactRepository
	{
		private readonly GoodkeyContext _context;

		public ContactRepository(GoodkeyContext context)
		{
			_context = context;
		}

		public async Task AddAsync(Contact contact)
		{
			contact.CreatedAt = DateTime.Now;
			await _context.Contact.AddAsync(contact);
			await _context.SaveChangesAsync();
		}

		public async Task<IEnumerable<Contact>> GetByLocationIdAsync(int locationId)
		{
			return await _context.Contact
				.Where(c => c.LocationId == locationId)
				.ToListAsync();
		}

		public async Task<Contact> GetByIdAsync(int contactId)
		{
			return await _context.Contact.FirstOrDefaultAsync(c => c.ContactId == contactId);
		}

		public async Task UpdateAsync(int contactId, Contact updated)
		{
			var existing = await _context.Contact.FindAsync(contactId);
			if (existing == null) return;

			existing.FirstName = updated.FirstName;
			existing.LastName = updated.LastName;
			existing.Email = updated.Email;
			existing.Telephone = updated.Telephone;
			existing.Ext = updated.Ext;
			existing.Cellphone = updated.Cellphone;
			existing.UpdatedById = updated.UpdatedById;
			existing.UpdatedAt = updated.UpdatedAt ?? DateTime.Now;

			if (updated.IsArchived == true && existing.IsArchived != true)
			{
				existing.IsArchived = true;
				existing.ArchivedAt = updated.ArchivedAt ?? DateTime.Now;
				existing.ArchivedById = updated.ArchivedById;
			}
			else if (updated.IsArchived != true && existing.IsArchived == true)
			{
				existing.IsArchived = false;
				existing.ArchivedAt = null;
				existing.ArchivedById = null;
			}

			await _context.SaveChangesAsync();
		}
	}


}
