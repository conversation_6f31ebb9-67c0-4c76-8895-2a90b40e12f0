using goodkey_cms.Infrastructure.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Authorization.Policy;
using System.Security.Claims;
namespace goodkey_cms.Middlewares
{
    public class AuthorizationMiddleware : IAuthorizationMiddlewareResultHandler
    {
        private readonly AuthorizationMiddlewareResultHandler defaultHandler = new();

        public AuthorizationMiddleware()
        {
        }

        public async Task HandleAsync(
            RequestDelegate next,
            HttpContext context,
            AuthorizationPolicy policy,
            PolicyAuthorizationResult authorizeResult)
        {
            var userId = context.User.Claims?.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
            if (userId == null)
            {
                await defaultHandler.HandleAsync(next, context, policy, authorizeResult);
                return;
            }
            context.SetUsername(userId);
            await defaultHandler.HandleAsync(next, context, policy, authorizeResult);
        }

    }

    public class Show404Requirement : IAuthorizationRequirement { }
}