﻿namespace goodkey_cms.DTO.Group
{
	public class GroupDto
	{
		public int Id { get; set; }
		public string? GroupType { get; set; }
		public string? Name { get; set; }
		public string? Code { get; set; }
		public bool? IsAvailable { get; set; }
		public string? GroupTypeName { get; set; }
	}

	public class GroupCreateDto
	{
		public int? Id { get; set; }
		public string? Name { get; set; }
		public string? Code { get; set; }
		public bool? IsAvailable { get; set; }
		public int? GroupTypeId { get; set; }
	}
}
