﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
	public interface IPropertyRepository
	{
		Task<IEnumerable<Property>> GetAllAsync();
		Task<Property?> GetByIdAsync(int id);
		Task AddAsync(Property property);
		Task UpdateAsync(int id, Property updated);
	}

	public class PropertyRepository : IPropertyRepository
	{
		private readonly GoodkeyContext _context;

		public PropertyRepository(GoodkeyContext context)
		{
			_context = context;
		}

		public async Task<IEnumerable<Property>> GetAllAsync()
		{
			return await _context.Property.ToListAsync();
		}

		public async Task<Property?> GetByIdAsync(int id)
		{
			return await _context.Property.FirstOrDefaultAsync(x => x.Id == id);
		}

		public async Task AddAsync(Property property)
		{
			await _context.Property.AddAsync(property);
			await _context.SaveChangesAsync();
		}

		public async Task UpdateAsync(int id, Property updated)
		{
			var existing = await _context.Property.FindAsync(id);
			if (existing == null) return;

			existing.Name = updated.Name;
			existing.Code = updated.Code;
			existing.Description = updated.Description;
			existing.UpdatedAt = updated.UpdatedAt;
			existing.UpdatedById = updated.UpdatedById;

			await _context.SaveChangesAsync();
		}
	}
}
