﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
	public interface IDocTypeRepository
	{
		Task<IEnumerable<DocType>> GetAllAsync();
	}
	public class DocTypeRepository : IDocTypeRepository
	{
		private readonly GoodkeyContext _context;

		public DocTypeRepository(GoodkeyContext context)
		{
			_context = context;
		}

		public async Task<IEnumerable<DocType>> GetAllAsync()
		{
			return await _context.DocType.ToListAsync();
		}

	}
}
