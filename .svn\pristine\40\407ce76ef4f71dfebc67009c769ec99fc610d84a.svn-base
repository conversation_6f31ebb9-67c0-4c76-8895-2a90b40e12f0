using goodkey_common.DTO;
using goodkey_common.DTO.Show;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;
using goodkey_cms.Infrastructure.Extensions;
using goodkey_cms.Repositories;

namespace goodkey_cms.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class ShowsController : ControllerBase
    {
        private readonly IShowRepository _repo;
        private readonly IUserRepository _userRepo;

        public ShowsController(IShowRepository repo, IUserRepository userRepo)
        {
            _repo = repo;
            _userRepo = userRepo;
        }

        // Get all shows general info
        [HttpGet]
        public GenericRespond<IEnumerable<ShowGeneralInfoDto>> GetAllGeneralInfo()
        {
            var shows = _repo.GetAll();
            var result = shows.Select(s => new ShowGeneralInfoDto
            {
                Id = s.Id,
                Archive = s.Archive,
                Name = s.Name,
                Code = s.Code,
                StartDate = s.StartDate,
                EndDate = s.EndDate,
                DisplayDate = s.DisplayDate,
                OrderDeadlineDate = s.OrderDeadlineDate,
                LateChargePercentage = s.LateChargePercentage,
                Link = s.Link,
                Description = s.Description,
                Display = s.Display,
                CreatedAt = s.CreatedAt,
                CreatedBy = s.CreatedBy,
                LocationId = s.LocationId,
                KioskPrintingQueueDate = s.KioskPrintingQueueDate,
                View = s.View,
                CreatedByUsername = s.CreatedByNavigation?.Username,
                LocationName = s.LocationNavigation?.Name
            });

            return new GenericRespond<IEnumerable<ShowGeneralInfoDto>>
            {
                Data = result,
                StatusCode = 200,
                Message = "Shows general info retrieved successfully"
            };
        }

        // Get show general info by ID
        [HttpGet("{id}/general-info")]
        public GenericRespond<ShowGeneralInfoDto> GetGeneralInfoById(int id)
        {
            var show = _repo.GetById(id);
            if (show == null)
            {
                return new GenericRespond<ShowGeneralInfoDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Show not found"
                };
            }

            var dto = new ShowGeneralInfoDto
            {
                Id = show.Id,
                Archive = show.Archive,
                Name = show.Name,
                Code = show.Code,
                StartDate = show.StartDate,
                EndDate = show.EndDate,
                DisplayDate = show.DisplayDate,
                OrderDeadlineDate = show.OrderDeadlineDate,
                LateChargePercentage = show.LateChargePercentage,
                Link = show.Link,
                Description = show.Description,
                Display = show.Display,
                CreatedAt = show.CreatedAt,
                CreatedBy = show.CreatedBy,
                LocationId = show.LocationId,
                KioskPrintingQueueDate = show.KioskPrintingQueueDate,
                View = show.View,
                CreatedByUsername = show.CreatedByNavigation?.Username,
                LocationName = show.LocationNavigation?.Name
            };

            return new GenericRespond<ShowGeneralInfoDto>
            {
                Data = dto,
                StatusCode = 200,
                Message = "Show general info found"
            };
        }

        // Create new show general info
        [HttpPost("general-info")]
        public GenericRespond<int> CreateGeneralInfo([FromBody] CreateShowGeneralInfoDto dto)
        {
            var username = Request.HttpContext.GetUsername();
            var user = _userRepo.GetUserByUsername(username);

            // Generate next show code (e.g., SH01, SH02, ...)
            var lastShowWithCode = _repo.GetAll()
                .Select(s => s.Code)
                .Where(code => !string.IsNullOrEmpty(code) && code.StartsWith("SH"))
                .Select(code => {
                    if (int.TryParse(code.Substring(2), out int num)) return num;
                    return 0;
                })
                .DefaultIfEmpty(0)
                .Max();
            var nextCode = $"SH{(lastShowWithCode + 1).ToString("D2")}";

            var show = new Shows
            {
                Archive = false,
                Name = dto.Name,
                Code = nextCode,
                StartDate = dto.StartDate?.ToLocalTime(),
                EndDate = dto.EndDate?.ToLocalTime(),
                DisplayDate = dto.DisplayDate?.ToLocalTime(),
                OrderDeadlineDate = dto.OrderDeadlineDate?.ToLocalTime(),
                LateChargePercentage = dto.LateChargePercentage,
                Link = dto.Link,
                Description = dto.Description,
                Display = dto.Display,
                CreatedAt = DateTime.Now,
                CreatedBy = user?.UserId,
                LocationId = dto.LocationId,
                KioskPrintingQueueDate = dto.KioskPrintingQueueDate?.ToLocalTime(),
                View = dto.View
            };

            var created = _repo.Add(show);
            var result = new ShowGeneralInfoDto
            {
                Id = created.Id,
                Archive = created.Archive,
                Name = created.Name,
                Code = created.Code,
                StartDate = created.StartDate,
                EndDate = created.EndDate,
                DisplayDate = created.DisplayDate,
                OrderDeadlineDate = created.OrderDeadlineDate,
                LateChargePercentage = created.LateChargePercentage,
                Link = created.Link,
                Description = created.Description,
                Display = created.Display,
                CreatedAt = created.CreatedAt,
                CreatedBy = created.CreatedBy,
                LocationId = created.LocationId,
                KioskPrintingQueueDate = created.KioskPrintingQueueDate,
                View = created.View,
                CreatedByUsername = user?.Username,
                LocationName = null // Will be populated when retrieved with includes
            };

            return new GenericRespond<int>
            {
                Data = result.Id,
                StatusCode = 201,
                Message = "Show general info created successfully"
            };
        }

        // Update show general info
        [HttpPut("{id}/general-info")]
        public GenericRespond<ShowGeneralInfoDto> UpdateGeneralInfo(int id, [FromBody] UpdateShowGeneralInfoDto dto)
        {
            var show = _repo.GetById(id);
            if (show == null)
            {
                return new GenericRespond<ShowGeneralInfoDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Show not found"
                };
            }

            show.Name = dto.Name;
            show.StartDate = dto.StartDate?.ToLocalTime();
            show.EndDate = dto.EndDate?.ToLocalTime();
            show.DisplayDate = dto.DisplayDate?.ToLocalTime();
            show.OrderDeadlineDate = dto.OrderDeadlineDate?.ToLocalTime();
            show.LateChargePercentage = dto.LateChargePercentage;
            show.Link = dto.Link;
            show.Description = dto.Description;
            show.Display = dto.Display;
            show.LocationId = dto.LocationId;
            show.KioskPrintingQueueDate = dto.KioskPrintingQueueDate?.ToLocalTime();
            show.View = dto.View;

            var updated = _repo.Update(show);
            var result = new ShowGeneralInfoDto
            {
                Id = updated.Id,
                Archive = updated.Archive,
                Name = updated.Name,
                Code = updated.Code,
                StartDate = updated.StartDate,
                EndDate = updated.EndDate,
                DisplayDate = updated.DisplayDate,
                OrderDeadlineDate = updated.OrderDeadlineDate,
                LateChargePercentage = updated.LateChargePercentage,
                Link = updated.Link,
                Description = updated.Description,
                Display = updated.Display,
                CreatedAt = updated.CreatedAt,
                CreatedBy = updated.CreatedBy,
                LocationId = updated.LocationId,
                KioskPrintingQueueDate = updated.KioskPrintingQueueDate,
                View = updated.View,
                CreatedByUsername = show.CreatedByNavigation?.Username,
                LocationName = show.LocationNavigation?.Name
            };

            return new GenericRespond<ShowGeneralInfoDto>
            {
                Data = result,
                StatusCode = 200,
                Message = "Show general info updated successfully"
            };
        }

        // Delete show general info
        [HttpDelete("{id}/general-info")]
        public GenericRespond<bool> DeleteGeneralInfo(int id)
        {
            var result = _repo.Delete(id);
            return new GenericRespond<bool>
            {
                Data = result,
                StatusCode = result ? 200 : 404,
                Message = result ? "Show general info deleted successfully" : "Show not found"
            };
        }

        // Toggle archive status for general info
        [HttpPatch("{id}/toggle-archive")]
        public GenericRespond<bool> ToggleArchiveGeneralInfo(int id)
        {
            var result = _repo.ToggleArchive(id);
            return new GenericRespond<bool>
            {
                Data = result,
                StatusCode = result ? 200 : 404,
                Message = result ? "Show archive status toggled successfully" : "Show not found"
            };
        }
    }
} 