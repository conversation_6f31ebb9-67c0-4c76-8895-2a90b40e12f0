﻿using goodkey_cms.DTO;
using goodkey_cms.DTO.Offering;
using goodkey_common.DTO;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace Goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class GroupTypeController : Controller
	{
		private readonly IGroupTypeRepository _repo;
		public GroupTypeController(IGroupTypeRepository repo)
		{
			_repo = repo;
		}

		[HttpGet]
		public async Task<GenericRespond<IEnumerable<BasicDetail>>> GetBrief()
		{
			var data = await _repo.GetAllAsync();

			var list = data.Select(x => new BasicDetail
			{
				Id = x.Id,
				Name = x.Name
			}).ToList();

			return new GenericRespond<IEnumerable<BasicDetail>>
			{
				Data = list,
				StatusCode = 200,
				Message = "Group Type retrieved successfully"
			};
		}

		[HttpGet("{groupId}/get")]
		public async Task<GenericRespond<GroupTypeWithGroupDto>> GetAllSorted(int groupId)
		{
			// Fetch the GroupType with related entities
			var groupType = await _repo.GetAll(groupId);

			// Handle the case where no GroupType is found
			if (groupType == null)
			{
				return new GenericRespond<GroupTypeWithGroupDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Group type not found."
				};
			}

			// Map the data to DTOs
			var groupWithCategoriesDtos = groupType.Group?.Select(group => new GroupWithCategoriesDto
			{
				GroupId = group.Id,
				GroupName = group.Name,
				Categories = group.Category?.Select(category => new CategoryWithOfferingsDto
				{
					CategoryId = category.Id,
					CategoryName = category.Name,
					Offerings = category.Offering?.Select(offering => new OfferingDto
					{
						Id = offering.Id,
						Name = offering.Name,
						Code = offering.Code,
						SupplierItemNumber = offering.SupplierItemNumber,
						PublicDescription = offering.PublicDescription,
						IsActive = offering.IsActive,
						IsObsolete = offering.IsObsolete
					}).ToList()
				}).ToList()
			}).ToList();

			// Create the final DTO
			var result = new GroupTypeWithGroupDto
			{
				GroupTypeId = groupType.Id,
				GroupTypeName = groupType.Name,
				Group = groupWithCategoriesDtos
			};

			// Return the successful response
			return new GenericRespond<GroupTypeWithGroupDto>
			{
				Data = result,
				StatusCode = 200,
				Message = "Offerings grouped by categories under group type retrieved successfully"
			};
		}
	}
}
