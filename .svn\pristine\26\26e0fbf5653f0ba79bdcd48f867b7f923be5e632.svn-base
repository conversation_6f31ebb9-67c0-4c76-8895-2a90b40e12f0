﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class Company
    {
        public Company()
        {
            AuthUser = new HashSet<AuthUser>();
            Contact = new HashSet<Contact>();
        }

        public int CompanyId { get; set; }
        public string CompanyName { get; set; }
        public string Phone { get; set; }
        public string Address1 { get; set; }
        public string City { get; set; }
        public int? ProvinceId { get; set; }
        public string PostalCode { get; set; }
        public int? CountryId { get; set; }
        public string WebsiteUrl { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? Updatedat { get; set; }
        public int? Updatedbyid { get; set; }
        public string AccountNumber { get; set; }
        public int? CompanyGroupId { get; set; }
        public string Address2 { get; set; }
        public string Note { get; set; }
        public bool? IsArchived { get; set; }
        public string Email { get; set; }

        public virtual CompanyGroup CompanyGroup { get; set; }
        public virtual Countries Country { get; set; }
        public virtual AuthUser CreatedBy { get; set; }
        public virtual Provinces Province { get; set; }
        public virtual AuthUser Updatedby { get; set; }
        public virtual ICollection<AuthUser> AuthUser { get; set; }
        public virtual ICollection<Contact> Contact { get; set; }
    }
}