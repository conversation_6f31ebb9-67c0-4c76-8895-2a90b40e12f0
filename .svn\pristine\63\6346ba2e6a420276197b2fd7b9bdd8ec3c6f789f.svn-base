﻿using goodkey_cms.DTO;
using goodkey_cms.DTO.Category;
using goodkey_cms.DTO.Property;
using goodkey_cms.Services;
using goodkey_common.DTO;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;
using FileType = goodkey_cms.Services.FileType;
using StorageService = goodkey_cms.Services.StorageService;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class CategoryController : ControllerBase
	{
		private readonly ICategoryRepository _repository;
		private readonly AuthService _authService;
		private readonly StorageService _storageService;

		public CategoryController(
			ICategoryRepository repository,
			AuthService authService,
			StorageService storageService)
		{
			_repository = repository;
			_authService = authService;
			_storageService = storageService;
		}

		[HttpGet]
		public async Task<GenericRespond<IEnumerable<CategoryDto>>> GetAll()
		{
			var data = await _repository.GetAllAsync();

			var list = data.Select(x => new CategoryDto
			{
				Id = x.Id,
				Name = x.Name,
				Code = x.Code,
				Group = x.Group?.Name,
				DisplayOrder = x.DisplayOrder,
				ImagePath = x.ImagePath,
				IsSoldByQ = x.IsSoldByQ,
				IsInternalProduct = x.IsInternalProduct,
				IsAvailable = x.IsAvailable ?? false
			}).ToList();

			return new GenericRespond<IEnumerable<CategoryDto>>
			{
				Data = list,
				StatusCode = 200,
				Message = "Categories retrieved successfully"
			};
		}

		[HttpGet("brief")]
		public async Task<GenericRespond<IEnumerable<BasicDetail>>> GetBrief()
		{
			var data = await _repository.GetAllAsync();

			var list = data.Select(x => new BasicDetail
			{
				Id = x.Id,
				Name = x.Code + " " + x.Name
			}).ToList();

			return new GenericRespond<IEnumerable<BasicDetail>>
			{
				Data = list,
				StatusCode = 200,
				Message = "Categories retrieved successfully"
			};
		}

		[HttpGet("{id}")]
		public async Task<GenericRespond<CategoryDetailDto>> GetById(int id)
		{
			var category = await _repository.GetByIdAsync(id);
			if (category == null)
			{
				return new GenericRespond<CategoryDetailDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Category not found"
				};
			}

			return new GenericRespond<CategoryDetailDto>
			{
				Data = new CategoryDetailDto()
				{
					Id = id,
					GroupId = category.GroupId,
					Name = category.Name,
					Code = category.Code,
					DisplayOrder = category.DisplayOrder,
					ImagePath = category.ImagePath,
					IsSoldByQ = category.IsSoldByQ ?? false,
					IsInternalProduct = category.IsInternalProduct ?? false,
					IsAvailable = category.IsAvailable ?? false,
				},
				Message = "Category retrieved successfully"
			};
		}

		[HttpGet("{id}/properties")]
		public async Task<GenericRespond<CategorySelectionDto>> GetProperty(int id)
		{
			var category = await _repository.GetByIdAsync(id);
			if (category == null)
			{
				return new GenericRespond<CategorySelectionDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Category not found"
				};
			}

			return new GenericRespond<CategorySelectionDto>
			{
				Data = new CategorySelectionDto()
				{
					Selection1 = category.CategoryProperty?.FirstOrDefault(p => p.DisplayOrder == 1)?.PropertyId,
					Selection2 = category.CategoryProperty?.FirstOrDefault(p => p.DisplayOrder == 2)?.PropertyId,
				},
				Message = "Category retrieved successfully"
			};
		}

		[HttpPost]
		public async Task<GenericRespond<bool>> Create([FromForm] CategoryCreateDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			string imagePath = null;
			if (dto.Image != null && dto.Image.Length > 0)
			{
				var file = _storageService.UploadFile(
					dto.Image,
					FileType.Image,
					"Category",
					Visibility.Public,
					true,
					"Goodkey_Category");

				imagePath = file.RelativePath;
			}

			var category = new Category
			{
				GroupId = dto.GroupId ?? 0,
				Name = dto.Name,
				Code = GenerateUniqueCode(dto.Name, await GetAllCodes()),
				DisplayOrder = dto.DisplayOrder,
				ImagePath = imagePath,
				IsSoldByQ = dto.IsSoldByQ,
				IsInternalProduct = dto.IsInternalProduct,
				IsAvailable = dto.IsAvailable,
				CreatedAt = DateTime.Now,
				CreatedById = user.UserId
			};

			await _repository.AddAsync(category);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Category created successfully"
			};
		}

		[HttpPost("{id}/properties")]
		public async Task<GenericRespond<bool>> SaveSelections(int id, [FromBody] CategorySelectionDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			var category = await _repository.GetByIdAsync(id);
			if (category == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 404,
					Message = "Category not found"
				};
			}

			category.CategoryProperty?.Clear();

			var newAssociations = new List<CategoryProperty>();

			if (dto.Selection1.HasValue)
			{
				newAssociations.Add(new CategoryProperty
				{
					CategoryId = id,
					PropertyId = dto.Selection1.Value,
					DisplayOrder = 1,
					CreatedById = user.UserId,
					CreatedAt = DateTime.Now
				});
			}

			if (dto.Selection2.HasValue)
			{
				newAssociations.Add(new CategoryProperty
				{
					CategoryId = id,
					PropertyId = dto.Selection2.Value,
					DisplayOrder = 2,
					CreatedById = user.UserId,
					CreatedAt = DateTime.Now
				});
			}

			category.CategoryProperty = newAssociations;
			category.UpdatedAt = DateTime.Now;
			category.UpdatedById = user.UserId;

			await _repository.UpdateAsync(id, category);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Selections saved successfully"
			};
		}

		[HttpPut("{id}")]
		public async Task<GenericRespond<bool>> Update(int id, [FromForm] CategoryUpdateDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			var existingCategory = await _repository.GetByIdAsync(id);
			if (existingCategory == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 404,
					Message = "Category not found"
				};
			}

			string imagePath = existingCategory.ImagePath;
			if (dto.Image != null && dto.Image.Length > 0)
			{
				var file = _storageService.UploadFile(
					dto.Image,
					FileType.Image,
					"Category",
					Visibility.Public,
					true,
					"Goodkey_Category");

				imagePath = file.RelativePath;
			}

			var updatedCategory = new Category
			{
				GroupId = dto.GroupId ?? 0,
				Name = dto.Name,
				Code = existingCategory.Code,
				DisplayOrder = dto.DisplayOrder,
				ImagePath = imagePath,
				IsSoldByQ = dto.IsSoldByQ,
				IsInternalProduct = dto.IsInternalProduct,
				IsAvailable = dto.IsAvailable,
				UpdatedAt = DateTime.Now,
				UpdatedById = user.UserId
			};

			await _repository.UpdateAsync(id, updatedCategory);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Category updated successfully"
			};
		}

		private async Task<HashSet<string>> GetAllCodes()
		{
			var categories = await _repository.GetAllAsync();
			return new HashSet<string>(
				categories
					.Where(c => !string.IsNullOrWhiteSpace(c.Code))
					.Select(c => c.Code.ToUpper()));
		}

		private string GenerateUniqueCode(string name, HashSet<string> existingCodes)
		{
			var baseCode = new string(name
				.Where(char.IsLetterOrDigit)
				.Take(6)
				.ToArray())
				.ToUpper();

			if (baseCode.Length < 3)
			{
				baseCode = baseCode.PadRight(3, 'X');
			}

			string uniqueCode = baseCode;
			int suffix = 1;

			while (existingCodes.Contains(uniqueCode))
			{
				var prefix = baseCode.Length > 4 ? baseCode.Substring(0, 4) : baseCode;
				uniqueCode = $"{prefix}{suffix}";
				suffix++;
			}

			return uniqueCode;
		}

		[HttpGet("{id}/PropertyDetails")]
		public async Task<GenericRespond<IEnumerable<PropertyDetail>>> GetAllDetails(int id)
		{
			var properties = await _repository.GetByCategoryIdAsync(id);

			var result = properties.Select(p => new PropertyDetail
			{
				Id = p.PropertyId,
				Code = p.Property.Code,
				Name = p.Property.Name,
				Description = p.Property.Description,
				Option = p.Property.PropertyOption?.Select(opt => new PropertyOptionDto
				{
					Id = opt.Id,
					Code = opt.Code,
					PropertyId = p.PropertyId,
					Name = opt.Name,
					Description = opt.Description,
				}).ToArray()
			}).ToList();

			return new GenericRespond<IEnumerable<PropertyDetail>>
			{
				Data = result,
				Message = "Properties with options retrieved successfully",
				StatusCode = 200
			};
		}

	}
}