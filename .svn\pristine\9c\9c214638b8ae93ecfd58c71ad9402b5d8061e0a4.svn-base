﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
	public interface IPropertyOptionRepository
	{
		Task<IEnumerable<PropertyOption>> GetAllAsync();
		Task<IEnumerable<PropertyOption>> GetByPropertyIdAsync(int propertyId);
		Task<PropertyOption?> GetByIdAsync(int id);
		Task AddAsync(PropertyOption option);
		Task UpdateAsync(int id, PropertyOption updated);
		Task DeleteAsync(int id);
	}

	public class PropertyOptionRepository : IPropertyOptionRepository
	{
		private readonly GoodkeyContext _context;

		public PropertyOptionRepository(GoodkeyContext context)
		{
			_context = context;
		}

		public async Task<IEnumerable<PropertyOption>> GetAllAsync()
		{
			return await _context.PropertyOption
				.Include(po => po.Property)
				.ToListAsync();
		}

		public async Task<IEnumerable<PropertyOption>> GetByPropertyIdAsync(int propertyId)
		{
			return await _context.PropertyOption
				.Where(po => po.PropertyId == propertyId)
				.OrderBy(po => po.DisplayOrder)
				.ToListAsync();
		}

		public async Task<PropertyOption?> GetByIdAsync(int id)
		{
			return await _context.PropertyOption
				.Include(po => po.Property)
				.FirstOrDefaultAsync(po => po.Id == id);
		}

		public async Task AddAsync(PropertyOption option)
		{
			await _context.PropertyOption.AddAsync(option);
			await _context.SaveChangesAsync();
		}

		public async Task UpdateAsync(int id, PropertyOption updated)
		{
			var existing = await _context.PropertyOption.FindAsync(id);
			if (existing == null) return;

			existing.Name = updated.Name;	
			existing.Code = updated.Code;
			existing.Description = updated.Description;
			existing.DisplayOrder = updated.DisplayOrder;
			existing.UpdatedAt = updated.UpdatedAt;
			existing.UpdatedById = updated.UpdatedById;

			await _context.SaveChangesAsync();
		}

		public async Task DeleteAsync(int id)
		{
			var option = await _context.PropertyOption.FindAsync(id);
			if (option == null) return;

			_context.PropertyOption.Remove(option);
			await _context.SaveChangesAsync();
		}
	}
}
