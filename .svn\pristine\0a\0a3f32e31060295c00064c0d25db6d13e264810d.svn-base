﻿namespace goodkey_cms.DTO.Warehouse
{
    // DTO for GetAll: shows text names instead of IDs
    public class WarehouseSummaryDto
    {
        public int Id { get; set; }
        public string? Code { get; set; }
        public string? WarehouseName { get; set; }
        public string? AddressLine1 { get; set; }
        public string? AddressLine2 { get; set; }
        public string? City { get; set; }
        public string? PostalCode { get; set; }
        public string? ProvinceName { get; set; }
        public string? CountryName { get; set; }
        public string? WarehouseTypeName { get; set; }
        public string? Phone { get; set; }
        public string? ContactPersonName { get; set; }
        public bool? IsActive { get; set; }
    }

    // DTO for Create and Update operations (input DTO)
    public class WarehouseCreateUpdateDto
    {
        public string? Code { get; set; }
        public string? WarehouseName { get; set; }
        public string? AddressLine1 { get; set; }
        public string? AddressLine2 { get; set; }
        public string? Phone { get; set; }
        public string? City { get; set; }
        public string? PostalCode { get; set; }
        public int? ProvinceId { get; set; }
        public int? CountryId { get; set; }
        public int? WarehouseTypeId { get; set; }
        public int? ContactPersonId { get; set; }
        public bool? IsActive { get; set; }
    }

    // DTO for GetById: includes IDs (if needed)
    public class WarehouseDetailDto
    {
        public int WarehouseId { get; set; }
        public string? Code { get; set; }
        public string? WarehouseName { get; set; }
        public string? AddressLine1 { get; set; }
        public string? AddressLine2 { get; set; }
        public string? City { get; set; }
        public string? PostalCode { get; set; }
        public string? Phone { get; set; }
        public int? ProvinceId { get; set; }
        public int? CountryId { get; set; }
        public int? WarehouseTypeId { get; set; }
        public int? ContactPersonId { get; set; }
        public DateTime CreatedAt { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? UpdatedById { get; set; }

        public bool? IsActive { get; set; }
    }

    public class WarehouseActivationDto
    {
        public bool IsActive { get; set; }
    }
}
