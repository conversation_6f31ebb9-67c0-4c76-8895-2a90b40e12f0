using goodkey_cms.Infrastructure.Binders;
using goodkey_cms.Infrastructure.Filters;
using goodkey_cms.Infrastructure.Handlers;
using goodkey_cms.Middlewares;
using goodkey_cms.Services;
using goodkey_common.Context;
using goodkey_common.DTO;
using goodkey_common.Middlewares;
using goodkey_common.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Text;

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddLocalization(options => options.ResourcesPath = "Resources");

builder.Services.AddControllers(options =>
{
    options.ModelBinderProviders.Insert(0, new JsonDictionaryModelBinderProvider());
    options.Filters.Add(new AuthorizeFilter());
});
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen((c) =>
{
    var securityScheme = new OpenApiSecurityScheme
    {
        Name = "JWT Authentication",
        Description = "Enter JWT Bearer token **_only_**",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.Http,
        Scheme = "bearer",
        BearerFormat = "JWT",
        Reference = new OpenApiReference
        {
            Type = ReferenceType.SecurityScheme,
            Id = JwtBearerDefaults.AuthenticationScheme
        }
    };

    c.AddSecurityDefinition(JwtBearerDefaults.AuthenticationScheme, securityScheme);

    // Define the security requirement
    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        { securityScheme, Array.Empty<string>() }
    });
    c.OperationFilter<FileUploadSwaggerFilter>();

});
builder.Services.AddDbContext<GoodkeyContext>(options =>
{
	options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection"));
});

using (var scope = builder.Services.BuildServiceProvider().CreateScope())
{
    var services = scope.ServiceProvider;

    var context = services.GetRequiredService<GoodkeyContext>();
    context.Database.EnsureCreated();
    var roles = context.AuthRole.ToList();
    var permissions = context.AuthPermission.ToList();

    builder.Services.AddAuthorization(options =>
    {
        foreach (var permission in permissions)
        {
            options.AddPolicy(permission.Name, policy => policy.Requirements.Add(new RoleRequirement(permission.Name)));
        }
        foreach (var role in roles)
        {
            options.AddPolicy(role.Name, policy => policy.Requirements.Add(new RoleRequirement(role.Name)));
        }
    });
}

builder.Services.AddRepositories();

builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(builder =>
    {
        builder.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod().
               WithOrigins("https://admingoodkey.malopan.com/")
              .AllowAnyMethod()
              .AllowAnyHeader().
               WithOrigins("http://localhost:3000")
              .AllowAnyMethod()
              .AllowAnyHeader();
        // WithOrigins("https://myaccount.malopan.com")
        //.AllowAnyMethod()
        //.AllowAnyHeader();
    });
});
var jwtSettings = builder.Configuration.GetSection("JwtSettings").Get<JwtSettings>();
builder.Services.Configure<JwtSettings>(builder.Configuration.GetSection("JwtSettings"));
builder.Services.AddSingleton<JwtService>();
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = jwtSettings.Issuer,
            ValidAudience = jwtSettings.Audience,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSettings.Key))
        };
    });

builder.Services.AddHttpContextAccessor();
builder.Services.Configure<MailSettings>(builder.Configuration.GetSection("MailSettings"));
builder.Services.AddTransient<IMailService, MailService>();
builder.Services.AddTransient<goodkey_cms.Services.StorageService>();
builder.Services.AddTransient<goodkey_common.Services.StorageService>();
builder.Services.AddScoped<AuthService>();
builder.Services.AddScoped<
    IAuthorizationMiddlewareResultHandler, goodkey_cms.Middlewares.AuthorizationMiddleware>();
builder.Services.AddScoped<IAuthorizationHandler, RoleAuthorizationHandler>();
var app = builder.Build();
app.UseMiddleware<RespondCleanserMiddleware>();
app.UseMiddleware<ErrorHandlingMiddleware>();
if (app.Environment.IsDevelopment())
{
}
else
{
    app.UseHsts();
}
app.UseSwagger();
app.UseSwaggerUI();

app.UseCors();

app.UseRouting();
app.UseHttpsRedirection();
app.MapSwagger().RequireAuthorization();

app.UseAuthentication();
app.UseAuthorization();

app.UseMiddleware<CultureMiddleware>();
app.MapControllers();
app.MapControllerRoute(
        name: "default",
        pattern: "{controller}/{action}");

app.Run();