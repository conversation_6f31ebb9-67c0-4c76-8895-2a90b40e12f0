using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_cms.Repositories
{
    public interface IScheduleRepository
    {
        IEnumerable<Schedules> GetAll();
        Schedules? Get(int id);
        int? Create(string? code, string name, string? description, string? notes, bool? isActive, string username);
        bool Update(int id, string? code, string? name, string? description, string? notes, bool? isActive, string username);
        bool Delete(int id);
        bool ToggleActive(int id, string username);
    }

    public class ScheduleRepository : IScheduleRepository
    {
        private readonly GoodkeyContext _context;

        public ScheduleRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public IEnumerable<Schedules> GetAll()
        {
            return _context.Schedules
                .Include(s => s.CreatedBy)
                .Include(s => s.UpdatedBy)
                .OrderBy(s => s.Code)
                .ThenBy(s => s.Name);
        }

        public Schedules? Get(int id)
        {
            return _context.Schedules
                .Include(s => s.CreatedBy)
                .Include(s => s.UpdatedBy)
                .FirstOrDefault(s => s.Id == id);
        }

        public int? Create(string? code, string name, string? description, string? notes, bool? isActive, string username)
        {
            // Get user by username
            var user = _context.AuthUser.FirstOrDefault(u => u.Username.ToLower() == username.ToLower());
            if (user == null)
                return null;

            // Generate auto code if not provided or if provided code already exists
            string finalCode = code;
            if (string.IsNullOrEmpty(code) || _context.Schedules.Any(s => s.Code == code))
            {
                finalCode = GenerateScheduleCode();
                if (finalCode == null)
                    return null; // Could not generate unique code
            }

            var schedule = new Schedules()
            {
                Code = finalCode,
                Name = name,
                Description = description,
                Notes = notes,
                IsActive = isActive ?? true,
                CreatedById = user.UserId,
                UpdatedById = user.UserId,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            _context.Schedules.Add(schedule);
            _context.SaveChanges();
            return schedule.Id;
        }

        private string? GenerateScheduleCode()
        {
            // Get all existing schedule codes that start with "SH"
            var existingCodes = _context.Schedules
                .Where(s => s.Code.StartsWith("SH"))
                .Select(s => s.Code)
                .ToList();

            // Find the highest number
            int maxNumber = 0;
            foreach (var code in existingCodes)
            {
                if (code.Length >= 5 && code.Substring(0, 2) == "SH")
                {
                    string numberPart = code.Substring(2);
                    if (int.TryParse(numberPart, out int number))
                    {
                        maxNumber = Math.Max(maxNumber, number);
                    }
                }
            }

            // Generate next code
            int nextNumber = maxNumber + 1;
            string newCode = $"SH{nextNumber:D3}"; // SH001, SH002, etc.

            // Double-check it doesn't exist (safety check)
            if (_context.Schedules.Any(s => s.Code == newCode))
            {
                // If somehow it exists, try a few more numbers
                for (int i = 1; i <= 100; i++)
                {
                    newCode = $"SH{nextNumber + i:D3}";
                    if (!_context.Schedules.Any(s => s.Code == newCode))
                        return newCode;
                }
                return null; // Could not find unique code
            }

            return newCode;
        }

        public bool Update(int id, string? code, string? name, string? description, string? notes, bool? isActive, string username)
        {
            var schedule = _context.Schedules.FirstOrDefault(s => s.Id == id);
            if (schedule == null)
                return false;

            // Check if code is being changed and if new code already exists
            if (!string.IsNullOrEmpty(code) && code != schedule.Code)
            {
                if (_context.Schedules.Any(s => s.Code == code && s.Id != id))
                {
                    return false;
                }
            }

            // Get user by username
            var user = _context.AuthUser.FirstOrDefault(u => u.Username.ToLower() == username.ToLower());
            if (user == null)
                return false;

            // Update only provided fields
            if (!string.IsNullOrEmpty(code))
                schedule.Code = code;
            if (!string.IsNullOrEmpty(name))
                schedule.Name = name;
            if (description != null)
                schedule.Description = description;
            if (notes != null)
                schedule.Notes = notes;
            if (isActive.HasValue)
                schedule.IsActive = isActive.Value;

            schedule.UpdatedById = user.UserId;
            schedule.UpdatedAt = DateTime.Now;

            _context.SaveChanges();
            return true;
        }

        public bool Delete(int id)
        {
            var schedule = _context.Schedules.FirstOrDefault(s => s.Id == id);
            if (schedule == null)
                return false;

            _context.Schedules.Remove(schedule);
            _context.SaveChanges();
            return true;
        }

        public bool ToggleActive(int id, string username)
        {
            var schedule = _context.Schedules.FirstOrDefault(s => s.Id == id);
            if (schedule == null)
                return false;

            // Get user by username
            var user = _context.AuthUser.FirstOrDefault(u => u.Username.ToLower() == username.ToLower());
            if (user == null)
                return false;

            schedule.IsActive = !schedule.IsActive;
            schedule.UpdatedById = user.UserId;
            schedule.UpdatedAt = DateTime.Now;

            _context.SaveChanges();
            return true;
        }
    }
}
