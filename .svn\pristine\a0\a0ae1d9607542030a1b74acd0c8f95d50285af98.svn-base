﻿namespace goodkey_cms.DTO.Contact
{

    public class CreateContactDto
    {
       
        public int? ContactTypeId { get; set; }
        public int? LocationId { get; set; }
        public int? CompanyId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public string? Telephone { get; set; }
        public string? Ext { get; set; }
        public string? Cellphone { get; set; }
        public int? CreatedById { get; set; }
    }

    public class ContactDto : CreateContactDto
    {
        public int? Id { get; set; }
        public DateTime? CreatedAt { get; set; }
        public bool? IsArchived { get; set; }
    }

    public class UpdateContactDto : CreateContactDto
    {
        public bool? IsArchived { get; set; }
    }


}
