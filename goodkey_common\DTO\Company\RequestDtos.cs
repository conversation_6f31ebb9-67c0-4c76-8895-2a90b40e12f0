namespace goodkey_common.DTO.Company
{
    public class CompanyCreateUpdateDto
    {
        public string? Name { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Address1 { get; set; }
        public string? Address2 { get; set; }
        public string? City { get; set; }
        public int? ProvinceId { get; set; }
        public string? PostalCode { get; set; }
        public int? CountryId { get; set; }
        public string? WebsiteUrl { get; set; }
        public string? AccountNumber { get; set; }
        public string? CompanyGroup { get; set; }
        public string? Note { get; set; }
        public bool? IsArchived { get; set; }
    }
}
