namespace goodkey_common.DTO.Ground
{
    public class GroundServiceDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Conditions { get; set; }
        public DateTime? EnteredDate { get; set; }
        public int? EnteredBy { get; set; }
        public bool LocalCartageAppliable { get; set; }
        public int? FlatRate { get; set; }
    }

    public class CreateGroundServiceDto
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string Conditions { get; set; }
        public bool LocalCartageAppliable { get; set; }
        public int? FlatRate { get; set; }
    }

    public class UpdateGroundServiceDto
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string Conditions { get; set; }
        public bool LocalCartageAppliable { get; set; }
        public int? FlatRate { get; set; }
    }
} 