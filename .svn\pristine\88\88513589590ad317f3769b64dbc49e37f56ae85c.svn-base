using goodkey_common.DTO;
using goodkey_common.DTO.Show;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;
using goodkey_cms.Infrastructure.Extensions;
using goodkey_cms.Repositories;

namespace goodkey_cms.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class ShowsController : ControllerBase
    {
        private readonly IShowRepository _repo;
        private readonly IUserRepository _userRepo;
        private readonly IShowScheduleRepository _scheduleRepo;

        public ShowsController(IShowRepository repo, IUserRepository userRepo, IShowScheduleRepository scheduleRepo)
        {
            _repo = repo;
            _userRepo = userRepo;
            _scheduleRepo = scheduleRepo;
        }

        // Get all shows general info
        [HttpGet]
        public GenericRespond<IEnumerable<ShowGeneralInfoDto>> GetAllGeneralInfo()
        {
            var shows = _repo.GetAll();
            var result = shows.Select(s => new ShowGeneralInfoDto
            {
                Id = s.Id,
                Archive = s.Archive,
                Name = s.Name,
                Code = s.Code,
                StartDate = s.StartDate,
                EndDate = s.EndDate,
                DisplayDate = s.DisplayDate,
                OrderDeadlineDate = s.OrderDeadlineDate,
                LateChargePercentage = s.LateChargePercentage,
                Link = s.Link,
                Description = s.Description,
                Display = s.Display,
                CreatedAt = s.CreatedAt,
                CreatedBy = s.CreatedBy,
                LocationId = s.LocationId,
                KioskPrintingQueueDate = s.KioskPrintingQueueDate,
                View = s.View,
                CreatedByUsername = s.CreatedByNavigation?.Username,
                LocationName = s.LocationNavigation?.Name
            });

            return new GenericRespond<IEnumerable<ShowGeneralInfoDto>>
            {
                Data = result,
                StatusCode = 200,
                Message = "Shows general info retrieved successfully"
            };
        }

        // Get show general info by ID
        [HttpGet("{id}/general-info")]
        public GenericRespond<ShowGeneralInfoDto> GetGeneralInfoById(int id)
        {
            var show = _repo.GetById(id);
            if (show == null)
            {
                return new GenericRespond<ShowGeneralInfoDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Show not found"
                };
            }

            var dto = new ShowGeneralInfoDto
            {
                Id = show.Id,
                Archive = show.Archive,
                Name = show.Name,
                Code = show.Code,
                StartDate = show.StartDate,
                EndDate = show.EndDate,
                DisplayDate = show.DisplayDate,
                OrderDeadlineDate = show.OrderDeadlineDate,
                LateChargePercentage = show.LateChargePercentage,
                Link = show.Link,
                Description = show.Description,
                Display = show.Display,
                CreatedAt = show.CreatedAt,
                CreatedBy = show.CreatedBy,
                LocationId = show.LocationId,
                KioskPrintingQueueDate = show.KioskPrintingQueueDate,
                View = show.View,
                CreatedByUsername = show.CreatedByNavigation?.Username,
                LocationName = show.LocationNavigation?.Name
            };

            return new GenericRespond<ShowGeneralInfoDto>
            {
                Data = dto,
                StatusCode = 200,
                Message = "Show general info found"
            };
        }

        // Create new show general info
        [HttpPost("general-info")]
        public GenericRespond<int> CreateGeneralInfo([FromBody] CreateShowGeneralInfoDto dto)
        {
            var username = Request.HttpContext.GetUsername();
            var user = _userRepo.GetUserByUsername(username);

            // Generate next show code (e.g., SH01, SH02, ...)
            var lastShowWithCode = _repo.GetAll()
                .Select(s => s.Code)
                .Where(code => !string.IsNullOrEmpty(code) && code.StartsWith("SH"))
                .Select(code => {
                    if (int.TryParse(code.Substring(2), out int num)) return num;
                    return 0;
                })
                .DefaultIfEmpty(0)
                .Max();
            var nextCode = $"SH{(lastShowWithCode + 1).ToString("D2")}";

            var show = new Shows
            {
                Archive = false,
                Name = dto.Name,
                Code = nextCode,
                StartDate = dto.StartDate?.ToLocalTime(),
                EndDate = dto.EndDate?.ToLocalTime(),
                DisplayDate = dto.DisplayDate?.ToLocalTime(),
                OrderDeadlineDate = dto.OrderDeadlineDate?.ToLocalTime(),
                LateChargePercentage = dto.LateChargePercentage,
                Link = dto.Link,
                Description = dto.Description,
                Display = dto.Display,
                CreatedAt = DateTime.Now,
                CreatedBy = user?.UserId,
                LocationId = dto.LocationId,
                KioskPrintingQueueDate = dto.KioskPrintingQueueDate?.ToLocalTime(),
                View = dto.View
            };

            var created = _repo.Add(show);
            var result = new ShowGeneralInfoDto
            {
                Id = created.Id,
                Archive = created.Archive,
                Name = created.Name,
                Code = created.Code,
                StartDate = created.StartDate,
                EndDate = created.EndDate,
                DisplayDate = created.DisplayDate,
                OrderDeadlineDate = created.OrderDeadlineDate,
                LateChargePercentage = created.LateChargePercentage,
                Link = created.Link,
                Description = created.Description,
                Display = created.Display,
                CreatedAt = created.CreatedAt,
                CreatedBy = created.CreatedBy,
                LocationId = created.LocationId,
                KioskPrintingQueueDate = created.KioskPrintingQueueDate,
                View = created.View,
                CreatedByUsername = user?.Username,
                LocationName = null // Will be populated when retrieved with includes
            };

            return new GenericRespond<int>
            {
                Data = result.Id,
                StatusCode = 201,
                Message = "Show general info created successfully"
            };
        }

        // Update show general info
        [HttpPut("{id}/general-info")]
        public GenericRespond<ShowGeneralInfoDto> UpdateGeneralInfo(int id, [FromBody] UpdateShowGeneralInfoDto dto)
        {
            var show = _repo.GetById(id);
            if (show == null)
            {
                return new GenericRespond<ShowGeneralInfoDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Show not found"
                };
            }

            show.Name = dto.Name;
            show.StartDate = dto.StartDate?.ToLocalTime();
            show.EndDate = dto.EndDate?.ToLocalTime();
            show.DisplayDate = dto.DisplayDate?.ToLocalTime();
            show.OrderDeadlineDate = dto.OrderDeadlineDate?.ToLocalTime();
            show.LateChargePercentage = dto.LateChargePercentage;
            show.Link = dto.Link;
            show.Description = dto.Description;
            show.Display = dto.Display;
            show.LocationId = dto.LocationId;
            show.KioskPrintingQueueDate = dto.KioskPrintingQueueDate?.ToLocalTime();
            show.View = dto.View;

            var updated = _repo.Update(show);
            var result = new ShowGeneralInfoDto
            {
                Id = updated.Id,
                Archive = updated.Archive,
                Name = updated.Name,
                Code = updated.Code,
                StartDate = updated.StartDate,
                EndDate = updated.EndDate,
                DisplayDate = updated.DisplayDate,
                OrderDeadlineDate = updated.OrderDeadlineDate,
                LateChargePercentage = updated.LateChargePercentage,
                Link = updated.Link,
                Description = updated.Description,
                Display = updated.Display,
                CreatedAt = updated.CreatedAt,
                CreatedBy = updated.CreatedBy,
                LocationId = updated.LocationId,
                KioskPrintingQueueDate = updated.KioskPrintingQueueDate,
                View = updated.View,
                CreatedByUsername = show.CreatedByNavigation?.Username,
                LocationName = show.LocationNavigation?.Name
            };

            return new GenericRespond<ShowGeneralInfoDto>
            {
                Data = result,
                StatusCode = 200,
                Message = "Show general info updated successfully"
            };
        }

        // Delete show general info
        [HttpDelete("{id}/general-info")]
        public GenericRespond<bool> DeleteGeneralInfo(int id)
        {
            var result = _repo.Delete(id);
            return new GenericRespond<bool>
            {
                Data = result,
                StatusCode = result ? 200 : 404,
                Message = result ? "Show general info deleted successfully" : "Show not found"
            };
        }

        // Toggle archive status for general info
        [HttpPatch("{id}/toggle-archive")]
        public GenericRespond<bool> ToggleArchiveGeneralInfo(int id)
        {
            var result = _repo.ToggleArchive(id);
            return new GenericRespond<bool>
            {
                Data = result,
                StatusCode = result ? 200 : 404,
                Message = result ? "Show archive status toggled successfully" : "Show not found"
            };
        }

        // Get hall and contact info for a show
        [HttpGet("{id}/hall")]
        public GenericRespond<ShowHallContactDto> GetHallContactInfo(int id)
        {
            var show = _repo.GetById(id);
            if (show == null)
            {
                return new GenericRespond<ShowHallContactDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Show not found"
                };
            }

            var dto = new ShowHallContactDto
            {
                ShowId = show.Id,
                HallId = show.HallId,
                ContactId = show.ContactId,
                HallName = show.Hall?.HallName,
                HallCode = show.Hall?.HallCode,
                ContactName = show.Contact != null ? $"{show.Contact.FirstName} {show.Contact.LastName}" : null,
                ContactEmail = show.Contact?.Email,
                ContactPhone = show.Contact?.Telephone
            };

            return new GenericRespond<ShowHallContactDto>
            {
                Data = dto,
                StatusCode = 200,
                Message = "Show hall and contact info retrieved successfully"
            };
        }

        // Set hall and contact for a show
        [HttpPut("{id}/hall")]
        public GenericRespond<ShowHallContactDto> SetHallContact(int id, [FromBody] SetShowHallContactDto dto)
        {
            var show = _repo.GetById(id);
            if (show == null)
            {
                return new GenericRespond<ShowHallContactDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Show not found"
                };
            }

            show.HallId = dto.HallId;
            show.ContactId = dto.ContactId;

            var updated = _repo.Update(show);
            var result = new ShowHallContactDto
            {
                ShowId = updated.Id,
                HallId = updated.HallId,
                ContactId = updated.ContactId,
                HallName = updated.Hall?.HallName,
                HallCode = updated.Hall?.HallCode,
                ContactName = updated.Contact != null ? $"{updated.Contact.FirstName} {updated.Contact.LastName}" : null,
                ContactEmail = updated.Contact?.Email,
                ContactPhone = updated.Contact?.Telephone
            };

            return new GenericRespond<ShowHallContactDto>
            {
                Data = result,
                StatusCode = 200,
                Message = "Show hall and contact updated successfully"
            };
        }

        // =====================================================
        // Show Schedule Management Endpoints
        // =====================================================

        // Get all show schedules
        [HttpGet("schedules")]
        public GenericRespond<IEnumerable<ShowScheduleDto>> GetAllShowSchedules()
        {
            var schedules = _scheduleRepo.GetAll();
            var result = schedules.Select(s => new ShowScheduleDto
            {
                Id = s.Id,
                ShowScheduleDate = s.ShowScheduleDate,
                TimeStart = s.TimeStart,
                TimeEnd = s.TimeEnd,
                ShowId = s.ShowId,
                ShowScheduleConfirmed = s.ShowScheduleConfirmed,
                ShowScheduleComments = s.ShowScheduleComments,
                CreatedAt = s.CreatedAt,
                CreatedBy = s.CreatedBy,
                ApplyScheduleToServiceForm = s.ApplyScheduleToServiceForm,
                CreatedByUsername = s.CreatedByNavigation?.Username,
                ShowName = s.Show?.Name,
                ShowCode = s.Show?.Code
            });

            return new GenericRespond<IEnumerable<ShowScheduleDto>>
            {
                Data = result,
                StatusCode = 200,
                Message = "Show schedules retrieved successfully"
            };
        }

        // Get show schedules by show ID
        [HttpGet("{showId}/schedules")]
        public GenericRespond<IEnumerable<ShowScheduleDto>> GetShowSchedulesByShowId(int showId)
        {
            var schedules = _scheduleRepo.GetByShowId(showId);
            var result = schedules.Select(s => new ShowScheduleDto
            {
                Id = s.Id,
                ShowScheduleDate = s.ShowScheduleDate,
                TimeStart = s.TimeStart,
                TimeEnd = s.TimeEnd,
                ShowId = s.ShowId,
                ShowScheduleConfirmed = s.ShowScheduleConfirmed,
                ShowScheduleComments = s.ShowScheduleComments,
                CreatedAt = s.CreatedAt,
                CreatedBy = s.CreatedBy,
                ApplyScheduleToServiceForm = s.ApplyScheduleToServiceForm,
                CreatedByUsername = s.CreatedByNavigation?.Username,
                ShowName = s.Show?.Name,
                ShowCode = s.Show?.Code
            });

            return new GenericRespond<IEnumerable<ShowScheduleDto>>
            {
                Data = result,
                StatusCode = 200,
                Message = "Show schedules retrieved successfully"
            };
        }

        // Get show schedule by ID
        [HttpGet("schedules/{id}")]
        public GenericRespond<ShowScheduleDto> GetShowScheduleById(int id)
        {
            var schedule = _scheduleRepo.GetById(id);
            if (schedule == null)
            {
                return new GenericRespond<ShowScheduleDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Show schedule not found"
                };
            }

            var result = new ShowScheduleDto
            {
                Id = schedule.Id,
                ShowScheduleDate = schedule.ShowScheduleDate,
                TimeStart = schedule.TimeStart,
                TimeEnd = schedule.TimeEnd,
                ShowId = schedule.ShowId,
                ShowScheduleConfirmed = schedule.ShowScheduleConfirmed,
                ShowScheduleComments = schedule.ShowScheduleComments,
                CreatedAt = schedule.CreatedAt,
                CreatedBy = schedule.CreatedBy,
                ApplyScheduleToServiceForm = schedule.ApplyScheduleToServiceForm,
                CreatedByUsername = schedule.CreatedByNavigation?.Username,
                ShowName = schedule.Show?.Name,
                ShowCode = schedule.Show?.Code
            };

            return new GenericRespond<ShowScheduleDto>
            {
                Data = result,
                StatusCode = 200,
                Message = "Show schedule found"
            };
        }

        // Create new show schedule
        [HttpPost("{showId}/schedules")]
        public GenericRespond<ShowScheduleDto> CreateShowSchedule(int showId, [FromBody] CreateShowScheduleDto dto)
        {
            var username = Request.HttpContext.GetUsername();
            var user = _userRepo.GetUserByUsername(username);

            var showSchedule = new ShowSchedules
            {
                ShowScheduleDate = dto.ShowScheduleDate,
                TimeStart = dto.TimeStart,
                TimeEnd = dto.TimeEnd,
                ShowId = showId,
                ShowScheduleConfirmed = dto.ShowScheduleConfirmed ?? false,
                ShowScheduleComments = dto.ShowScheduleComments,
                CreatedAt = DateTime.Now,
                CreatedBy = user?.UserId,
                ApplyScheduleToServiceForm = dto.ApplyScheduleToServiceForm ?? false
            };

            var created = _scheduleRepo.Add(showSchedule);
            var result = new ShowScheduleDto
            {
                Id = created.Id,
                ShowScheduleDate = created.ShowScheduleDate,
                TimeStart = created.TimeStart,
                TimeEnd = created.TimeEnd,
                ShowId = created.ShowId,
                ShowScheduleConfirmed = created.ShowScheduleConfirmed,
                ShowScheduleComments = created.ShowScheduleComments,
                CreatedAt = created.CreatedAt,
                CreatedBy = created.CreatedBy,
                ApplyScheduleToServiceForm = created.ApplyScheduleToServiceForm,
                CreatedByUsername = user?.Username,
                ShowName = null, // Will be populated when retrieved with includes
                ShowCode = null
            };

            return new GenericRespond<ShowScheduleDto>
            {
                Data = result,
                StatusCode = 201,
                Message = "Show schedule created successfully"
            };
        }

        // Update show schedule
        [HttpPut("{showId}/schedules/{id}")]
        public GenericRespond<ShowScheduleDto> UpdateShowSchedule(int showId, int id, [FromBody] UpdateShowScheduleDto dto)
        {
            var existingSchedule = _scheduleRepo.GetById(id);
            if (existingSchedule == null)
            {
                return new GenericRespond<ShowScheduleDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Show schedule not found"
                };
            }

            // Verify the schedule belongs to the specified show
            if (existingSchedule.ShowId != showId)
            {
                return new GenericRespond<ShowScheduleDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Show schedule does not belong to the specified show"
                };
            }

            existingSchedule.ShowScheduleDate = dto.ShowScheduleDate;
            existingSchedule.TimeStart = dto.TimeStart;
            existingSchedule.TimeEnd = dto.TimeEnd;
            existingSchedule.ShowId = showId;
            existingSchedule.ShowScheduleConfirmed = dto.ShowScheduleConfirmed;
            existingSchedule.ShowScheduleComments = dto.ShowScheduleComments;
            existingSchedule.ApplyScheduleToServiceForm = dto.ApplyScheduleToServiceForm;

            var updated = _scheduleRepo.Update(existingSchedule);
            var result = new ShowScheduleDto
            {
                Id = updated.Id,
                ShowScheduleDate = updated.ShowScheduleDate,
                TimeStart = updated.TimeStart,
                TimeEnd = updated.TimeEnd,
                ShowId = updated.ShowId,
                ShowScheduleConfirmed = updated.ShowScheduleConfirmed,
                ShowScheduleComments = updated.ShowScheduleComments,
                CreatedAt = updated.CreatedAt,
                CreatedBy = updated.CreatedBy,
                ApplyScheduleToServiceForm = updated.ApplyScheduleToServiceForm,
                CreatedByUsername = existingSchedule.CreatedByNavigation?.Username,
                ShowName = existingSchedule.Show?.Name,
                ShowCode = existingSchedule.Show?.Code
            };

            return new GenericRespond<ShowScheduleDto>
            {
                Data = result,
                StatusCode = 200,
                Message = "Show schedule updated successfully"
            };
        }

        // Delete show schedule
        [HttpDelete("{showId}/schedules/{id}")]
        public GenericRespond<bool> DeleteShowSchedule(int showId, int id)
        {
            var existingSchedule = _scheduleRepo.GetById(id);
            if (existingSchedule == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 404,
                    Message = "Show schedule not found"
                };
            }

            // Verify the schedule belongs to the specified show
            if (existingSchedule.ShowId != showId)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 400,
                    Message = "Show schedule does not belong to the specified show"
                };
            }

            var result = _scheduleRepo.Delete(id);
            return new GenericRespond<bool>
            {
                Data = result,
                StatusCode = result ? 200 : 404,
                Message = result ? "Show schedule deleted successfully" : "Show schedule not found"
            };
        }

        // Toggle show schedule confirmed status
        [HttpPatch("{showId}/schedules/{id}/toggle-confirmed")]
        public GenericRespond<bool> ToggleShowScheduleConfirmed(int showId, int id)
        {
            var existingSchedule = _scheduleRepo.GetById(id);
            if (existingSchedule == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 404,
                    Message = "Show schedule not found"
                };
            }

            // Verify the schedule belongs to the specified show
            if (existingSchedule.ShowId != showId)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 400,
                    Message = "Show schedule does not belong to the specified show"
                };
            }

            var result = _scheduleRepo.ToggleConfirmed(id);
            return new GenericRespond<bool>
            {
                Data = result,
                StatusCode = result ? 200 : 404,
                Message = result ? "Show schedule confirmed status toggled successfully" : "Show schedule not found"
            };
        }

        // Toggle show schedule apply to service form status
        [HttpPatch("{showId}/schedules/{id}/toggle-apply-to-service")]
        public GenericRespond<bool> ToggleShowScheduleApplyToService(int showId, int id)
        {
            var existingSchedule = _scheduleRepo.GetById(id);
            if (existingSchedule == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 404,
                    Message = "Show schedule not found"
                };
            }

            // Verify the schedule belongs to the specified show
            if (existingSchedule.ShowId != showId)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 400,
                    Message = "Show schedule does not belong to the specified show"
                };
            }

            var result = _scheduleRepo.ToggleApplyToServiceForm(id);
            return new GenericRespond<bool>
            {
                Data = result,
                StatusCode = result ? 200 : 404,
                Message = result ? "Show schedule apply to service form status toggled successfully" : "Show schedule not found"
            };
        }
    }
} 