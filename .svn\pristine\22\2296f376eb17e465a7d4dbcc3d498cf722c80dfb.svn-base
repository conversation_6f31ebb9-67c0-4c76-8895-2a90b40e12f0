﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
	public interface ICategoryRepository
	{
		Task<IEnumerable<Category>> GetAllAsync();
		Task<Category> GetByIdAsync(int id);
		Task AddAsync(Category location);
		Task UpdateAsync(int id, Category updated);
		Task<IEnumerable<CategoryProperty>?> GetByCategoryIdAsync(int id);
	}

	public class CategoryRepository : ICategoryRepository
	{
		private readonly GoodkeyContext _context;

		public CategoryRepository(GoodkeyContext context)
		{
			_context = context;
		}

		public async Task<IEnumerable<Category>> GetAllAsync()
		{
			return await _context.Category.Include(x => x.Group)
				.Include(x => x.Offering).ThenInclude(x => x.OfferingProperty).ThenInclude(x => x.PropertyOption1)
				.Include(x => x.Offering).ThenInclude(x => x.OfferingProperty).ThenInclude(x => x.PropertyOption2)
				.ToListAsync();
		}

		public async Task<Category> GetByIdAsync(int id)
		{
			return await _context.Category.Include(x => x.Group).Include(x => x.CategoryProperty).FirstOrDefaultAsync(x => x.Id == id);
		}

		public async Task AddAsync(Category category)
		{
			await _context.Category.AddAsync(category);
			await _context.SaveChangesAsync();
		}

		public async Task UpdateAsync(int id, Category updated)
		{
			var existing = await _context.Category.FindAsync(id);
			if (existing == null) return;

			existing.Name = updated.Name;
			existing.DisplayOrder = updated.DisplayOrder;
			existing.ImagePath = updated.ImagePath;
			existing.IsSoldByQ = updated.IsSoldByQ;
			existing.IsInternalProduct = updated.IsInternalProduct;
			existing.IsAvailable = updated.IsAvailable;
			existing.UpdatedById = updated.UpdatedById;
			existing.UpdatedAt = updated.UpdatedAt;

			await _context.SaveChangesAsync();
		}

		public async Task<IEnumerable<CategoryProperty>?> GetByCategoryIdAsync(int id)
		{
			return await _context.CategoryProperty
				.Include(x => x.Property)
				.ThenInclude(x => x.PropertyOption)
				.Where(x => x.CategoryId == id)
				.ToListAsync();
		}
	}
}
