

namespace goodkey_cms.DTO.Document
{


    #region FileType DTOs

    public class DocumentFileTypeListDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = null!;
        public string ExtensionCode { get; set; } = null!;
        public string Extension { get; set; } = null!;
        public bool IsImage { get; set; }
    }

    public class DocumentFileTypeDetailDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = null!;
        public string ExtensionCode { get; set; } = null!;
        public string Extension { get; set; } = null!;
        public bool IsAvailable { get; set; }
        public bool IsImage { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = null!;
        public DateTime? UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }
    }

    public class CreateDocumentFileTypeDto
    {
        public string Name { get; set; } = null!;
        public string ExtensionCode { get; set; } = null!;
        public string Extension { get; set; } = null!;
        public bool IsImage { get; set; }
    }

    public class UpdateDocumentFileTypeDto
    {
        public string Name { get; set; } = null!;
        public string ExtensionCode { get; set; } = null!;
        public string Extension { get; set; } = null!;
        public bool IsImage { get; set; }
    }

    #endregion



}
