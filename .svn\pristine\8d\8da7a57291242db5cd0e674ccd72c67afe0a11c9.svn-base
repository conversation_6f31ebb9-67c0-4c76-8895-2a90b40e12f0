﻿using goodkey_cms.DTO;
using goodkey_common.DTO;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class UnitController : Controller
	{
		private readonly IUnitRepository _repo;
		public UnitController(IUnitRepository repo)
		{
			_repo = repo;
		}

		[HttpGet]
		public GenericRespond<IEnumerable<BasicDetail>> Get()
		{
			return new GenericRespond<IEnumerable<BasicDetail>>
			{
				Data = _repo.GetAll().Select(x => new BasicDetail()
				{
					Id = x.Id,
					Name = x.Name
				}),
			};
		}
	}
}
