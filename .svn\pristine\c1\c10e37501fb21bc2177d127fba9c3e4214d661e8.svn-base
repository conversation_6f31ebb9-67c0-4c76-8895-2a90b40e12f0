using goodkey_cms.DTO;

namespace goodkey_common.DTO.Contact
{
    public class CreateCompanyContactDto
    {
        public int ContactTypeId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public string? Telephone { get; set; }
        public string? Ext { get; set; }
        public string? Cellphone { get; set; }
        public bool? IsArchived { get; set; }
    }

    public class UpdateCompanyContactDto : CreateCompanyContactDto
    {
    }

    public class CompanyContactDto : BasicDetail
    {
        public int ContactTypeId { get; set; }
        public int? LocationId { get; set; }
        public int? CompanyId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public string? Telephone { get; set; }
        public string? Ext { get; set; }
        public string? Cellphone { get; set; }
        public bool? IsArchived { get; set; }
        public string? Username { get; set; }
        public string? Password { get; set; }
    }

    public class Contact : BasicDetail
    {
        public string? ContactType { get; set; }
        public string? Location { get; set; }
        public string? Company { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public string? Telephone { get; set; }
        public string? Ext { get; set; }
        public string? Cellphone { get; set; }
        public string? Fax { get; set; }
        public bool? IsArchived { get; set; }
        public string? FullName { get; set; }
    }
}
