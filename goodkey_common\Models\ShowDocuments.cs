// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class ShowDocuments
    {
        public int Id { get; set; }
        public int ShowId { get; set; }
        public int DocumentTypeId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string FileName { get; set; }
        public string OriginalFileName { get; set; }
        public string FilePath { get; set; }
        public long? FileSize { get; set; }
        public string MimeType { get; set; }
        public bool? IsRequired { get; set; }
        public bool? IsPublic { get; set; }
        public int? Version { get; set; }
        public string Status { get; set; }
        public DateTime? UploadedAt { get; set; }
        public int? UploadedById { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? ArchivedAt { get; set; }
        public int? ArchivedById { get; set; }
        public bool? IsArchived { get; set; }

        public virtual AuthUser ArchivedBy { get; set; }
        public virtual DocumentFileTypes DocumentType { get; set; }
        public virtual Shows Show { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
        public virtual AuthUser UploadedBy { get; set; }
    }
}