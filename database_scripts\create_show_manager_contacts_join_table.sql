-- This script creates the join table for "Show Manager" contacts.

-- Step 1: Create the join table to link ShowsPromoters and Contacts for Show Managers.
CREATE TABLE "ShowsPromoterManagerContacts" (
    "ShowsPromoterId" INTEGER NOT NULL,
    "ContactId" INTEGER NOT NULL,
    CONSTRAINT "PK_ShowsPromoterManagerContacts" PRIMARY KEY ("ShowsPromoterId", "ContactId"),
    CONSTRAINT "FK_ShowsPromoterManagerContacts_ShowsPromoters" FOREIGN KEY ("ShowsPromoterId") REFERENCES "ShowsPromoters"("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_ShowsPromoterManagerContacts_Contact" FOREIGN KEY ("ContactId") REFERENCES "Contact"("Id") ON DELETE CASCADE
);

-- Note: This table is ready to be populated with the IDs of show managers.
