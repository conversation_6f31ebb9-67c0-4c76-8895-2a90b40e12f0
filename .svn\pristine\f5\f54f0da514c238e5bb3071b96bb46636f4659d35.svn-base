﻿using goodkey_cms.DTO.Property;
using goodkey_cms.Services;
using goodkey_common.DTO;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class PropertyOptionController : ControllerBase
	{
		private readonly IPropertyOptionRepository _repository;
		private readonly AuthService _authService;

		public PropertyOptionController(
			IPropertyOptionRepository repository,
			AuthService authService)
		{
			_repository = repository;
			_authService = authService;
		}

		[HttpGet]
		public async Task<GenericRespond<IEnumerable<PropertyOptionDto>>> GetAll()
		{
			var options = await _repository.GetAllAsync();

			var list = options.Select(x => new PropertyOptionDto
			{
				Id = x.Id,
				PropertyId = x.PropertyId,
				Name = x.Name,
				Code = x.Code,
				Description = x.Description,
				DisplayOrder = x.DisplayOrder
			});

			return new GenericRespond<IEnumerable<PropertyOptionDto>>
			{
				Data = list,
				StatusCode = 200,
				Message = "Property options retrieved successfully"
			};
		}

		[HttpGet("property/{propertyId}")]
		public async Task<GenericRespond<IEnumerable<PropertyOptionDto>>> GetByPropertyId(int propertyId)
		{
			var options = await _repository.GetByPropertyIdAsync(propertyId);

			var list = options.Select(x => new PropertyOptionDto
			{
				Id = x.Id,
				PropertyId = x.PropertyId,
				Code = x.Code,
				Name = x.Name,
				Description = x.Description,
				DisplayOrder = x.DisplayOrder
			});

			return new GenericRespond<IEnumerable<PropertyOptionDto>>
			{
				Data = list,
				StatusCode = 200,
				Message = "Property options retrieved successfully"
			};
		}

		[HttpGet("{id}")]
		public async Task<GenericRespond<PropertyOptionDto>> GetById(int id)
		{
			var option = await _repository.GetByIdAsync(id);
			if (option == null)
			{
				return new GenericRespond<PropertyOptionDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Property option not found"
				};
			}

			return new GenericRespond<PropertyOptionDto>
			{
				Data = new PropertyOptionDto
				{
					Id = option.Id,
					PropertyId = option.PropertyId,
					Name = option.Name,
					Code = option.Code,
					Description = option.Description,
					DisplayOrder = option.DisplayOrder
				},
				StatusCode = 200,
				Message = "Property option retrieved successfully"
			};
		}

		[HttpPost]
		public async Task<GenericRespond<bool>> Create([FromBody] PropertyOptionCreateDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			var option = new PropertyOption
			{
				PropertyId = dto.PropertyId,
				Name = dto.Name,
				Code = await GenerateNextAvailableCode(),
				Description = dto.Description,
				DisplayOrder = dto.DisplayOrder ?? 0,
				CreatedAt = DateTime.Now,
				CreatedById = user.UserId
			};

			await _repository.AddAsync(option);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Property option created successfully"
			};
		}

		[HttpPut("{id}")]
		public async Task<GenericRespond<bool>> Update(int id, [FromBody] PropertyOptionCreateDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			var existing = await _repository.GetByIdAsync(id);
			if (existing == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 404,
					Message = "Property option not found"
				};
			}

			var updated = new PropertyOption
			{
				Name = dto.Name,
				Description = dto.Description,
				Code = existing.Code,
				DisplayOrder = dto.DisplayOrder ?? 0,
				UpdatedAt = DateTime.Now,
				UpdatedById = user.UserId
			};

			await _repository.UpdateAsync(id, updated);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Property option updated successfully"
			};
		}

		[HttpDelete("{id}")]
		public async Task<GenericRespond<bool>> Delete(int id)
		{
			var option = await _repository.GetByIdAsync(id);
			if (option == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 404,
					Message = "Property option not found"
				};
			}

			await _repository.DeleteAsync(id);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Property option deleted successfully"
			};
		}

		private async Task<string> GenerateNextAvailableCode()
		{
			var existingCodes = (await _repository.GetAllAsync())
				.Select(p => p.Code)
				.Where(code => !string.IsNullOrWhiteSpace(code))
				.ToHashSet();

			for (int i = 1; i <= 99; i++)
			{
				string code = i.ToString("D2"); // "01" to "99"
				if (!existingCodes.Contains(code))
				{
					return code;
				}
			}

			throw new InvalidOperationException("All property codes from 01 to 99 are used.");
		}
	}
}
