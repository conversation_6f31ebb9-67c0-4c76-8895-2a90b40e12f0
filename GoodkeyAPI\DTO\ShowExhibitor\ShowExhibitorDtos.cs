namespace goodkey_cms.DTO.ShowExhibitor
{
    public class CreateShowExhibitorDto
    {
        public int ShowId { get; set; }
        public int CompanyId { get; set; }
        public int? ContactId { get; set; } // Optional - if null, will create new contact
        public string[]? BoothNumber { get; set; } // Array of booth numbers

        // Contact information (used when ContactId is null to create new contact)
        public int? ContactTypeId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public string? Telephone { get; set; }
        public string? Ext { get; set; }
        public string? Cellphone { get; set; }

        // Optional: Send email invitation to new contact
        public bool SendEmailInvite { get; set; } = true;
    }

    public class UpdateShowExhibitorDto
    {
        public string[]? BoothNumber { get; set; }
        public bool? IsActive { get; set; }

        // Optional: Update contact information if needed
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public string? Telephone { get; set; }
        public string? Ext { get; set; }
        public string? Cellphone { get; set; }
    }

    public class ShowExhibitorResponseDto
    {
        public int Id { get; set; }
        public int ShowId { get; set; }
        public int CompanyId { get; set; }
        public int? ContactId { get; set; }
        public string[]? BoothNumber { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsArchived { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime? ArchivedAt { get; set; }

        // Navigation properties
        public string? ShowName { get; set; }
        public string? ShowCode { get; set; }
        public string? CompanyName { get; set; }
        public string? ContactFirstName { get; set; }
        public string? ContactLastName { get; set; }
        public string? ContactEmail { get; set; }
        public string? ContactTelephone { get; set; }
        public string? ContactCellphone { get; set; }
        public bool? ContactIsRegistered { get; set; } // True if contact's auth user is verified
        public string? CreatedByName { get; set; }
        public string? UpdatedByName { get; set; }
        public string? ArchivedByName { get; set; }
    }

    public class ShowExhibitorListDto
    {
        public int Id { get; set; }
        public string[]? BoothNumber { get; set; }
        public string? CompanyName { get; set; }
        public string? ContactName { get; set; }
        public string? ContactEmail { get; set; }
        public string? ContactTelephone { get; set; }
        public bool? ContactIsRegistered { get; set; } // True if contact's auth user is verified
        public bool? IsActive { get; set; }
        public bool? IsArchived { get; set; }
        public DateTime? CreatedAt { get; set; }
    }

    public class ArchiveShowExhibitorDto
    {
        public string? ArchiveReason { get; set; }
    }

    public class ShowExhibitorStatsDto
    {
        public int TotalExhibitors { get; set; }
        public int ActiveExhibitors { get; set; }
        public int ArchivedExhibitors { get; set; }
        public int ExhibitorsWithBooths { get; set; }
        public int ExhibitorsWithoutBooths { get; set; }
        public Dictionary<string, int> ExhibitorsByCompany { get; set; } = new();
        public List<string> AllBoothNumbers { get; set; } = new();
    }

    public class ShowExhibitorFilterDto
    {
        public int? CompanyId { get; set; }
        public string? CompanyName { get; set; }
        public string? ContactName { get; set; }
        public string? ContactEmail { get; set; }
        public string? BoothNumber { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsArchived { get; set; }
        public DateTime? CreatedAfter { get; set; }
        public DateTime? CreatedBefore { get; set; }
        public string? SearchTerm { get; set; }
    }
}
