namespace goodkey_cms.DTO.ShowExhibitor
{
    public class CreateShowExhibitorDto
    {
        public int ShowId { get; set; }
        public int CompanyId { get; set; }
        public int? ContactId { get; set; } // Optional - if null, will create new contact
        public string? BoothNumber { get; set; }
        
        // Contact information (used when ContactId is null)
        public string? FullName { get; set; }
        public string? Address1 { get; set; }
        public string? Address2 { get; set; }
        public string? City { get; set; }
        public int? CountryId { get; set; }
        public int? ProvinceId { get; set; }
        public string? PostalCode { get; set; }
        public string? Email { get; set; }
        public string? Telephone { get; set; }
        public string? Fax { get; set; }
        public string? Password { get; set; }
    }

    public class UpdateShowExhibitorDto
    {
        public string? BoothNumber { get; set; }
        public string? FullName { get; set; }
        public string? Address1 { get; set; }
        public string? Address2 { get; set; }
        public string? City { get; set; }
        public int? CountryId { get; set; }
        public int? ProvinceId { get; set; }
        public string? PostalCode { get; set; }
        public string? Email { get; set; }
        public string? Telephone { get; set; }
        public string? Fax { get; set; }
        public string? Password { get; set; }
        public bool? IsActive { get; set; }
    }

    public class ShowExhibitorResponseDto
    {
        public int Id { get; set; }
        public int ShowId { get; set; }
        public int CompanyId { get; set; }
        public int? ContactId { get; set; }
        public string? BoothNumber { get; set; }
        public string? FullName { get; set; }
        public string? Address1 { get; set; }
        public string? Address2 { get; set; }
        public string? City { get; set; }
        public int? CountryId { get; set; }
        public int? ProvinceId { get; set; }
        public string? PostalCode { get; set; }
        public string? Email { get; set; }
        public string? Telephone { get; set; }
        public string? Fax { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsArchived { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime? ArchivedAt { get; set; }

        // Navigation properties
        public string? ShowName { get; set; }
        public string? ShowCode { get; set; }
        public string? CompanyName { get; set; }
        public string? ContactName { get; set; }
        public string? CountryName { get; set; }
        public string? ProvinceName { get; set; }
        public string? CreatedByName { get; set; }
        public string? UpdatedByName { get; set; }
        public string? ArchivedByName { get; set; }
    }

    public class ShowExhibitorListDto
    {
        public int Id { get; set; }
        public string? BoothNumber { get; set; }
        public string? CompanyName { get; set; }
        public string? FullName { get; set; }
        public string? Email { get; set; }
        public string? Telephone { get; set; }
        public string? City { get; set; }
        public string? CountryName { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsArchived { get; set; }
        public DateTime? CreatedAt { get; set; }
    }

    public class ArchiveShowExhibitorDto
    {
        public string? ArchiveReason { get; set; }
    }

    public class ShowExhibitorStatsDto
    {
        public int TotalExhibitors { get; set; }
        public int ActiveExhibitors { get; set; }
        public int ArchivedExhibitors { get; set; }
        public int ExhibitorsWithBooths { get; set; }
        public int ExhibitorsWithoutBooths { get; set; }
        public Dictionary<string, int> ExhibitorsByCountry { get; set; } = new();
        public Dictionary<string, int> ExhibitorsByProvince { get; set; } = new();
    }
}
