﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class ShowDocs
    {
        public int ShowDocId { get; set; }
        public int? DocCategoryId { get; set; }
        public int? ShowId { get; set; }
        public int? LocationId { get; set; }
        public int? HallId { get; set; }
        public DateTime? ValidUntil { get; set; }
        public string FilePath { get; set; }
        public string OriginalFilename { get; set; }
        public string Note { get; set; }
        public int? DocId { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? CreatedById { get; set; }
        public int? UpdatedById { get; set; }

        public virtual AuthUser CreatedBy { get; set; }
        public virtual DocCategory DocCategory { get; set; }
        public virtual ShowLocationHalls Hall { get; set; }
        public virtual ShowLocations Location { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
    }
}