
using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_cms.Repositories
{
    public interface IDocumentRepository
    {

        // Document File Type operations
        IEnumerable<DocumentFileTypes> GetAllFileTypes();
        DocumentFileTypes? GetFileType(int id);
        DocumentFileTypes? GetFileTypeByExtension(string extension);
        int CreateFileType(string name, string extensionCode, string extension, bool isImage, string username);
        bool UpdateFileType(int id, string name, string extensionCode, string extension, bool isImage, string username);
        bool DeleteFileType(int id);




    }

    public class DocumentRepository : IDocumentRepository
    {
        private readonly GoodkeyContext _context;


        public DocumentRepository(GoodkeyContext context)
        {
            _context = context;

        }


        #region Document File Type Methods

        public IEnumerable<DocumentFileTypes> GetAllFileTypes()
        {
            return _context.DocumentFileTypes
                .OrderBy(ft => ft.Name);
        }

        public DocumentFileTypes? GetFileType(int id)
        {
            return _context.DocumentFileTypes
                .FirstOrDefault(ft => ft.Id == id);
        }

        public DocumentFileTypes? GetFileTypeByExtension(string extension)
        {
            if (string.IsNullOrEmpty(extension))
                return null;

            // Remove leading dot if present and convert to lowercase
            extension = extension.TrimStart('.').ToLower();

            return _context.DocumentFileTypes
                .FirstOrDefault(ft => ft.Extension.ToLower() == extension);
        }

        public int CreateFileType(string name, string extensionCode, string extension, bool isImage, string username)
        {
            var user = _context.AuthUser.FirstOrDefault(u => u.Username == username);
            if (user == null) return 0;

            var fileType = new DocumentFileTypes
            {
                Name = name,
                ExtensionCode = extensionCode.ToUpper(),
                Extension = extension.ToLower(),
                IsImage = isImage,
                IsAvailable = true,
                CreatedAt = DateTime.UtcNow,
                CreatedById = user.UserId
            };

            _context.DocumentFileTypes.Add(fileType);
            _context.SaveChanges();

            return fileType.Id;
        }

        public bool UpdateFileType(int id, string name, string extensionCode, string extension, bool isImage, string username)
        {
            var user = _context.AuthUser.FirstOrDefault(u => u.Username == username);
            if (user == null) return false;

            var fileType = _context.DocumentFileTypes
                .FirstOrDefault(ft => ft.Id == id);

            if (fileType == null) return false;

            fileType.Name = name;
            fileType.ExtensionCode = extensionCode.ToUpper();
            fileType.Extension = extension.ToLower();
            fileType.IsImage = isImage;
            fileType.UpdatedAt = DateTime.UtcNow;
            fileType.UpdatedById = user.UserId;

            _context.SaveChanges();
            return true;
        }

        public bool DeleteFileType(int id)
        {
            var fileType = _context.DocumentFileTypes.FirstOrDefault(ft => ft.Id == id);
            if (fileType == null) return false;

            // Check if there are any documents using this file type
            // var hasDocuments = _context.Documents.Any(d => d.FileTypeId == id);
            // if (hasDocuments) return false;

            // Check if there are any category restrictions using this file type
            var hasRestrictions = _context.DocumentFileTypes
                .Where(ft => ft.Id == id)
                // .SelectMany(ft => ft.Categories)
                .Any();

            if (hasRestrictions)
            {
                // Remove all restrictions for this file type
                var restrictions = _context.DocumentFileTypes
                    .Where(ft => ft.Id == id)
                    // .SelectMany(ft => ft.Categories)
                    .ToList();

                // foreach (var category in restrictions)
                // {
                //     fileType.Categories.Remove(category);
                // }
            }

            _context.DocumentFileTypes.Remove(fileType);
            _context.SaveChanges();
            return true;
        }

        #endregion















    }
}
