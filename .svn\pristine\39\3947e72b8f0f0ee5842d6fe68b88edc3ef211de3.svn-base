﻿using goodkey_cms.DTO;
using goodkey_common.DTO;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class DocTypeController : Controller
	{
		private readonly IDocTypeRepository _repo;

		public DocTypeController(IDocTypeRepository repo)
		{
			_repo = repo;
		}

		[HttpGet]
		public GenericRespond<IEnumerable<BasicDetail>> GetAll()
		{
			var docTypes = _repo.GetAllAsync().Result;

			var result = docTypes.Select(item => new BasicDetail
			{
				Id = item.Id,
				Name = item.Code + " " + item.Name,
			});

			return new GenericRespond<IEnumerable<BasicDetail>>
			{
				Data = result
			};
		}

	}
}
