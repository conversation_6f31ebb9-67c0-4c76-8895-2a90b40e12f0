﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
	public interface IShowLocationRepository
	{
		Task<IEnumerable<ShowLocations>> GetAllAsync();
		Task<ShowLocations> GetByIdAsync(int id);
		Task<int> AddAsync(ShowLocations location);
		Task<int> UpdateGeneralInfoAsync(int id, ShowLocations updated);
		Task UpdateAddressInfoAsync(int id, ShowLocations updated);
		Task<ShowLocations> GetAddressInfoAsync(int id);

	}

	public class ShowLocationRepository : IShowLocationRepository
	{
		private readonly GoodkeyContext _context;

		public ShowLocationRepository(GoodkeyContext context)
		{
			_context = context;
		}

		public async Task<IEnumerable<ShowLocations>> GetAllAsync()
		{
			return await _context.ShowLocations.Include(x => x.Country).Include(x => x.Province).ToListAsync();
		}

		public async Task<ShowLocations> GetByIdAsync(int id)
		{
			return await _context.ShowLocations
				.Include(x => x.Country)
				.Include(x => x.Province)
				.FirstOrDefaultAsync(x => x.LocationId == id);
		}

		public async Task<int> AddAsync(ShowLocations location)
		{
			await _context.ShowLocations.AddAsync(location);
			await _context.SaveChangesAsync();
			return location.LocationId;
		}

		public async Task<ShowLocations> GetAddressInfoAsync(int id)
		{
			return await _context.ShowLocations
				.Where(x => x.LocationId == id)
				.Select(x => new ShowLocations
				{
					LocationId = x.LocationId,
					Address1 = x.Address1,
					Address2 = x.Address2,
					City = x.City,
					ProvinceId = x.ProvinceId,
					CountryId = x.CountryId,
					PostalCode = x.PostalCode,
					ShippingAddress1 = x.ShippingAddress1,
					ShippingAddress2 = x.ShippingAddress2,
					ShippingCity = x.ShippingCity,
					ShippingProvinceId = x.ShippingProvinceId,
					ShippingCountryId = x.ShippingCountryId,
					ShippingPostalCode = x.ShippingPostalCode,
					ShippingUsingMain = x.ShippingUsingMain,
					AccessPlan = x.AccessPlan,
					MapLink = x.MapLink
				})
				.FirstOrDefaultAsync();
		}

		public async Task<int> UpdateGeneralInfoAsync(int id, ShowLocations updated)
		{
			var existing = await _context.ShowLocations.FindAsync(id);
			if (existing == null) return 0;

			existing.Name = updated.Name;
			existing.LocationCode = updated.LocationCode;
			existing.Website = updated.Website;
			existing.Email = updated.Email;
			existing.Telephone = updated.Telephone;
			existing.Tollfree = updated.Tollfree;
			existing.Fax = updated.Fax;
			existing.UpdatedById = updated.UpdatedById;
			existing.UpdatedAt = updated.UpdatedAt;
			existing.MapLink = updated.MapLink;
			existing.AccessPlan = updated.AccessPlan;

			if (updated.IsArchived == true && existing.IsArchived != true)
			{
				// Archiving now
				existing.IsArchived = true;
				existing.ArchivedAt = DateTime.Now;
				existing.ArchivedById = updated.ArchivedById;
			}
			else if (updated.IsArchived != true && existing.IsArchived == true)
			{
				// Unarchiving
				existing.IsArchived = false;
				existing.ArchivedAt = null;
				existing.ArchivedById = null;
			}

			await _context.SaveChangesAsync();

			return existing.LocationId;
		}

		public async Task UpdateAddressInfoAsync(int id, ShowLocations updated)
		{
			var existing = await _context.ShowLocations.FindAsync(id);
			if (existing == null) return;

			existing.Address1 = updated.Address1;
			existing.Address2 = updated.Address2;
			existing.PostalCode = updated.PostalCode;
			existing.City = updated.City;
			existing.ProvinceId = updated.ProvinceId;
			existing.CountryId = updated.CountryId;
			existing.ShippingAddress1 = updated.ShippingAddress1;
			existing.ShippingAddress2 = updated.ShippingAddress2;
			existing.ShippingPostalCode = updated.ShippingPostalCode;
			existing.ShippingCity = updated.ShippingCity;
			existing.ShippingProvinceId = updated.ShippingProvinceId;
			existing.ShippingCountryId = updated.ShippingCountryId;
			existing.AccessPlan = updated.AccessPlan;
			existing.MapLink = updated.MapLink;
			existing.UpdatedById = updated.UpdatedById;
			existing.UpdatedAt = updated.UpdatedAt;
			existing.ShippingUsingMain = updated.ShippingUsingMain;

			await _context.SaveChangesAsync();
		}
	}
}
