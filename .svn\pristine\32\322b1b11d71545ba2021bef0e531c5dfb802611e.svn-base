using goodkey_common.Context;
using goodkey_common.Models;

namespace goodkey_common.Repositories
{
    public interface IContactTypeRepository
    {
        IEnumerable<ContactType> GetAll();
        ContactType? Get(int id);
    }

    public class ContactTypeRepository : IContactTypeRepository
    {
        private readonly GoodkeyContext _context;

        public ContactTypeRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public IEnumerable<ContactType> GetAll()
        {
            return _context.ContactType.Where(x => !string.IsNullOrEmpty(x.Name));
        }

        public ContactType? Get(int id)
        {
            return _context.ContactType.FirstOrDefault(x => x.Id == id);
        }
    }
}
