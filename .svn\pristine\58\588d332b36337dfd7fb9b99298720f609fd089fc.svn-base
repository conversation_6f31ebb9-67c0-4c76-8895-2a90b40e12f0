﻿using goodkey_cms.DTO.Offering;
using goodkey_cms.Services;
using goodkey_common.DTO;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;
using FileType = goodkey_cms.Services.FileType;
using StorageService = goodkey_cms.Services.StorageService;
using Visibility = goodkey_cms.Services.Visibility;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class OfferingController : ControllerBase
	{
		private readonly IOfferingRepository _offeringRepository;
		private readonly IOfferingTaxRepository _offeringTaxRepository;
		private readonly AuthService _authService;
		private readonly StorageService _storageService;
		private readonly ICategoryRepository _repo;
		public OfferingController(
			IOfferingRepository offeringRepository,
			IOfferingTaxRepository offeringTaxRepository,
			AuthService authService,
			StorageService storageService,
			ICategoryRepository repo)
		{
			_offeringRepository = offeringRepository;
			_offeringTaxRepository = offeringTaxRepository;
			_authService = authService;
			_storageService = storageService;
			_repo = repo;
		}

		[HttpGet("{id}/Details")]
		public async Task<GenericRespond<OfferingDetailDto>> GetDetailById(int id)
		{
			var x = await _offeringRepository.GetByIdAsync(id);
			if (x == null)
			{
				return new GenericRespond<OfferingDetailDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Category not found"
				};
			}

			return new GenericRespond<OfferingDetailDto>
			{
				Data = new OfferingDetailDto()
				{
					Id = x.Id,
					Name = x.Name,
					Code = x.Code,
					GroupName = x.Category.Group?.Name,
					CategoryName = x.Category.Name,
					CreatedAt = x.CreatedAt,
					UpdatedAt = x.UpdatedAt,
				},
				Message = "Category retrieved successfully"
			};
		}

		// Display in datatable
		[HttpGet("{groupId}/getAll")]
		public async Task<GenericRespond<IEnumerable<OfferingDto>>> GetAll(int groupId)
		{
			var offerings = await _offeringRepository.GetAllAsync(groupId);
			var list = offerings.Select(o => new OfferingDto
			{
				Id = o.Id,
				Name = o.Name,
				Code = o.Code,
				SupplierItemNumber = o.SupplierItemNumber,
				PublicDescription = o.PublicDescription,
				IsActive = o.IsActive,
				IsObsolete = o.IsObsolete
			}).ToList();

			return new GenericRespond<IEnumerable<OfferingDto>>
			{
				Data = list,
				StatusCode = 200,
				Message = "Categories retrieved successfully"
			};
		}

		[HttpGet("{id}")]
		public async Task<GenericRespond<OfferingDetailDto>> GetById(int id)
		{
			var offering = await _offeringRepository.GetByIdAsync(id);
			if (offering == null)
			{
				return new GenericRespond<OfferingDetailDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Item not found"
				};
			}

			var taxes = await _offeringTaxRepository.GetByOfferingIdAsync(id);

			var offeringDetailDto = new OfferingDetailDto
			{
				Id = offering.Id,
				CategoryId = offering.CategoryId,
				GroupTypeId = offering.GroupTypeId,
				Name = offering.Name,
				Code = offering.Code,
				SupplierItemNumber = offering.SupplierItemNumber,
				PublicDescription = offering.PublicDescription,
				InternalDescription = offering.InternalDescription,
				DisplayOrder = offering.DisplayOrder,
				UnitChargedId = offering.UnitChargedId,
				IsUnitTypeEach = offering.IsUnitTypeEach,
				IsAddOn = offering.IsAddOn,
				IsForSmOnly = offering.IsForSmOnly,
				IsInternalOnly = offering.IsInternalOnly,
				ImagePath = offering.Image,
				IsActive = offering.IsActive,
				IsObsolete = offering.IsObsolete,
				CreatedAt = offering.CreatedAt,
				UpdatedAt = offering.UpdatedAt,
				CreatedById = offering.CreatedById,
				UpdatedById = offering.UpdatedById,
				CategoryName = offering.Category?.Name,
				GroupTypeName = offering.GroupType?.Name,
				TaxTypeIds = taxes.Select(t => t.TaxTypeId).ToList()
			};

			return new GenericRespond<OfferingDetailDto>
			{
				Data = offeringDetailDto
			};
		}

		[HttpPost]
		public async Task<GenericRespond<bool>> Create([FromForm] OfferingCreateDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			string imagePath = null;
			if (dto.Image != null && dto.Image.Length > 0)
			{
				var file = _storageService.UploadFile(
					dto.Image,
					FileType.Image,
					"Offering",
					Visibility.Public,
					true,
					"Goodkey_Offering");

				imagePath = file.RelativePath;
			}

			// Step 1: Retrieve GroupType and Category to get their codes
			var groupType = await _offeringRepository.GetGroupTypeByIdAsync(dto.GroupTypeId ?? 0);
			var category = await _offeringRepository.GetCategoryByIdAsync(dto.CategoryId ?? 0);

			if (groupType == null || category == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 400,
					Message = "Invalid GroupTypeId or CategoryId"
				};
			}

			// Step 2: Create offering WITHOUT code first (code depends on Id which is generated after save)
			var offering = new Offering
			{
				CategoryId = dto.CategoryId,
				GroupTypeId = dto.GroupTypeId,
				Name = dto.Name,
				Code = await GenerateUniqueOfferingCode(dto.CategoryId ?? 0),
				SupplierItemNumber = dto.SupplierItemNumber,
				DisplayOrder = dto.DisplayOrder,
				UnitChargedId = dto.UnitChargedId,
				IsUnitTypeEach = dto.IsUnitTypeEach,
				IsAddOn = dto.IsAddOn,
				IsForSmOnly = dto.IsForSmOnly,
				IsInternalOnly = dto.IsInternalOnly,
				Image = imagePath,
				IsActive = dto.IsActive,
				IsObsolete = dto.IsObsolete,
				CreatedAt = DateTime.Now,
				CreatedById = user.UserId
			};

			// Save first to get the generated Id
			await _offeringRepository.AddAsync(offering);

			// Add OfferingTax associations if any
			if (dto.TaxType != null && dto.TaxType.Any())
			{
				var offeringTaxAssociations = dto.TaxType.Select(taxTypeId => new OfferingTax
				{
					OfferingId = offering.Id,
					TaxTypeId = taxTypeId,
					CreatedBy = user.UserId,
					CreatedAt = DateTime.Now
				}).ToList();

				await _offeringTaxRepository.AddRangeAsync(offeringTaxAssociations);
			}

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Offering created successfully"
			};
		}


		[HttpPut("{id}")]
		public async Task<GenericRespond<bool>> Update(int id, [FromForm] OfferingCreateDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			var existingOffering = await _offeringRepository.GetByIdAsync(id);
			if (existingOffering == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 404,
					Message = "Item not found"
				};
			}

			string imagePath = existingOffering.Image;
			if (dto.Image != null && dto.Image.Length > 0)
			{
				var file = _storageService.UploadFile(
					dto.Image,
					FileType.Image,
					"Offering",
					Visibility.Public,
					true,
					"Goodkey_Offering");
				_storageService.DeleteFileSpecific(existingOffering.Image);
				imagePath = file.RelativePath;
			}

			existingOffering.CategoryId = dto.CategoryId;
			existingOffering.GroupTypeId = dto.GroupTypeId;
			existingOffering.Name = dto.Name;
			existingOffering.SupplierItemNumber = dto.SupplierItemNumber;
			existingOffering.DisplayOrder = dto.DisplayOrder;
			existingOffering.UnitChargedId = dto.UnitChargedId;
			existingOffering.IsUnitTypeEach = dto.IsUnitTypeEach;
			existingOffering.IsAddOn = dto.IsAddOn;
			existingOffering.IsForSmOnly = dto.IsForSmOnly;
			existingOffering.IsInternalOnly = dto.IsInternalOnly;
			existingOffering.Image = imagePath;
			existingOffering.IsActive = dto.IsActive;
			existingOffering.IsObsolete = dto.IsObsolete;
			existingOffering.UpdatedAt = DateTime.Now;
			existingOffering.UpdatedById = user.UserId;

			await _offeringRepository.UpdateAsync(id, existingOffering);

			// Update OfferingTax associations
			await _offeringTaxRepository.DeleteByOfferingIdAsync(id);

			if (dto.TaxType != null && dto.TaxType.Any())
			{
				var offeringTaxAssociations = dto.TaxType.Select(taxTypeId => new OfferingTax
				{
					OfferingId = existingOffering.Id,
					TaxTypeId = taxTypeId,
					CreatedBy = user.UserId,
					CreatedAt = DateTime.Now
				}).ToList();

				await _offeringTaxRepository.AddRangeAsync(offeringTaxAssociations);
			}

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Offering saved successfully"
			};
		}

		[HttpPut("{id}/Description")]
		public async Task<GenericRespond<bool>> UpdateDescription(int id, [FromBody] Description dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			var existingOffering = await _offeringRepository.GetByIdAsync(id);
			if (existingOffering == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 404,
					Message = "Item not found"
				};
			}

			existingOffering.PublicDescription = dto.PublicDescription;
			existingOffering.InternalDescription = dto.InternalDescription;
			existingOffering.UpdatedAt = DateTime.Now;
			existingOffering.UpdatedById = user.UserId;

			await _offeringRepository.UpdateAsync(id, existingOffering);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Offering saved successfully"
			};
		}

		private async Task<string> GenerateUniqueOfferingCode(int categoryId)
		{
			// Step 1: Get category to access its code
			var category = await _repo.GetByIdAsync(categoryId); // You must have this in repository
			if (category == null || string.IsNullOrWhiteSpace(category.Code))
			{
				throw new InvalidOperationException("Invalid category or category code not found.");
			}
			string categoryCode = category.Code.Trim().ToUpper();

			// Step 2: Get all offerings under this category
			var offerings = await _offeringRepository.GetAll();
			var categoryOfferings = offerings.Where(o => o.CategoryId == categoryId);

			// Step 3: Extract suffixes from existing offering codes like "P01-01-02" => "02"
			var existingSuffixes = categoryOfferings
				.Select(o =>
				{
					if (!string.IsNullOrEmpty(o.Code) && o.Code.StartsWith(categoryCode + "-"))
					{
						string suffix = o.Code.Substring(categoryCode.Length + 1); // After "P01-01-"
						if (int.TryParse(suffix, out int num) && num >= 1 && num <= 99)
						{
							return num;
						}
					}
					return (int?)null;
				})
				.Where(n => n.HasValue)
				.Select(n => n.Value)
				.ToHashSet();

			// Step 4: Find the first available 2-digit suffix
			for (int i = 1; i <= 99; i++)
			{
				if (!existingSuffixes.Contains(i))
				{
					return $"{categoryCode}-{i.ToString("D2")}";
				}
			}

			throw new InvalidOperationException("No available offering codes in this category. Limit of 99 reached.");
		}



		private string GenerateUniqueOfferingCode(string groupCode, string categoryCode, int productId)
		{
			// Normalize codes to uppercase
			string normalizedGroupCode = groupCode?.Trim().ToUpper() ?? "GRP";
			string normalizedCategoryCode = categoryCode?.Trim().ToUpper() ?? "CAT";

			string code = $"{normalizedGroupCode}-{normalizedCategoryCode}-{productId}";

			return code;
		}

		//[HttpGet("{id}/Property")]
		//public async Task<OfferingPropertyDisplay> GetOfferingProperty(int id)
		//{
		//	var offeringProperties = await _offeringRepository.GetOfferingPropertiesAsync(id);

		//	if (offeringProperties == null || !offeringProperties.Any())
		//	{
		//		return new OfferingPropertyDisplay();
		//	}

		//	// Extract Property1 and Property2 data
		//	var property1Id = offeringProperties.FirstOrDefault(x => x.Property1Id.HasValue)?.Property1Id;
		//	var property2Id = offeringProperties.FirstOrDefault(x => x.Property2Id.HasValue)?.Property2Id;

		//	var property1Options = offeringProperties
		//		.Where(x => x.PropertyOption1Id.HasValue)
		//		.Select(x => x.PropertyOption1Id.Value)
		//		.Distinct()
		//		.ToList();

		//	var property2Options = offeringProperties
		//		.Where(x => x.PropertyOption2Id.HasValue)
		//		.Select(x => x.PropertyOption2Id.Value)
		//		.Distinct()
		//		.ToList();

		//	return new OfferingPropertyDisplay
		//	{
		//		Property1 = property1Id.HasValue ? new PropertyDisplay
		//		{
		//			Id = property1Id,
		//			Options = property1Options
		//		} : null,

		//		Property2 = property2Id.HasValue ? new PropertyDisplay
		//		{
		//			Id = property2Id,
		//			Options = property2Options
		//		} : null
		//	};
		//}

		[HttpGet("{id}/PropertyDetails")] // OfferingID
		public async Task<GenericRespond<IEnumerable<OfferingPropertyDetailDto>>> GetOfferingPropertyDetails(int id)
		{
			var offeringProperties = await _offeringRepository.GetOfferingPropertiesAsync(id);

			return new GenericRespond<IEnumerable<OfferingPropertyDetailDto>>
			{
				Data = offeringProperties.Select(o => new OfferingPropertyDetailDto
				{
					Id = o.Id,
					Code = o.Code,
					Property1 = o.Property1?.Name,
					Property2 = o.Property2?.Name,
					PropertyOption1 = o.PropertyOption1?.Name,
					PropertyOption2 = o.PropertyOption2?.Name,
					Image = o.Image,
					IsActive = o.IsActive,
				}),
			};

		}

		[HttpGet("{id}/Property")] // Offering Id
		public async Task<GenericRespond<OfferingPropertyRequest>> GetOfferingProperty(int id)
		{
			var offeringProperties = await _offeringRepository.GetOfferingPropertiesAsync(id);
			offeringProperties = offeringProperties.Where(o => o.IsActive == true);

			// Group by Property1Id and Property2Id to ensure options are correctly mapped
			var property1Group = offeringProperties
				.Where(x => x.Property1Id.HasValue)
				.GroupBy(x => x.Property1Id.Value)
				.ToDictionary(g => g.Key, g => g.Select(x => x.PropertyOption1Id.Value).Distinct().ToList());

			var property2Group = offeringProperties
				.Where(x => x.Property2Id.HasValue)
				.GroupBy(x => x.Property2Id.Value)
				.ToDictionary(g => g.Key, g => g.Select(x => x.PropertyOption2Id.Value).Distinct().ToList());

			// Create the OfferingPropertyRequest
			var response = new OfferingPropertyRequest
			{
				Property = new List<OPropertyDto>()
			};

			// Add Property1 data if it exists
			if (property1Group.Any())
			{
				foreach (var property in property1Group)
				{
					response.Property.Add(new OPropertyDto
					{
						Id = property.Key,
						Options = property.Value,
					});
				}
			}

			// Add Property2 data if it exists
			if (property2Group.Any())
			{
				foreach (var property in property2Group)
				{
					response.Property.Add(new OPropertyDto
					{
						Id = property.Key,
						Options = property.Value
					});
				}
			}

			return new GenericRespond<OfferingPropertyRequest>
			{
				Data = response,
				StatusCode = 200,
				Message = "Property retrieved successfully"
			}; ;
		}


		[HttpPost("{id}/Property")]// Offering Id
		public async Task<GenericRespond<bool>> AddPropertyLink(int id, [FromBody] OfferingPropertyRequest dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			//Get offeringCode from offeringId
			var offering = await _offeringRepository.GetByIdAsync(id);
			if (offering == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 404,
					Message = "Offering not found"
				};
			}
			string offeringCode = offering.Code;

			var newLinks = new List<OfferingProperty>();

			// Loop through each property group in the request
			if (dto.Property?.Count == 1)
			{
				// Case when there's only one property in the list
				var propertyRequest = dto.Property[0]; // Get the single property
				var property1Id = propertyRequest.Id;
				var property1Options = propertyRequest.Options ?? new List<int>(); // Options can be null, so we use an empty list if null

				// Create multiple OfferingProperty entries for the single property
				foreach (var optionId in property1Options)
				{
					newLinks.Add(new OfferingProperty
					{
						OfferingId = id,  // Assuming OfferingId comes from the route or request
						Property1Id = property1Id,
						PropertyOption1Id = optionId, // Convert string option to integer
						IsActive = true,
						CreatedAt = DateTime.Now, // Use UTC for consistency
						CreatedById = user.UserId
					});
				}
			}
			else if (dto.Property?.Count == 2)
			{
				// Case when there are exactly two properties
				var property1Request = dto.Property[0]; // First property
				var property2Request = dto.Property[1]; // Second property

				var property1Id = property1Request.Id;
				var property2Id = property2Request.Id;
				var property1Options = property1Request.Options ?? new List<int>(); // Options for Property1
				var property2Options = property2Request.Options ?? new List<int>(); // Options for Property2

				// Create combinations between Property1 options and Property2 options
				var combinations = from option1 in property1Options
								   from option2 in property2Options
								   select new { Option1 = option1, Option2 = option2 };

				foreach (var combo in combinations)
				{
					newLinks.Add(new OfferingProperty
					{
						OfferingId = id,
						Property1Id = property1Id,
						PropertyOption1Id = combo.Option1,
						Property2Id = property2Id,
						PropertyOption2Id = combo.Option2,
						IsActive = true,
						CreatedAt = DateTime.Now,
						CreatedById = user.UserId
					});
				}
			}

			if (newLinks.Any())
			{
				var savedLinks = await _offeringRepository.AddOfferingPropertiesAsync(newLinks);

				foreach (var link in savedLinks)
				{
					link.Code = $"{offeringCode}-{link.Id}";
				}

				await _offeringRepository.UpdateOfferingPropertiesAsync(savedLinks);
			}

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Property links added successfully"
			};
		}

		[HttpPut("{id}/Property")]
		public async Task<GenericRespond<bool>> UpdatePropertyLink(int id, [FromBody] OfferingPropertyRequest dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			//Get offeringCode from offeringId
			var offering = await _offeringRepository.GetByIdAsync(id);
			if (offering == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 404,
					Message = "Offering not found"
				};
			}
			string offeringCode = offering.Code;

			// Step 1: Fetch all existing property links for the offering
			var existingLinks = await _offeringRepository.GetOfferingPropertiesAsync(id);

			// Step 2: Mark all existing links as inactive
			foreach (var existing in existingLinks)
			{
				existing.IsActive = false;
				existing.UpdatedAt = DateTime.Now;
				existing.UpdatedById = user.UserId;
			}

			var newLinks = new List<OfferingProperty>();

			if (dto.Property?.Count == 1)
			{
				var property = dto.Property[0];
				var property1Id = property.Id;
				var options = property.Options ?? new List<int>();

				foreach (var o in options)
				{
					var match = existingLinks.FirstOrDefault(e =>
						(e.PropertyOption1Id == o &&
						e.PropertyOption2Id == null) ||
						(e.PropertyOption2Id == o &&
						e.PropertyOption1Id == null));

					if (match != null)
					{
						// Reactivate
						match.IsActive = true;
						match.UpdatedAt = DateTime.Now;
						match.UpdatedById = user.UserId;
					}
					else
					{
						// New link
						newLinks.Add(new OfferingProperty
						{
							OfferingId = id,
							Property1Id = property1Id,
							PropertyOption1Id = o,
							IsActive = true,
							CreatedAt = DateTime.Now,
							CreatedById = user.UserId
						});
					}
				}
			}
			else if (dto.Property?.Count == 2)
			{
				var property1 = dto.Property[0];
				var property2 = dto.Property[1];

				var options1 = property1.Options ?? new List<int>();
				var options2 = property2.Options ?? new List<int>();

				foreach (var option1 in options1)
				{
					foreach (var option2 in options2)
					{
						var match = existingLinks.FirstOrDefault(e =>
							(e.PropertyOption1Id == option1 && e.PropertyOption2Id == option2) ||
							(e.PropertyOption1Id == option2 && e.PropertyOption2Id == option1));

						if (match != null)
						{
							// Reactivate
							match.IsActive = true;
							match.UpdatedAt = DateTime.Now;
							match.UpdatedById = user.UserId;
						}
						else
						{
							// New link
							newLinks.Add(new OfferingProperty
							{
								OfferingId = id,
								Property1Id = property1.Id,
								PropertyOption1Id = option1,
								Property2Id = property2.Id,
								PropertyOption2Id = option2,
								IsActive = true,
								CreatedAt = DateTime.Now,
								CreatedById = user.UserId
							});
						}
					}
				}
			}

			// Step 3: Persist changes
			if (existingLinks.Any())
			{
				await _offeringRepository.UpdateOfferingPropertiesAsync(existingLinks); // mark IsActive false or true
			}

			if (newLinks.Any())
			{
				var savedLinks = await _offeringRepository.AddOfferingPropertiesAsync(newLinks);

				foreach (var link in savedLinks)
				{
					link.Code = $"{offeringCode}-{link.Id}";
				}

				await _offeringRepository.UpdateOfferingPropertiesAsync(savedLinks);
			}

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Property links updated successfully"
			};
		}



		[HttpGet("OfferingProperty/{id}")] // OfferingProperty Id
		public async Task<GenericRespond<OfferingPropertyCreateDto>> GetOfferingPropertyById(int id)
		{
			var offeringProperty = await _offeringRepository.GetOfferingPropertyAsync(id);
			if (offeringProperty == null)
			{
				return new GenericRespond<OfferingPropertyCreateDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Item not found"
				};
			}

			var offeringDetailDto = new OfferingPropertyCreateDto
			{
				Id = offeringProperty.Id,
				Name = offeringProperty.PropertyOption1?.Name + (offeringProperty.PropertyOption2 != null ? $", {offeringProperty.PropertyOption2.Name}" : ""),
				Code = offeringProperty.Code,
				SupplierItemNumber = offeringProperty.SupplierItemNumber,
				IsForSmOnly = offeringProperty.IsForSmOnly,
				IsInternalOnly = offeringProperty.IsInternalOnly,
				ImagePath = offeringProperty.Image,
				IsActive = offeringProperty.IsActive,
			};

			return new GenericRespond<OfferingPropertyCreateDto>
			{
				Data = offeringDetailDto
			};
		}


		[HttpPut("OfferingProperty/{id}")]
		public async Task<GenericRespond<bool>> UpdateOfferingProperty(int id, [FromForm] OfferingPropertyCreateDto dto) // OfferingProperty Id
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			var existingOffering = await _offeringRepository.GetOfferingPropertyAsync(id);
			if (existingOffering == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 404,
					Message = "Item not found"
				};
			}

			string imagePath = existingOffering.Image;
			if (dto.Image != null && dto.Image.Length > 0)
			{
				var file = _storageService.UploadFile(
					dto.Image,
					FileType.Image,
					"Offering",
					Visibility.Public,
					true,
					"Goodkey_Offering");
				if (existingOffering.Image != null) _storageService.DeleteFileSpecific(existingOffering.Image);
				imagePath = file.RelativePath;
			}

			existingOffering.SupplierItemNumber = dto.SupplierItemNumber;
			existingOffering.IsForSmOnly = dto.IsForSmOnly;
			existingOffering.IsInternalOnly = dto.IsInternalOnly;
			existingOffering.Image = imagePath;
			existingOffering.IsActive = dto.IsActive;
			existingOffering.UpdatedAt = DateTime.Now;
			existingOffering.UpdatedById = user.UserId;

			await _offeringRepository.UpdateOfferingPropertyAsync(id, existingOffering);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Offering saved successfully"
			};
		}



		//[HttpPost("AddPropertyLink")]
		//public async Task<GenericRespond<bool>> AddPropertyLink([FromBody] OfferingPropertyCreateDto[] dto)
		//{
		//	var user = _authService.Current;
		//	if (user == null)
		//	{
		//		return new GenericRespond<bool>
		//		{
		//			Data = false,
		//			StatusCode = 401,
		//			Message = "Unauthorized"
		//		};
		//	}

		//	var propertyLinks = dto.Select(item => new OfferingProperty
		//	{
		//		ProductId = item.ProductId,
		//		Code = item.Code,
		//		Property1Id = item.Property1Id,
		//		PropertyOption1Id = item.PropertyOption1Id,
		//		Property2Id = item.Property2Id,
		//		PropertyOption2Id = item.PropertyOption2Id,
		//		IsActive = item.IsActive,
		//		CreatedById = user.UserId,
		//		CreatedAt = DateTime.Now,
		//	}).ToList();

		//	await _offeringRepository.AddOfferingPropertiesAsync(propertyLinks);

		//	return new GenericRespond<bool>
		//	{
		//		Data = true,
		//		Message = "Property links added successfully"
		//	};
		//}


		[HttpPost("{offeringId}/AddonSelection")]
		public async Task<GenericRespond<bool>> SaveAddons(int offeringId, [FromBody] AddonSelectionDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			// Ensure the offering exists
			var offering = await _offeringRepository.GetByIdAsync(offeringId);
			if (offering == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 404,
					Message = "Offering not found"
				};
			}

			// Step 1: Delete previous add-ons related to this offering (if any)
			await _offeringRepository.DeleteAddonsByOfferingIdAsync(offeringId);

			List<OfferingAddOn> offeringAddons = new List<OfferingAddOn>();
			// Step 2: Add the selected add-ons for the offering
			if (dto.SelectedOfferings != null && dto.SelectedOfferings.Any())
			{
				offeringAddons.AddRange(dto.SelectedOfferings.Select(addonId => new OfferingAddOn
				{
					OfferingId = offeringId,
					AddOnOfferingId = addonId,
				}).ToList());
			}

			// Step 3: Add the selected options (if applicable) for the offering
			if (dto.SelectedOptions != null && dto.SelectedOptions.Any())
			{
				offeringAddons.AddRange(dto.SelectedOptions.Select(optionId => new OfferingAddOn
				{
					OfferingId = offeringId,
					AddOnOfferingPropertyId = optionId,
				}).ToList());
			}

			if (offeringAddons.Any())
			{
				await _offeringRepository.AddAddonsAsync(offeringAddons);
			}

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Add-ons and options saved successfully"
			};
		}

		[HttpGet("{offeringId}/AddonSelection")]
		public async Task<GenericRespond<AddonSelectionDto>> GetAddons(int offeringId)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<AddonSelectionDto>
				{
					Data = null,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			// Ensure the offering exists
			var offering = await _offeringRepository.GetByIdAsync(offeringId);
			if (offering == null)
			{
				return new GenericRespond<AddonSelectionDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Offering not found"
				};
			}

			// Fetch the add-ons and options related to the offering
			var offeringAddons = await _offeringRepository.GetAddonsByOfferingIdAsync(offeringId);

			// Prepare lists for selected offerings and selected options
			var selectedOfferings = new List<int>();
			var selectedOptions = new List<int>();

			// Map the add-ons to selected offerings and options
			foreach (var addon in offeringAddons)
			{
				if (addon.AddOnOfferingId.HasValue)
				{
					selectedOfferings.Add(addon.AddOnOfferingId.Value);
				}

				if (addon.AddOnOfferingPropertyId.HasValue)
				{
					selectedOptions.Add(addon.AddOnOfferingPropertyId.Value);
				}
			}

			// Create the AddonSelectionDto
			var dtoResult = new AddonSelectionDto
			{
				SelectedOfferings = selectedOfferings,
				SelectedOptions = selectedOptions
			};

			return new GenericRespond<AddonSelectionDto>
			{
				Data = dtoResult,
				Message = "Add-ons retrieved successfully"
			};
		}


	}
}
