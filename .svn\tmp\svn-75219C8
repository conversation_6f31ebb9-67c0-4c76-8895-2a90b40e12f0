using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
    public interface ICompanyRepository
    {
        IEnumerable<Company> GetAll();
        IEnumerable<Company> GetByCompanyGroup(string companyGroupName);
        Company? Get(int id);
        bool Add(string name, string phone, string email, string address1, string address2,
                 string postalCode, string city, int provinceId, int countryId, string websiteUrl,
                 string accountNumber, string companyGroup, string note, bool isArchived, string username);
        bool Update(int id, string name, string phone, string email, string address1, string address2,
                   string postalCode, string city, int provinceId, int countryId, string websiteUrl,
                   string accountNumber, string companyGroup, string note, bool isArchived, string username);

        IEnumerable<Contact> GetAllContacts();
        IEnumerable<Contact> GetContactsByCompanyId(int companyId);
        Contact? GetContact(int id);
        bool AddContact(int contactTypeId, int? locationId, int? companyId, string firstName, string lastName,
                       string email, string telephone, string ext, string cellphone, string fax, bool isArchived, string username);
        bool UpdateContact(int id, int contactTypeId, int? locationId, int? companyId, string firstName, string lastName,
                          string email, string telephone, string ext, string cellphone, string fax, bool isArchived, string username);
        bool DeleteContact(int id, string username);
    }

    public class CompanyRepository : ICompanyRepository
    {
        private readonly GoodkeyContext _context;

        public CompanyRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public bool Add(string name, string phone, string email, string address1, string address2,
                       string postalCode, string city, int provinceId, int countryId, string websiteUrl,
                       string accountNumber, string companyGroup, string note, bool isArchived, string username)
        {
            AuthUser? user = _context.AuthUser.FirstOrDefault(x => x.Username == username);

            if (user == null)
            {
                return false;
            }

            var existingCompany = _context.Company.FirstOrDefault(d => d.CompanyName == name);
            if (existingCompany != null)
            {
                return false;
            }

            // Find company group by name
            int? companyGroupId = null;
            if (!string.IsNullOrWhiteSpace(companyGroup))
            {
                var companyGroupEntity = _context.CompanyGroup.FirstOrDefault(cg => cg.Name.ToLower() == companyGroup.ToLower());
                companyGroupId = companyGroupEntity?.CompanyGroupId;
            }

            var company = new Company()
            {
                CompanyName = name,
                Phone = phone,
                Email = email,
                Address1 = address1,
                Address2 = address2,
                City = city,
                ProvinceId = provinceId,
                CountryId = countryId,
                PostalCode = postalCode,
                WebsiteUrl = websiteUrl,
                AccountNumber = accountNumber,
                CompanyGroupId = companyGroupId,
                Note = note,
                IsArchived = isArchived,
                CreatedById = user.UserId,
                CreatedAt = DateTime.Now,
            };

            _context.Company.Add(company);
            _context.SaveChanges();
            return true;
        }

        public Company? Get(int id)
        {
            return _context.Company
                .Include(x => x.Province)
                .Include(x => x.Country)
                .Include(x => x.CompanyGroup)
                .FirstOrDefault(x => x.CompanyId == id);
        }

        public IEnumerable<Company> GetAll()
        {
            return _context.Company
                .Include(x => x.Province)
                .Include(x => x.Country)
                .Include(x => x.CompanyGroup);
        }

        public IEnumerable<Company> GetByCompanyGroup(string companyGroupName)
        {
            return _context.Company
                .Include(x => x.Province)
                .Include(x => x.Country)
                .Include(x => x.CompanyGroup)
                .Where(x => x.CompanyGroup != null && x.CompanyGroup.Name.ToLower() == companyGroupName.ToLower());
        }

        public bool Update(int id, string name, string phone, string email, string address1, string address2,
                          string postalCode, string city, int provinceId, int countryId, string websiteUrl,
                          string accountNumber, string companyGroup, string note, bool isArchived, string username)
        {
            AuthUser? user = _context.AuthUser.FirstOrDefault(x => x.Username == username);
            var company = _context.Company.FirstOrDefault(x => x.CompanyId == id);

            if (company == null || user == null)
            {
                return false;
            }

            // Find company group by name
            int? companyGroupId = null;
            if (!string.IsNullOrWhiteSpace(companyGroup))
            {
                var companyGroupEntity = _context.CompanyGroup.FirstOrDefault(cg => cg.Name.ToLower() == companyGroup.ToLower());
                companyGroupId = companyGroupEntity?.CompanyGroupId;
            }

            company.CompanyName = name;
            company.Phone = phone;
            company.Email = email;
            company.Address1 = address1;
            company.Address2 = address2;
            company.City = city;
            company.PostalCode = postalCode;
            company.ProvinceId = provinceId;
            company.CountryId = countryId;
            company.WebsiteUrl = websiteUrl;
            company.AccountNumber = accountNumber;
            company.CompanyGroupId = companyGroupId;
            company.Note = note;
            company.IsArchived = isArchived;
            company.Updatedat = DateTime.Now;
            company.Updatedbyid = user.UserId;

            _context.Company.Update(company);
            _context.SaveChanges();
            return true;
        }

        public Contact? GetContact(int id)
        {
            return _context.Contact
                .Include(x => x.ContactType)
                .Include(x => x.Location)
                .Include(x => x.Company)
                .FirstOrDefault(x => x.ContactId == id);
        }

        public IEnumerable<Contact> GetAllContacts()
        {
            return _context.Contact
                .Include(x => x.ContactType)
                .Include(x => x.Location)
                .Include(x => x.Company);


        }

        public IEnumerable<Contact> GetContactsByCompanyId(int companyId)
        {
            return _context.Contact
                .Include(x => x.ContactType)
                .Include(x => x.Location)
                .Include(x => x.Company)
                .Where(x => x.CompanyId == companyId);
        }

        public bool AddContact(int contactTypeId, int? locationId, int? companyId, string firstName, string lastName,
                              string email, string telephone, string ext, string cellphone, string fax, bool isArchived, string username)
        {
            AuthUser? user = _context.AuthUser.FirstOrDefault(x => x.Username == username);

            if (user == null)
            {
                return false;
            }

            var contact = new Contact()
            {
                ContactTypeId = contactTypeId,
                LocationId = locationId,
                CompanyId = companyId,
                FirstName = firstName,
                LastName = lastName,
                Email = email,
                Telephone = telephone,
                Ext = ext,
                Cellphone = cellphone,
                Fax = fax,
                IsArchived = isArchived,
                CreatedById = user.UserId,
                CreatedAt = DateTime.Now,
            };

            _context.Contact.Add(contact);
            _context.SaveChanges();
            return true;
        }

        public bool UpdateContact(int id, int contactTypeId, int? locationId, int? companyId, string firstName, string lastName,
                                 string email, string telephone, string ext, string cellphone, string fax, bool isArchived, string username)
        {
            AuthUser? user = _context.AuthUser.FirstOrDefault(x => x.Username == username);
            var contact = _context.Contact.FirstOrDefault(x => x.ContactId == id);

            if (contact == null || user == null)
            {
                return false;
            }

            contact.ContactTypeId = contactTypeId;
            contact.LocationId = locationId;
            contact.CompanyId = companyId;
            contact.FirstName = firstName;
            contact.LastName = lastName;
            contact.Email = email;
            contact.Telephone = telephone;
            contact.Ext = ext;
            contact.Cellphone = cellphone;
            contact.Fax = fax;
            contact.IsArchived = isArchived;
            contact.UpdatedById = user.UserId;
            contact.UpdatedAt = DateTime.Now;

            _context.Contact.Update(contact);
            _context.SaveChanges();
            return true;
        }

        public bool DeleteContact(int id, string username)
        {
            AuthUser? user = _context.AuthUser.FirstOrDefault(x => x.Username == username);
            var contact = _context.Contact.FirstOrDefault(x => x.ContactId == id);

            if (contact == null || user == null)
            {
                return false;
            }

            contact.IsArchived = true;
            contact.ArchivedAt = DateTime.Now;
            contact.ArchivedById = user.UserId;

            _context.Contact.Update(contact);
            _context.SaveChanges();
            return true;
        }
    }
}

