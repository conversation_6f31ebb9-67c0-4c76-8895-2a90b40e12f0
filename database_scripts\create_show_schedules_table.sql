-- Create Show Schedules Table Script
-- This script creates the show_schedules table with only essential fields

-- =====================================================
-- 1. Create Show Schedules Table
-- =====================================================

CREATE TABLE show_schedules (
    id SERIAL PRIMARY KEY,
    show_schedule_date VARCHAR(50),
    time_start VARCHAR(50),
    time_end VARCHAR(50),
    show_id INTEGER,
    show_schedule_confirmed BOOLEAN DEFAULT false,
    show_schedule_comments TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    created_by INTEGER,
    apply_schedule_to_service_form BOOLEAN DEFAULT false,
    
    -- Foreign key constraints
    CONSTRAINT fk_show_schedules_show_id 
        FOREIGN KEY (show_id) REFERENCES shows(id) ON DELETE CASCADE,
    CONSTRAINT fk_show_schedules_created_by 
        FOREIGN KEY (created_by) REFERENCES auth_user(user_id) ON DELETE SET NULL
);

-- =====================================================
-- 2. Create Indexes for Performance
-- =====================================================

-- Create indexes for better performance
CREATE INDEX idx_show_schedules_show_id ON show_schedules(show_id);
CREATE INDEX idx_show_schedules_created_by ON show_schedules(created_by);
CREATE INDEX idx_show_schedules_date ON show_schedules(show_schedule_date);
CREATE INDEX idx_show_schedules_confirmed ON show_schedules(show_schedule_confirmed);

-- =====================================================
-- 3. Add Comments for Documentation
-- =====================================================

COMMENT ON TABLE show_schedules IS 'Stores schedule information for specific shows';
COMMENT ON COLUMN show_schedules.id IS 'Primary key, auto-incrementing';
COMMENT ON COLUMN show_schedules.show_schedule_date IS 'Date of the show schedule';
COMMENT ON COLUMN show_schedules.time_start IS 'Start time of the schedule';
COMMENT ON COLUMN show_schedules.time_end IS 'End time of the schedule';
COMMENT ON COLUMN show_schedules.show_id IS 'Reference to the shows table';
COMMENT ON COLUMN show_schedules.show_schedule_confirmed IS 'Whether the schedule is confirmed';
COMMENT ON COLUMN show_schedules.show_schedule_comments IS 'Additional comments for the schedule';
COMMENT ON COLUMN show_schedules.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN show_schedules.created_by IS 'User ID who created the record';
COMMENT ON COLUMN show_schedules.apply_schedule_to_service_form IS 'Whether to apply schedule to service form';

-- =====================================================
-- 4. Verify Table Structure
-- =====================================================

-- Display the new table structure
SELECT 
    column_name,
    data_type,
    column_default,
    is_nullable,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'show_schedules' 
ORDER BY ordinal_position;

-- Display the new constraints
SELECT 
    tc.constraint_name,
    tc.constraint_type,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu 
    ON tc.constraint_name = kcu.constraint_name
LEFT JOIN information_schema.constraint_column_usage ccu 
    ON ccu.constraint_name = tc.constraint_name
WHERE tc.table_name = 'show_schedules'
ORDER BY tc.constraint_type, tc.constraint_name;

-- =====================================================
-- 5. Sample Data (Optional)
-- =====================================================

-- Uncomment to insert sample data
/*
INSERT INTO show_schedules (
    show_schedule_date, 
    time_start, 
    time_end, 
    show_id, 
    show_schedule_confirmed, 
    show_schedule_comments, 
    created_by,
    apply_schedule_to_service_form
) VALUES 
('2024-07-01', '09:00', '17:00', 1, true, 'Main exhibition day', 1, true),
('2024-07-02', '10:00', '18:00', 2, false, 'Second show setup', 1, false);
*/

-- =====================================================
-- 6. Rollback Script (if needed)
-- =====================================================

/*
-- To rollback these changes, run:
DROP INDEX IF EXISTS idx_show_schedules_confirmed;
DROP INDEX IF EXISTS idx_show_schedules_date;
DROP INDEX IF EXISTS idx_show_schedules_created_by;
DROP INDEX IF EXISTS idx_show_schedules_show_id;

ALTER TABLE show_schedules DROP CONSTRAINT IF EXISTS fk_show_schedules_created_by;
ALTER TABLE show_schedules DROP CONSTRAINT IF EXISTS fk_show_schedules_show_id;

DROP TABLE IF EXISTS show_schedules CASCADE;
*/ 