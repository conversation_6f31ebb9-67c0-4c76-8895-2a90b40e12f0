﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class TaxType
    {
        public TaxType()
        {
            OfferingTax = new HashSet<OfferingTax>();
            TaxProvince = new HashSet<TaxProvince>();
        }

        public int Id { get; set; }
        public string TaxName { get; set; }
        public string TaxAbr { get; set; }

        public virtual ICollection<OfferingTax> OfferingTax { get; set; }
        public virtual ICollection<TaxProvince> TaxProvince { get; set; }
    }
}