namespace goodkey_cms.DTO.Schedule
{
    public class ScheduleData
    {
        public string? Code { get; set; }
        public string Name { get; set; } = null!;
        public string? Description { get; set; }
        public string? Notes { get; set; }
        public bool? IsActive { get; set; }
    }

    public class UpdateScheduleData
    {
        public string? Code { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public string? Notes { get; set; }
        public bool? IsActive { get; set; }
    }
}
