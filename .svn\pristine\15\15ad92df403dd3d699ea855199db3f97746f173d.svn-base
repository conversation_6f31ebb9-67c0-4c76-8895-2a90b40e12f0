﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class GallerySubcategories
    {
        public GallerySubcategories()
        {
            GalleryImages = new HashSet<GalleryImages>();
        }

        public int Id { get; set; }
        public int CategoryId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? DisplayOrder { get; set; }

        public virtual GalleryCategories Category { get; set; }
        public virtual ICollection<GalleryImages> GalleryImages { get; set; }
    }
}