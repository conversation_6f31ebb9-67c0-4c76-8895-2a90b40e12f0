﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
	public interface IOfferingRepository
	{
		Task<IEnumerable<Offering>> GetAllAsync(int groupId);
		Task<IEnumerable<Offering>> GetAll();
		Task<Offering> GetByIdAsync(int id);
		Task AddAsync(Offering offering);
		Task UpdateAsync(int id, Offering updated);
		Task<IEnumerable<OfferingTax>> GetTaxesByOfferingIdAsync(int offeringId);
		Task AddTaxToOfferingAsync(OfferingTax tax);
		Task<GroupType> GetGroupTypeByIdAsync(int id);
		Task<Category> GetCategoryByIdAsync(int id);
	}

	public class OfferingRepository : IOfferingRepository
	{
		private readonly GoodkeyContext _context;

		public OfferingRepository(GoodkeyContext context)
		{
			_context = context;
		}

		public async Task<GroupType> GetGroupTypeByIdAsync(int id)
		{
			return await _context.GroupType
				.FirstOrDefaultAsync(gt => gt.Id == id);
		}

		public async Task<Category> GetCategoryByIdAsync(int id)
		{
			return await _context.Category
				.FirstOrDefaultAsync(c => c.Id == id);
		}

		public async Task<IEnumerable<Offering>> GetAll()
		{
			return await _context.Offering
				.Include(x => x.Category)
				.Include(x => x.GroupType)
				.Include(x => x.OfferingTax)
					.ThenInclude(ot => ot.TaxType)
				.ToListAsync();
		}

		public async Task<IEnumerable<Offering>> GetAllAsync(int groupId)
		{
			return await _context.Offering
				.Include(x => x.Category)
				.Include(x => x.GroupType)
				.Include(x => x.OfferingTax)
					.ThenInclude(ot => ot.TaxType).Where(x => x.GroupTypeId == groupId)
				.ToListAsync();
		}

		public async Task<Offering> GetByIdAsync(int id)
		{
			return await _context.Offering
				.Include(x => x.Category)
				.Include(x => x.GroupType)
				.Include(x => x.OfferingTax)
					.ThenInclude(ot => ot.TaxType)
				.FirstOrDefaultAsync(x => x.Id == id);
		}

		public async Task AddAsync(Offering offering)
		{
			await _context.Offering.AddAsync(offering);
			await _context.SaveChangesAsync();
		}

		public async Task UpdateAsync(int id, Offering updated)
		{
			var existing = await _context.Offering.FindAsync(id);
			if (existing == null) return;

			existing.Name = updated.Name;
			existing.Code = updated.Code;
			existing.SupplierItemNumber = updated.SupplierItemNumber;
			existing.PublicDescription = updated.PublicDescription;
			existing.InternalDescription = updated.InternalDescription;
			existing.DisplayOrder = updated.DisplayOrder;
			existing.UnitChargedId = updated.UnitChargedId;
			existing.IsUnitTypeEach = updated.IsUnitTypeEach;
			existing.IsAddOn = updated.IsAddOn;
			existing.IsForSmOnly = updated.IsForSmOnly;
			existing.IsInternalOnly = updated.IsInternalOnly;
			existing.Image = updated.Image;
			existing.IsActive = updated.IsActive;
			existing.IsObsolete = updated.IsObsolete;
			existing.UpdatedById = updated.UpdatedById;
			existing.UpdatedAt = updated.UpdatedAt;

			await _context.SaveChangesAsync();
		}

		public async Task<IEnumerable<OfferingTax>> GetTaxesByOfferingIdAsync(int offeringId)
		{
			return await _context.OfferingTax
				.Include(x => x.TaxType)
				.Where(x => x.OfferingId == offeringId)
				.ToListAsync();
		}

		public async Task AddTaxToOfferingAsync(OfferingTax tax)
		{
			await _context.OfferingTax.AddAsync(tax);
			await _context.SaveChangesAsync();
		}
	}
}
