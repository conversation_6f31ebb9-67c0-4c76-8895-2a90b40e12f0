﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace goodkey_common.Repositories
{
	public interface IGroupTypeRepository
	{
		Task<IEnumerable<GroupType>> GetAllAsync();

	}

	public class GroupTypeRepository : IGroupTypeRepository
	{
		private readonly GoodkeyContext _context;

		public GroupTypeRepository(GoodkeyContext context)
		{
			_context = context;
		}

		public async Task<IEnumerable<GroupType>> GetAllAsync()
		{
			return await _context.GroupType.ToListAsync();
		}

	}
}
