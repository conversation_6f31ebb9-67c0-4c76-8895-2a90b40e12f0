﻿using goodkey_cms.DTO.ShowLocation;
using goodkey_cms.Services;
using goodkey_common.DTO;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;
using FileType = goodkey_cms.Services.FileType;
using StorageService = goodkey_cms.Services.StorageService;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class ShowLocationController : ControllerBase
	{
		private readonly IShowLocationRepository _repository;
		private readonly AuthService _authService;
		private readonly StorageService _storageService;
		private readonly IShowDocRepository _repo;

		public ShowLocationController(IShowLocationRepository repository, AuthService authService, StorageService storageService, IShowDocRepository repo)
		{
			_repository = repository;
			_authService = authService;
			_storageService = storageService;
			_repo = repo;
		}

		[HttpGet]
		public async Task<GenericRespond<IEnumerable<ShowLocationInList>>> GetAll()
		{
			var data = await _repository.GetAllAsync();

			var list = data.Select(x => new ShowLocationInList
			{
				Id = x.LocationId,
				Name = x.Name,
				LocationCode = x.LocationCode,
				City = x.City,
				Province = x.Province?.ProvinceName,
				IsArchived = x.IsArchived,
			}).ToList();

			return new GenericRespond<IEnumerable<ShowLocationInList>>
			{
				Data = list,
				StatusCode = 200,
				Message = "Locations retrieved successfully"
			};
		}

		[HttpGet("[action]/{id}")]
		public async Task<GenericRespond<ShowLocationGeneralDto>> GetById(int id)
		{
			var location = await _repository.GetByIdAsync(id);
			if (location == null)
			{
				return new GenericRespond<ShowLocationGeneralDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Location not found"
				};
			}

			return new GenericRespond<ShowLocationGeneralDto>
			{
				Data = new ShowLocationGeneralDto()
				{
					Id = id,
					LocationCode = location.LocationCode,
					AccessPlanPath = location.AccessPlan,
					Email = location.Email,
					Fax = location.Fax,
					MapLink = location.MapLink,
					Name = location.Name,
					Telephone = location.Telephone,
					Tollfree = location.Tollfree,
					Website = location.Website,
					IsArchived = location.IsArchived,
				},
				Message = "Location retrieved successfully"
			};
		}

		[HttpPost("general")]
		public async Task<GenericRespond<bool>> CreateGeneral([FromForm] ShowLocationGeneralDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool> { Data = false, StatusCode = 401, Message = "Unauthorized" };
			}
			string locationCode = dto.LocationCode;

			var existingCodes = (await _repository.GetAllAsync())
									.Select(l => l.LocationCode)
									.Where(c => !string.IsNullOrWhiteSpace(c))
									.Select(c => c.ToUpper())
									.ToHashSet();

			if (string.IsNullOrWhiteSpace(locationCode))
			{
				// No code provided, generate from name
				locationCode = GenerateUniqueCodeFromName(dto.Name, existingCodes);
			}
			else
			{
				// Code provided, check uniqueness
				locationCode = GenerateUniqueCodeFromInput(locationCode.ToUpper(), existingCodes);
			}

			if (dto.AccessPlan == null || dto.AccessPlan.Length == 0)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 400,
					Message = "No file uploaded."
				};
			}

			var file = _storageService.UploadFile(
				dto.AccessPlan,
				FileType.Document,
				"Show Location",
				Visibility.Protected,
				true,
				"Goodkey_AccessPlan"
			);
			var path = file.RelativePath;			

			//var doc = new ShowDocs
			//{
			//	DocCategoryId = 2,			
			//	FilePath = path,
			//	OriginalFilename = dto.AccessPlan.FileName,
			//	CreatedAt = DateTime.Now,
			//	CreatedById = user.UserId
			//};

			//var accessPlanId = await _repo.AddShowDoc(doc);

			var location = new ShowLocations
			{
				LocationCode = locationCode,
				Name = dto.Name,
				Telephone = dto.Telephone,
				Tollfree = dto.Tollfree,
				Fax = dto.Fax,
				MapLink = dto.MapLink,
				Website = dto.Website,
				Email = dto.Email,
				AccessPlan = path,
				CreatedAt = DateTime.Now,
				CreatedById = user.UserId,
				//AccessPlanId = accessPlanId
			};

			await _repository.AddAsync(location);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Show location created successfully"
			};
		}

		[HttpPatch("general/{id}")]
		public async Task<GenericRespond<bool>> UpdateGeneral(int id, [FromForm] ShowLocationGeneralDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool> { Data = false, StatusCode = 401, Message = "Unauthorized" };
			}

			var updated = new ShowLocations
			{
				Name = dto.Name,
				LocationCode = dto.LocationCode,
				Telephone = dto.Telephone,
				Tollfree = dto.Tollfree,
				Fax = dto.Fax,
				MapLink = dto.MapLink,
				Website = dto.Website,
				Email = dto.Email,
				AccessPlan = dto.AccessPlanPath,
				UpdatedById = user.UserId,
				UpdatedAt = DateTime.Now,
				IsArchived = dto.IsArchived
			};

			if (dto.AccessPlan != null && dto.AccessPlan.Length > 0)
			{
				var newPath = _storageService.UploadFile(
					dto.AccessPlan,
					FileType.Document,
					"Show Location",
					Visibility.Protected,
					true,
					"Goodkey_ShowLocation").RelativePath;

				updated.AccessPlan = newPath;
			}

			await _repository.UpdateGeneralInfoAsync(id, updated);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "General info updated successfully"
			};
		}

		[HttpGet("address/{id}")]
		public async Task<GenericRespond<ShowLocationAddressDto>> GetAddress(int id)
		{
			var location = await _repository.GetAddressInfoAsync(id);
			if (location == null)
			{
				return new GenericRespond<ShowLocationAddressDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Location not found"
				};
			}

			var dto = new ShowLocationAddressDto
			{
				Address1 = location.Address1,
				Address2 = location.Address2,
				PostalCode = location.PostalCode,
				City = location.City,
				ProvinceId = location.ProvinceId,
				CountryId = location.CountryId,
				SameForShipping = location.ShippingUsingMain,
				ShippingAddress1 = location.ShippingAddress1,
				ShippingAddress2 = location.ShippingAddress2,
				ShippingPostalCode = location.ShippingPostalCode,
				ShippingCity = location.ShippingCity,
				ShippingProvinceId = location.ShippingProvinceId,
				ShippingCountryId = location.ShippingCountryId
			};

			return new GenericRespond<ShowLocationAddressDto>
			{
				Data = dto,
				StatusCode = 200,
				Message = "Address info retrieved successfully"
			};
		}


		[HttpPatch("address/{id}")]
		public async Task<GenericRespond<bool>> UpdateAddress(int id, [FromBody] ShowLocationAddressDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool> { Data = false, StatusCode = 401, Message = "Unauthorized" };
			}

			var updated = new ShowLocations
			{
				Address1 = dto.Address1,
				Address2 = dto.Address2,
				PostalCode = dto.PostalCode,
				City = dto.City,
				ProvinceId = dto.ProvinceId,
				CountryId = dto.CountryId,
				ShippingUsingMain = dto.SameForShipping,
				ShippingAddress1 = dto.ShippingAddress1,
				ShippingAddress2 = dto.ShippingAddress2,
				ShippingPostalCode = dto.ShippingPostalCode,
				ShippingCity = dto.ShippingCity,
				ShippingProvinceId = dto.ShippingProvinceId,
				ShippingCountryId = dto.ShippingCountryId,
				UpdatedById = user.UserId,
				UpdatedAt = DateTime.Now,

			};

			await _repository.UpdateAddressInfoAsync(id, updated);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Address info updated successfully"
			};
		}

		private string GenerateUniqueCodeFromName(string name, HashSet<string> existingCodes)
		{
			var baseCode = new string(name
				.Where(char.IsLetter)
				.Take(5)
				.ToArray())
				.ToUpper();

			if (baseCode.Length < 3)
			{
				baseCode = baseCode.PadRight(3, 'X');
			}

			return GenerateUniqueCodeFromInput(baseCode, existingCodes);
		}

		private string GenerateUniqueCodeFromInput(string baseCode, HashSet<string> existingCodes)
		{
			string uniqueCode = baseCode;
			int suffix = 1;

			while (existingCodes.Contains(uniqueCode))
			{
				var prefix = baseCode.Length > 4 ? baseCode.Substring(0, 4) : baseCode;
				uniqueCode = $"{prefix}{suffix}";
				suffix++;
			}

			return uniqueCode;
		}


	}
}
