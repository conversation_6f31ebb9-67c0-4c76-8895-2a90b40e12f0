using goodkey_cms.DTO;
using goodkey_cms.DTO.Schedule;
using goodkey_cms.Infrastructure.Extensions;
using goodkey_cms.Repositories;
using goodkey_common.DTO;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class SchedulesController : ControllerBase
	{
		private readonly IScheduleRepository _repo;

		public SchedulesController(IScheduleRepository repo)
		{
			_repo = repo;
		}

		[HttpGet]
		public GenericRespond<IEnumerable<BasicScheduleDetail>> Get()
		{
			return new()
			{
				Data = _repo.GetAll().Select(item =>
					new BasicScheduleDetail()
					{
						Id = item.Id,
						Code = item.Code,
						Name = item.Name,
						Description = item.Description,
						IsActive = item.IsActive ?? true,
						CreatedAt = item.CreatedAt
					}
				)
			};
		}

		[HttpGet("{id}")]
		public GenericRespond<ScheduleDetail?> Get(int id)
		{
			var item = _repo.Get(id);
			if (item == null)
			{
				return new GenericRespond<ScheduleDetail?>
				{
					Data = null,
					StatusCode = 404,
					Message = "Schedule not found"
				};
			}

			return new()
			{
				Data = new ScheduleDetail()
				{
					Id = item.Id,
					Code = item.Code,
					Name = item.Name,
					Description = item.Description,
					Notes = item.Notes,
					IsActive = item.IsActive ?? true,
					CreatedBy = item.CreatedBy != null ? new BasicDetail
					{
						Id = item.CreatedBy.UserId,
						Name = $"{item.CreatedBy.FirstName} {item.CreatedBy.LastName}"
					} : null,
					UpdatedBy = item.UpdatedBy != null ? new BasicDetail
					{
						Id = item.UpdatedBy.UserId,
						Name = $"{item.UpdatedBy.FirstName} {item.UpdatedBy.LastName}"
					} : null,
					CreatedAt = item.CreatedAt,
					UpdatedAt = item.UpdatedAt
				}
			};
		}

		[HttpPost]
		public GenericRespond<int?> Create([FromBody] ScheduleData data)
		{
			var username = Request.HttpContext.GetUsername() ?? "system";

			var scheduleId = _repo.Create(data.Code, data.Name, data.Description, data.Notes, data.IsActive, username);

			if (scheduleId == null)
			{
				return new GenericRespond<int?>
				{
					Data = null,
					StatusCode = 400,
					Message = "Failed to create schedule. Unable to generate unique code or user not found."
				};
			}

			return new GenericRespond<int?>
			{
				Data = scheduleId,
				StatusCode = 201,
				Message = "Schedule created successfully."
			};
		}

		[HttpPatch("{id}")]
		public GenericRespond<bool> Update(int id, [FromBody] UpdateScheduleData data)
		{
			var username = Request.HttpContext.GetUsername() ?? "system";

			var success = _repo.Update(id, data.Code, data.Name, data.Description, data.Notes, data.IsActive, username);

			return new GenericRespond<bool>
			{
				Data = success,
				StatusCode = success ? 200 : 400,
				Message = success ? "Schedule updated successfully." : "Failed to update schedule. Schedule may not exist, code may already be in use, or user not found."
			};
		}

		[HttpDelete("{id}")]
		public GenericRespond<bool> Delete(int id)
		{
			var success = _repo.Delete(id);

			return new GenericRespond<bool>
			{
				Data = success,
				StatusCode = success ? 200 : 404,
				Message = success ? "Schedule deleted successfully." : "Schedule not found."
			};
		}

		[HttpPatch("{id}/toggle-active")]
		public GenericRespond<bool> ToggleActive(int id)
		{
			var username = Request.HttpContext.GetUsername() ?? "system";

			var success = _repo.ToggleActive(id, username);

			return new GenericRespond<bool>
			{
				Data = success,
				StatusCode = success ? 200 : 404,
				Message = success ? "Schedule status toggled successfully." : "Schedule not found."
			};
		}
	}
}
