﻿using goodkey_cms.Services;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class StorageController : ControllerBase
	{
		private readonly StorageService _storage;

		public StorageController(StorageService storage)
		{
			_storage = storage;
		}
		[HttpGet("img/{*path}")]
		public IActionResult GetPublicImage([FromRoute] string path, [FromQuery] int? width, [FromQuery] int? height)
		{
			try
			{
				var downloadedFile = _storage.DownloadImage(path, width, height);
				return File(downloadedFile.FileBytes, downloadedFile.ContentType);
			}
			catch
			{
				return StatusCode(404, "Something went wrong");
			}
		}
		[HttpGet("doc/{*path}")]
		public IActionResult GetPublicDocs([FromRoute] string path, [FromQuery] bool Protected = false, [FromBody] int preview = 0)
		{
			try
			{
				var downloadedFile = preview > 0 ? _storage.GeneratePreview(path, Protected ? Visibility.Protected : Visibility.Public, preview) : _storage.DownloadDocument(path, Protected ? Visibility.Protected : Visibility.Public);
				return File(downloadedFile.FileBytes, downloadedFile.ContentType);
			}
			catch
			{
				return StatusCode(404, "Something went wrong");
			}
		}


	}
}
