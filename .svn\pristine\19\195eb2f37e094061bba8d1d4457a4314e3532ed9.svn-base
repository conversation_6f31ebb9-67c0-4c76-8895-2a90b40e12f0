﻿

using goodkey_cms.Repositories;
using goodkey_common.DTO;
using goodkey_common.Models;
using goodkey_public.Dto.Application;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_public.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MenuController : ControllerBase
    {
        private readonly IMenuRepository _repo;
        public MenuController(IMenuRepository repo)
        {
            _repo = repo;
        }

        [HttpGet("[action]")]
        public GenericRespond<IEnumerable<MenuBasicDetail>> Header()
        {
            return new()
            {
                Data = _repo.GetAll()
                    .Where(c => c.Section != null && c.Section.Name == "public_header")
                    .Where(c => c.IsVisible == true)
                    .Where(c => c.Parent == null || c.Parent.Section?.Name != "public_header")
                    .OrderBy(c => c.DisplayOrder)
                    .Select(item => GetMenu(item, "public_header"))
            };
        }

        [HttpGet("[action]")]
        public GenericRespond<IEnumerable<MenuBasicDetail>> Footer()
        {
            return new()
            {
                Data = _repo.GetAll()
                    .Where(c => c.Section != null && c.Section.Name == "public_footer")
                    .Where(c => c.IsVisible == true)
                    .Where(c => c.Parent == null || c.Parent.Section?.Name != "public_footer")
                    .OrderBy(c => c.DisplayOrder)
                    .Select(item => GetMenu(item, "public_footer"))
            };
        }

        private MenuBasicDetail GetMenu(MenuItem item, string sectionName)
        {
            return new MenuBasicDetail()
            {
                Id = item.MenuItemId,
                Name = item.Name,
                Href = item.Url ?? "",
                Children = item.InverseParent
                    .Where(c => c.IsVisible == true)
                    .Where(c => c.Section != null && c.Section.Name == sectionName)
                    .OrderBy(c => c.DisplayOrder)
                    .Select(c => GetMenu(c, sectionName)),
                Image = item.ImagePath,
                Description = item.Description,
                MetaDescription = item.MetaDescription,
                Keywords = item.MetaKeywords,
                Target = item.Target,
                Level = item.Level,
                Permission = item.PermissionKey
            };
        }
    }
}
