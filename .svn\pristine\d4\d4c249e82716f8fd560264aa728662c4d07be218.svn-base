﻿using goodkey_cms.DTO;
using goodkey_cms.DTO.Category;
using goodkey_cms.DTO.Offering;
using goodkey_cms.DTO.Property;
using goodkey_cms.Services;
using goodkey_common.DTO;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;
using FileType = goodkey_cms.Services.FileType;
using StorageService = goodkey_cms.Services.StorageService;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class CategoryController : ControllerBase
	{
		private readonly ICategoryRepository _repository;
		private readonly AuthService _authService;
		private readonly StorageService _storageService;

		public CategoryController(
			ICategoryRepository repository,
			AuthService authService,
			StorageService storageService)
		{
			_repository = repository;
			_authService = authService;
			_storageService = storageService;
		}

		[HttpGet]
		public async Task<GenericRespond<IEnumerable<CategoryDto>>> GetAll()
		{
			var data = await _repository.GetAllAsync();

			var list = data.Select(x => new CategoryDto
			{
				Id = x.Id,
				Name = x.Name,
				Code = x.Code,
				Group = x.Group?.Name,
				DisplayOrder = x.DisplayOrder,
				ImagePath = x.ImagePath,
				IsSoldByQ = x.IsSoldByQ,
				IsInternalProduct = x.IsInternalProduct,
				IsAvailable = x.IsAvailable ?? false
			}).ToList();

			return new GenericRespond<IEnumerable<CategoryDto>>
			{
				Data = list,
				StatusCode = 200,
				Message = "Categories retrieved successfully"
			};
		}

		[HttpGet("brief")]
		public async Task<GenericRespond<IEnumerable<BasicDetail>>> GetBrief()
		{
			var data = await _repository.GetAllAsync();

			var list = data.Select(x => new BasicDetail
			{
				Id = x.Id,
				Name = x.Code + " " + x.Name
			}).ToList();

			return new GenericRespond<IEnumerable<BasicDetail>>
			{
				Data = list,
				StatusCode = 200,
				Message = "Categories retrieved successfully"
			};
		}

		[HttpGet("{id}")]
		public async Task<GenericRespond<CategoryDetailDto>> GetById(int id)
		{
			var category = await _repository.GetByIdAsync(id);
			if (category == null)
			{
				return new GenericRespond<CategoryDetailDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Category not found"
				};
			}

			return new GenericRespond<CategoryDetailDto>
			{
				Data = new CategoryDetailDto()
				{
					Id = id,
					GroupId = category.GroupId,
					Name = category.Name,
					Code = category.Code,
					DisplayOrder = category.DisplayOrder,
					ImagePath = category.ImagePath,
					IsSoldByQ = category.IsSoldByQ ?? false,
					IsInternalProduct = category.IsInternalProduct ?? false,
					IsAvailable = category.IsAvailable ?? false,
				},
				Message = "Category retrieved successfully"
			};
		}


		[HttpGet("{id}/Details")]
		public async Task<GenericRespond<CategoryDto>> GetDetailById(int id)
		{
			var x = await _repository.GetByIdAsync(id);
			if (x == null)
			{
				return new GenericRespond<CategoryDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Category not found"
				};
			}

			return new GenericRespond<CategoryDto>
			{
				Data = new CategoryDto()
				{
					Id = x.Id,
					Name = x.Name,
					Code = x.Code,
					Group = x.Group?.Name,
					DisplayOrder = x.DisplayOrder,
					ImagePath = x.ImagePath,
					IsSoldByQ = x.IsSoldByQ,
					IsInternalProduct = x.IsInternalProduct,
					IsAvailable = x.IsAvailable ?? false
				},
				Message = "Category retrieved successfully"
			};
		}

		[HttpGet("{id}/properties")]
		public async Task<GenericRespond<CategorySelectionDto>> GetProperty(int id)
		{
			var category = await _repository.GetByIdAsync(id);
			if (category == null)
			{
				return new GenericRespond<CategorySelectionDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Category not found"
				};
			}

			return new GenericRespond<CategorySelectionDto>
			{
				Data = new CategorySelectionDto()
				{
					Selection1 = category.CategoryProperty?.FirstOrDefault(p => p.DisplayOrder == 1)?.PropertyId,
					Selection2 = category.CategoryProperty?.FirstOrDefault(p => p.DisplayOrder == 2)?.PropertyId,
				},
				Message = "Category retrieved successfully"
			};
		}

		[HttpPost]
		public async Task<GenericRespond<bool>> Create([FromForm] CategoryCreateDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			string imagePath = null;
			if (dto.Image != null && dto.Image.Length > 0)
			{
				var file = _storageService.UploadFile(
					dto.Image,
					FileType.Image,
					"Category",
					Visibility.Public,
					true,
					"Goodkey_Category");

				imagePath = file.RelativePath;
			}

			var category = new Category
			{
				GroupId = dto.GroupId ?? 0,
				Name = dto.Name,
				Code = await GenerateUniqueCode(),
				DisplayOrder = dto.DisplayOrder,
				ImagePath = imagePath,
				IsSoldByQ = dto.IsSoldByQ,
				IsInternalProduct = dto.IsInternalProduct,
				IsAvailable = dto.IsAvailable,
				CreatedAt = DateTime.Now,
				CreatedById = user.UserId
			};

			await _repository.AddAsync(category);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Category created successfully"
			};
		}

		[HttpPost("{id}/properties")]
		public async Task<GenericRespond<bool>> SaveSelections(int id, [FromBody] CategorySelectionDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			var category = await _repository.GetByIdAsync(id);
			if (category == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 404,
					Message = "Category not found"
				};
			}

			category.CategoryProperty?.Clear();

			var newAssociations = new List<CategoryProperty>();

			if (dto.Selection1.HasValue)
			{
				newAssociations.Add(new CategoryProperty
				{
					CategoryId = id,
					PropertyId = dto.Selection1.Value,
					DisplayOrder = 1,
					CreatedById = user.UserId,
					CreatedAt = DateTime.Now
				});
			}

			if (dto.Selection2.HasValue)
			{
				newAssociations.Add(new CategoryProperty
				{
					CategoryId = id,
					PropertyId = dto.Selection2.Value,
					DisplayOrder = 2,
					CreatedById = user.UserId,
					CreatedAt = DateTime.Now
				});
			}

			category.CategoryProperty = newAssociations;
			category.UpdatedAt = DateTime.Now;
			category.UpdatedById = user.UserId;

			await _repository.UpdateAsync(id, category);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Selections saved successfully"
			};
		}

		[HttpPut("{id}")]
		public async Task<GenericRespond<bool>> Update(int id, [FromForm] CategoryUpdateDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			var existingCategory = await _repository.GetByIdAsync(id);
			if (existingCategory == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 404,
					Message = "Category not found"
				};
			}

			string imagePath = existingCategory.ImagePath;
			if (dto.Image != null && dto.Image.Length > 0)
			{
				var file = _storageService.UploadFile(
					dto.Image,
					FileType.Image,
					"Category",
					Visibility.Public,
					true,
					"Goodkey_Category");
				if (existingCategory.ImagePath != null) _storageService.DeleteFileSpecific(existingCategory.ImagePath);
				imagePath = file.RelativePath;
			}

			var updatedCategory = new Category
			{
				GroupId = dto.GroupId ?? 0,
				Name = dto.Name,
				Code = existingCategory.Code,
				DisplayOrder = dto.DisplayOrder,
				ImagePath = imagePath,
				IsSoldByQ = dto.IsSoldByQ,
				IsInternalProduct = dto.IsInternalProduct,
				IsAvailable = dto.IsAvailable,
				UpdatedAt = DateTime.Now,
				UpdatedById = user.UserId
			};

			await _repository.UpdateAsync(id, updatedCategory);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Category updated successfully"
			};
		}

		private async Task<string> GenerateUniqueCode()
		{
			// Fetch all existing codes from the database
			var groups = await _repository.GetAllAsync();

			// Parse numeric codes and sort
			var existingCodes = groups
				.Select(g => int.TryParse(g.Code, out var code) ? code : (int?)null)
				.Where(c => c.HasValue && c.Value >= 1 && c.Value <= 99)
				.Select(c => c.Value)
				.ToHashSet();

			// Find the smallest available 2-digit number
			for (int i = 1; i <= 999; i++)
			{
				if (!existingCodes.Contains(i))
				{
					return i.ToString("D3");
				}
			}

			throw new InvalidOperationException("No available group codes. Limit of 99 reached.");
		}

		[HttpGet("{id}/PropertyDetails")]
		public async Task<GenericRespond<IEnumerable<PropertyDetail>>> GetAllDetails(int id)
		{
			var properties = await _repository.GetByCategoryIdAsync(id);

			var result = properties.Select(p => new PropertyDetail
			{
				Id = p.PropertyId,
				Code = p.Property.Code,
				Name = p.Property.Name,
				Description = p.Property.Description,
				Option = p.Property.PropertyOption?.Select(opt => new PropertyOptionDto
				{
					Id = opt.Id,
					Code = opt.Code,
					PropertyId = p.PropertyId,
					Name = opt.Name,
					Description = opt.Description,
				}).ToArray()
			}).ToList();

			return new GenericRespond<IEnumerable<PropertyDetail>>
			{
				Data = result,
				Message = "Properties with options retrieved successfully",
				StatusCode = 200
			};
		}


		[HttpGet("GetAllAddOn")]
		public async Task<GenericRespond<IEnumerable<CategoryWithOfferingsDto>>> GetAllSorted()
		{
			var category = await _repository.GetAllAsync();
			if (category == null)
			{
				return new GenericRespond<IEnumerable<CategoryWithOfferingsDto>>
				{
					Data = null,
					StatusCode = 404,
					Message = "Category not found."
				};
			}

			// Map the data to DTOs
			var categories = category.Where(c => c.IsAvailable == true).Select(category => new CategoryWithOfferingsDto
			{
				CategoryId = category.Id,
				CategoryName = category.Name,
				IsAvailable = category.IsAvailable,
				Offerings = category.Offering?.Where(x => x.IsAddOn == true && x.IsActive == true && x.IsObsolete != true).Select(offering => new OfferingDto
				{
					Id = offering.Id,
					Name = offering.Name,
					Code = offering.Code,
					SupplierItemNumber = offering.SupplierItemNumber,
					PublicDescription = offering.PublicDescription,
					IsActive = offering.IsActive,
					IsObsolete = offering.IsObsolete,
					Options = offering.OfferingProperty.Where(c => c.IsActive == true).Select(p => new PropertyOptionsDto
					{
						Id = p.Id,
						Name = p.PropertyOption1?.Name + (p.PropertyOption2 != null ? $", {p.PropertyOption2.Name}" : ""),
						Code = p.Code,
						IsActive = p.IsActive,
						Image = p.Image,
					}
					),
				}).ToList()
			}).ToList();


			// Return the successful response
			return new GenericRespond<IEnumerable<CategoryWithOfferingsDto>>
			{
				Data = categories,
				StatusCode = 200,
				Message = "Category retrieved successfully"
			};
		}
	}
}