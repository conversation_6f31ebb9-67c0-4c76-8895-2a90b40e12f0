﻿using goodkey_cms.DTO.Property;
using goodkey_cms.Services;
using goodkey_common.DTO;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class PropertyController : ControllerBase
	{
		private readonly IPropertyRepository _repository;
		private readonly AuthService _authService;

		public PropertyController(
			IPropertyRepository repository,
			AuthService authService)
		{
			_repository = repository;
			_authService = authService;
		}

		[HttpGet]
		public async Task<GenericRespond<IEnumerable<PropertyDto>>> GetAll()
		{
			var data = await _repository.GetAllAsync();

			var list = data.Select(x => new PropertyDto
			{
				Id = x.Id,
				Name = x.Name,
				Code = x.Code,
				Description = x.Description
			}).ToList();

			return new GenericRespond<IEnumerable<PropertyDto>>
			{
				Data = list,
				StatusCode = 200,
				Message = "Properties retrieved successfully"
			};
		}

		[HttpGet("{id}")]
		public async Task<GenericRespond<PropertyDto>> GetById(int id)
		{
			var property = await _repository.GetByIdAsync(id);
			if (property == null)
			{
				return new GenericRespond<PropertyDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Property not found"
				};
			}

			return new GenericRespond<PropertyDto>
			{
				Data = new PropertyDto
				{
					Id = property.Id,
					Name = property.Name,
					Code = property.Code,
					Description = property.Description
				},
				StatusCode = 200,
				Message = "Property retrieved successfully"
			};
		}

		[HttpPost]
		public async Task<GenericRespond<bool>> Create([FromBody] PropertyCreateDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			var property = new Property
			{
				Name = dto.Name,
				Description = dto.Description,
				Code = await GenerateNextAvailableCode(),
				CreatedAt = DateTime.Now,
				CreatedById = user.UserId
			};

			await _repository.AddAsync(property);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Property created successfully"
			};
		}

		[HttpPut("{id}")]
		public async Task<GenericRespond<bool>> Update(int id, [FromBody] PropertyCreateDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			var existing = await _repository.GetByIdAsync(id);
			if (existing == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 404,
					Message = "Property not found"
				};
			}

			var updated = new Property
			{
				Name = dto.Name,
				Code = existing.Code, // Keep original code
				Description = dto.Description,
				UpdatedAt = DateTime.Now,
				UpdatedById = user.UserId
			};

			await _repository.UpdateAsync(id, updated);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Property updated successfully"
			};
		}

		private async Task<string> GenerateNextAvailableCode()
		{
			var existingCodes = (await _repository.GetAllAsync())
				.Select(p => p.Code)
				.Where(code => !string.IsNullOrWhiteSpace(code))
				.ToHashSet();

			for (int i = 1; i <= 99; i++)
			{
				string code = i.ToString("D2"); // "01" to "99"
				if (!existingCodes.Contains(code))
				{
					return code;
				}
			}

			throw new InvalidOperationException("All property codes from 01 to 99 are used.");
		}
	}
}
