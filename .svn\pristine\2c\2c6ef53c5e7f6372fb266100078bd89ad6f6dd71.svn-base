﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class Property
    {
        public Property()
        {
            CategoryProperty = new HashSet<CategoryProperty>();
            PropertyOption = new HashSet<PropertyOption>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public string Description { get; set; }
        public int? CreatedById { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public virtual AuthUser CreatedBy { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
        public virtual ICollection<CategoryProperty> CategoryProperty { get; set; }
        public virtual ICollection<PropertyOption> PropertyOption { get; set; }
    }
}