﻿using goodkey_cms.DTO;
using goodkey_cms.DTO.ShowHall;
using goodkey_cms.Services;
using goodkey_common.DTO;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;
using FileType = goodkey_cms.Services.FileType;
using StorageService = goodkey_cms.Services.StorageService;
using Visibility = goodkey_cms.Services.Visibility;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class ShowHallController : ControllerBase
	{
		private readonly IShowLocationHallRepository _hallRepository;
		private readonly AuthService _authService;
		private readonly StorageService _storageService;
		public ShowHallController(IShowLocationHallRepository hallRepository, AuthService authService, StorageService storageService)
		{
			_hallRepository = hallRepository;
			_authService = authService;
			_storageService = storageService;
		}

		[HttpGet("{locationId}")]
		public async Task<GenericRespond<IEnumerable<ShowHallDto>>> GetAllByLocation(int locationId)
		{
			var halls = await _hallRepository.GetAllByLocationIdAsync(locationId);

			var result = halls.OrderByDescending(x => x.HallId).Select(h => new ShowHallDto
			{
				Id = h.HallId,
				LocationId = h.LocationId,
				HallName = h.HallName,
				HallCode = h.HallCode,
				HallStyle = h.HallStyle,
				HallFloorType = h.HallFloorType,
				BanquetCapacity = h.BanquetCapacity,
				HallWidth = h.HallWidth,
				HallLength = h.HallLength,
				OverheadHeight = h.OverheadHeight,
				HallArea = h.HallArea,
				IsElecOnFloor = h.IsElecOnFloor,
				IsElecOnCeiling = h.IsElecOnCeiling,
				HallSurface = h.HallSurface,
				HallCeilingHeight = h.HallCeilingHeight,
				AccessDoor = h.AccessDoor,
				LoadingDocks = h.LoadingDocks,
				HallBoothCount = h.HallBoothCount,
				IsArchived = h.IsArchived,
			});

			return new GenericRespond<IEnumerable<ShowHallDto>>
			{
				Data = result,
				StatusCode = 200,
				Message = "Halls retrieved successfully"
			};
		}


		[HttpGet("GetBrief/{locationId}")]
		public async Task<GenericRespond<IEnumerable<BasicDetail>>> GetBriefByLocation(int locationId)
		{
			var halls = await _hallRepository.GetAllByLocationIdAsync(locationId);

			var result = halls.Select(h => new BasicDetail
			{
				Id = h.HallId,
				Name = h.HallCode + " " + h.HallName
			}).OrderBy(x => x.Name);

			return new GenericRespond<IEnumerable<BasicDetail>>
			{
				Data = result,
				StatusCode = 200,
				Message = "Halls retrieved successfully"
			};
		}

		[HttpGet("hall/{hallId}")]
		public async Task<GenericRespond<ShowHallDto>> GetById(int hallId)
		{
			var hall = await _hallRepository.GetByIdAsync(hallId);
			if (hall == null)
			{
				return new GenericRespond<ShowHallDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Hall not found"
				};
			}

			return new GenericRespond<ShowHallDto>
			{
				Data = new ShowHallDto
				{
					Id = hall.HallId,
					LocationId = hall.LocationId,
					HallName = hall.HallName,
					HallCode = hall.HallCode,
					HallStyle = hall.HallStyle,
					HallFloorType = hall.HallFloorType,
					BanquetCapacity = hall.BanquetCapacity,
					HallWidth = hall.HallWidth,
					HallLength = hall.HallLength,
					OverheadHeight = hall.OverheadHeight,
					HallArea = hall.HallArea,
					IsElecOnFloor = hall.IsElecOnFloor,
					IsElecOnCeiling = hall.IsElecOnCeiling,
					HallSurface = hall.HallSurface,
					HallCeilingHeight = hall.HallCeilingHeight,
					AccessDoor = hall.AccessDoor,
					LoadingDocks = hall.LoadingDocks,
					HallBoothCount = hall.HallBoothCount,
					IsArchived = hall.IsArchived,
					FloorPlanPath = hall.FloorPlan,
				},
				StatusCode = 200,
				Message = "Hall retrieved successfully"
			};
		}

		[HttpPost]
		public async Task<GenericRespond<bool>> Create([FromForm] ShowHallDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool> { Data = false, StatusCode = 401, Message = "Unauthorized" };
			}


			string code = dto.HallCode;

			var existingCodes = (await _hallRepository.GetAllByLocationIdAsync(dto.LocationId ?? 0))
				.Where(h => !string.IsNullOrWhiteSpace(h.HallCode))
				.Select(h => h.HallCode.ToUpper())
				.ToHashSet();

			if (string.IsNullOrWhiteSpace(code))
			{
				code = GenerateUniqueCodeFromName(dto.HallName, existingCodes);
			}
			else
			{
				if (existingCodes.Contains(dto.HallCode.ToUpper()))
				{
					return new GenericRespond<bool>
					{
						Data = false,
						StatusCode = 409,
						Message = "Hall code already exists."
					};
				}
			}

			var floorPlan = _storageService.UploadFile(
						dto.FloorPlan,
						FileType.Document,
						"Show Halls",
						Visibility.Protected,
						true,
						"Goodkey_ShowHall").RelativePath;

			var hall = new ShowLocationHalls
			{
				LocationId = dto.LocationId ?? 0,
				HallName = dto.HallName,
				HallCode = code,
				HallStyle = dto.HallStyle,
				HallFloorType = dto.HallFloorType,
				BanquetCapacity = dto.BanquetCapacity,
				HallWidth = dto.HallWidth,
				HallLength = dto.HallLength,
				OverheadHeight = dto.OverheadHeight,
				HallArea = dto.HallArea,
				IsElecOnFloor = dto.IsElecOnFloor,
				IsElecOnCeiling = dto.IsElecOnCeiling,
				HallSurface = dto.HallSurface,
				HallCeilingHeight = dto.HallCeilingHeight,
				AccessDoor = dto.AccessDoor,
				LoadingDocks = dto.LoadingDocks,
				HallBoothCount = dto.HallBoothCount,
				FloorPlan = floorPlan,
				CreatedById = user.UserId,
				CreatedAt = DateTime.Now,

			};

			await _hallRepository.AddAsync(hall);

			return new GenericRespond<bool>
			{
				Data = true,
				StatusCode = 200,
				Message = "Hall created successfully"
			};

		}

		[HttpPatch("{hallId}")]
		public async Task<GenericRespond<bool>> Update(int hallId, [FromForm] ShowHallDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool> { Data = false, StatusCode = 401, Message = "Unauthorized" };
			}

			try
			{
				var existingHall = await _hallRepository.GetByIdAsync(hallId);
				if (existingHall == null)
				{
					return new GenericRespond<bool>
					{
						Data = false,
						StatusCode = 404,
						Message = "Hall not found"
					};
				}

				// Check if HallCode is being updated
				bool isCodeUpdated = !string.Equals(existingHall.HallCode, dto.HallCode, StringComparison.OrdinalIgnoreCase);

				if (isCodeUpdated)
				{
					// Get all other halls in the same location
					var hallsInSameLocation = await _hallRepository.GetAllByLocationIdAsync(existingHall.LocationId);
					var duplicate = hallsInSameLocation
						.Any(h => h.HallId != hallId && h.HallCode.Equals(dto.HallCode, StringComparison.OrdinalIgnoreCase));

					if (duplicate)
					{
						return new GenericRespond<bool>
						{
							Data = false,
							StatusCode = 409,
							Message = "Hall code already exists in this location."
						};
					}
				}

				var updated = new ShowLocationHalls
				{
					HallName = dto.HallName,
					HallCode = dto.HallCode,
					HallStyle = dto.HallStyle,
					HallFloorType = dto.HallFloorType,
					BanquetCapacity = dto.BanquetCapacity,
					HallWidth = dto.HallWidth,
					HallLength = dto.HallLength,
					OverheadHeight = dto.OverheadHeight,
					HallArea = dto.HallArea,
					IsElecOnFloor = dto.IsElecOnFloor,
					IsElecOnCeiling = dto.IsElecOnCeiling,
					HallSurface = dto.HallSurface,
					HallCeilingHeight = dto.HallCeilingHeight,
					AccessDoor = dto.AccessDoor,
					LoadingDocks = dto.LoadingDocks,
					HallBoothCount = dto.HallBoothCount,
					UpdatedById = user.UserId,
					UpdatedAt = DateTime.Now,
					IsArchived = dto.IsArchived,
				};

				if (dto.FloorPlan != null && dto.FloorPlan.Length > 0)
				{
					var newPath = _storageService.UploadFile(
						dto.FloorPlan,
						FileType.Document,
						"Show Halls",
						Visibility.Protected,
						true,
						"Goodkey_ShowHall").RelativePath;
					if (existingHall.FloorPlan != null) { _storageService.DeleteFileSpecific(existingHall.FloorPlan); }
					updated.FloorPlan = newPath;
				}

				await _hallRepository.UpdateAsync(hallId, updated);

				return new GenericRespond<bool>
				{
					Data = true,
					StatusCode = 200,
					Message = "Hall updated successfully"
				};
			}
			catch (Exception ex)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 500,
					Message = $"Error updating hall: {ex.Message}"
				};
			}
		}

		private string GenerateUniqueCodeFromName(string name, HashSet<string> existingCodes)
		{
			var baseCode = new string(name
				.Where(char.IsLetter)
				.Take(5)
				.ToArray())
				.ToUpper();

			if (baseCode.Length < 3)
			{
				baseCode = baseCode.PadRight(3, 'X');
			}

			return GenerateUniqueCodeFromInput(baseCode, existingCodes);
		}

		private string GenerateUniqueCodeFromInput(string baseCode, HashSet<string> existingCodes)
		{
			string uniqueCode = baseCode;
			int suffix = 1;

			while (existingCodes.Contains(uniqueCode))
			{
				var prefix = baseCode.Length > 4 ? baseCode.Substring(0, 4) : baseCode;
				uniqueCode = $"{prefix}{suffix}";
				suffix++;
			}

			return uniqueCode;
		}
	}
}
