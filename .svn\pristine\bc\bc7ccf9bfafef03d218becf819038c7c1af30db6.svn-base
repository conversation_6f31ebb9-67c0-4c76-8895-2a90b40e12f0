﻿using goodkey_cms.DTO;
using goodkey_cms.DTO.Group;
using goodkey_cms.DTO.Offering;
using goodkey_cms.Services;
using goodkey_common.DTO;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class GroupController : ControllerBase
	{
		private readonly IGroupRepository _repository;
		private readonly AuthService _authService;

		public GroupController(
			IGroupRepository repository,
			AuthService authService)
		{
			_repository = repository;
			_authService = authService;
		}


		[HttpGet("{groupId}/get")]
		public async Task<GenericRespond<GroupWithCategoriesDto>> GetAllSorted(int groupId)
		{
			// Fetch the GroupType data with related categories and offerings
			var groupType = await _repository.GetAll(groupId);

			// Check if the groupType exists (important for handling the case where groupId is not found)
			if (groupType == null)
			{
				return new GenericRespond<GroupWithCategoriesDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Group type not found."
				};
			}

			// Prepare the grouped offerings by category
			var groupedByCategory = groupType.Category
				.Select(category => new CategoryWithOfferingsDto
				{
					CategoryId = category.Id,
					CategoryName = category.Name,
					Offerings = category.Offering
						.Select(offering => new OfferingDto
						{
							Id = offering.Id,
							Name = offering.Name,
							Code = offering.Code,
							SupplierItemNumber = offering.SupplierItemNumber,
							PublicDescription = offering.PublicDescription,
							IsActive = offering.IsActive,
							IsObsolete = offering.IsObsolete
						}).ToList()
				}).ToList();

			// Create the result object to structure the response
			var result = new GroupWithCategoriesDto
			{
				GroupId = groupId,
				GroupName = groupType.Name,  // GroupType Name
				Categories = groupedByCategory  // Grouped Categories with Offerings
			};

			// Return the response with the structured data
			return new GenericRespond<GroupWithCategoriesDto>
			{
				Data = result,
				StatusCode = 200,
				Message = "Offerings grouped by categories under group type retrieved successfully"
			};
		}

		[HttpGet]
		public async Task<GenericRespond<IEnumerable<GroupDto>>> GetAll()
		{
			var data = await _repository.GetAllAsync();

			var list = data.Select(x => new GroupDto
			{
				Id = x.Id,
				Name = x.Name,
				Code = x.Code,
				IsAvailable = x.IsAvailable ?? false,
				GroupTypeName = x.GroupType.Name
			}).ToList();

			return new GenericRespond<IEnumerable<GroupDto>>
			{
				Data = list,
				StatusCode = 200,
				Message = "Groups retrieved successfully"
			};
		}

		[HttpGet("brief")]
		public async Task<GenericRespond<IEnumerable<BasicDetail>>> GetBrief()
		{
			var data = await _repository.GetAllAsync();

			var list = data.Select(x => new BasicDetail
			{
				Id = x.Id,
				Name = x.Code + " " + x.Name
			}).ToList();

			return new GenericRespond<IEnumerable<BasicDetail>>
			{
				Data = list,
				StatusCode = 200,
				Message = "Groups retrieved successfully"
			};
		}

		[HttpGet("{id}")]
		public async Task<GenericRespond<GroupCreateDto>> GetById(int id)
		{
			var group = await _repository.GetByIdAsync(id);
			if (group == null)
			{
				return new GenericRespond<GroupCreateDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Group not found"
				};
			}

			return new GenericRespond<GroupCreateDto>
			{
				Data = new GroupCreateDto()
				{
					Id = id,
					GroupTypeId = group.GroupTypeId,
					Name = group.Name,
					Code = group.Code,
					IsAvailable = group.IsAvailable ?? false,
				},
				Message = "Group retrieved successfully"
			};
		}


		[HttpPost]
		public async Task<GenericRespond<bool>> Create([FromBody] GroupCreateDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			var code = await GenerateUniqueCode();

			var group = new Group
			{
				GroupTypeId = dto.GroupTypeId ?? 0,
				Name = dto.Name,
				Code = code,
				IsAvailable = dto.IsAvailable,
				CreatedAt = DateTime.Now,
				CreatedById = user.UserId
			};

			await _repository.AddAsync(group);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Group created successfully"
			};
		}


		[HttpPut("{id}")]
		public async Task<GenericRespond<bool>> Update(int id, [FromBody] GroupCreateDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			var existingGroup = await _repository.GetByIdAsync(id);
			if (existingGroup == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 404,
					Message = "Group not found"
				};
			}

			var updatedGroup = new Group
			{
				GroupTypeId = dto.GroupTypeId ?? 0,
				Name = dto.Name,
				Code = existingGroup.Code,
				IsAvailable = dto.IsAvailable,
				UpdatedAt = DateTime.Now,
				UpdatedById = user.UserId
			};

			await _repository.UpdateAsync(id, updatedGroup);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Group updated successfully"
			};
		}

		private async Task<string> GenerateUniqueCode()
		{
			// Fetch all existing codes from the database
			var groups = await _repository.GetAllAsync();

			// Parse numeric codes and sort
			var existingCodes = groups
				.Select(g => int.TryParse(g.Code, out var code) ? code : (int?)null)
				.Where(c => c.HasValue && c.Value >= 1 && c.Value <= 99)
				.Select(c => c.Value)
				.ToHashSet();

			// Find the smallest available 2-digit number
			for (int i = 1; i <= 99; i++)
			{
				if (!existingCodes.Contains(i))
				{
					return i.ToString("D2");
				}
			}

			throw new InvalidOperationException("No available group codes. Limit of 99 reached.");
		}

	}
}