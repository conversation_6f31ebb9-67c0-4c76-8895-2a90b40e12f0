﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
    public interface IShowLocationHallRepository
    {
        Task<IEnumerable<ShowLocationHalls>> GetAllByLocationIdAsync(int locationId);
        Task<IEnumerable<ShowLocationHalls>> GetAllAsync();
        Task<ShowLocationHalls> GetByIdAsync(int hallId);
        Task AddAsync(ShowLocationHalls hall);
        Task UpdateAsync(int hallId, ShowLocationHalls updated);
    }
    public class ShowLocationHallRepository : IShowLocationHallRepository
    {
        private readonly GoodkeyContext _context;

        public ShowLocationHallRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<ShowLocationHalls>> GetAllAsync()
        {
            return await _context.ShowLocationHalls.ToListAsync();
        }

        public async Task<IEnumerable<ShowLocationHalls>> GetAllByLocationIdAsync(int locationId)
        {
            return await _context.ShowLocationHalls.Where(h => h.LocationId == locationId).ToListAsync();
        }

        public async Task<ShowLocationHalls> GetByIdAsync(int hallId)
        {
            return await _context.ShowLocationHalls
                .Include(h => h.Location)
                .FirstOrDefaultAsync(h => h.HallId == hallId);
        }

        public async Task AddAsync(ShowLocationHalls hall)
        {
            await _context.ShowLocationHalls.AddAsync(hall);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(int hallId, ShowLocationHalls updated)
        {
            var existing = await _context.ShowLocationHalls.FindAsync(hallId);
            if (existing == null) return;

            existing.HallName = updated.HallName;
            existing.HallCode = updated.HallCode;
            existing.HallStyle = updated.HallStyle;
            existing.HallFloorType = updated.HallFloorType;
            existing.BanquetCapacity = updated.BanquetCapacity;
            existing.HallWidth = updated.HallWidth;
            existing.HallLength = updated.HallLength;
            existing.OverheadHeight = updated.OverheadHeight;
            existing.HallArea = updated.HallArea;
            existing.IsElecOnFloor = updated.IsElecOnFloor;
            existing.IsElecOnCeiling = updated.IsElecOnCeiling;
            existing.HallSurface = updated.HallSurface;
            existing.HallCeilingHeight = updated.HallCeilingHeight;
            existing.AccessDoor = updated.AccessDoor;
            existing.LoadingDocks = updated.LoadingDocks;
            existing.HallBoothCount = updated.HallBoothCount;
            existing.UpdatedById = updated.UpdatedById;
            existing.UpdatedAt = DateTime.Now;

            if (updated.IsArchived == true && existing.IsArchived != true)
            {
                // Archiving now
                existing.IsArchived = true;
                existing.ArchivedAt = DateTime.Now;
                existing.ArchivedById = updated.ArchivedById;
            }
            else if (updated.IsArchived != true && existing.IsArchived == true)
            {
                // Unarchiving
                existing.IsArchived = false;
                existing.ArchivedAt = null;
                existing.ArchivedById = null;
            }


            await _context.SaveChangesAsync();
        }

    }

}
