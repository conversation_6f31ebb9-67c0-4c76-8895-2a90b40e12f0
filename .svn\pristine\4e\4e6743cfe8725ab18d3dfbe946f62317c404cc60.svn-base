﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class Category
    {
        public Category()
        {
            CategoryProperty = new HashSet<CategoryProperty>();
            Offering = new HashSet<Offering>();
        }

        public int Id { get; set; }
        public int GroupId { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public int? DisplayOrder { get; set; }
        public string ImagePath { get; set; }
        public bool? IsSoldByQ { get; set; }
        public bool? IsInternalProduct { get; set; }
        public bool? IsAvailable { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? CreatedById { get; set; }
        public int? UpdatedById { get; set; }
        public string Description { get; set; }

        public virtual AuthUser CreatedBy { get; set; }
        public virtual Group Group { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
        public virtual ICollection<CategoryProperty> CategoryProperty { get; set; }
        public virtual ICollection<Offering> Offering { get; set; }
    }
}