using goodkey_cms.Infrastructure.Extensions;
using goodkey_cms.Repositories;
using goodkey_common.DTO;
using goodkey_common.DTO.Ground;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class GroundConditionsController : ControllerBase
	{
		private readonly IGroundConditionRepository _repo;
		private readonly IUserRepository _userRepo;

		public GroundConditionsController(IGroundConditionRepository repo, IUserRepository userRepo)
		{
			_repo = repo;
			_userRepo = userRepo;
		}

		[HttpGet]
		public GenericRespond<IEnumerable<GroundConditionDto>> GetAll()
		{
			var data = _repo.GetAll().OrderByDescending(x => x.Id).Select(x => new GroundConditionDto
			{
				Id = x.Id,
				Name = x.Name,
				Text = x.Text,
				EnteredDate = x.EnteredDate,
				EnteredBy = x.EnteredBy
			});
			return new()
			{
				Data = data,
				StatusCode = 200,
				Message = "Ground conditions retrieved successfully"
			};
		}

		[HttpGet("{id}")]
		public GenericRespond<GroundConditionDto> GetById(int id)
		{
			var x = _repo.GetById(id);
			if (x == null)
				return new() { Data = null, StatusCode = 404, Message = "Ground condition not found" };
			var dto = new GroundConditionDto
			{
				Id = x.Id,
				Name = x.Name,
				Text = x.Text,
				EnteredDate = x.EnteredDate,
				EnteredBy = x.EnteredBy
			};
			return new() { Data = dto, StatusCode = 200, Message = "Ground condition found" };
		}

		[HttpPost]
		public GenericRespond<GroundConditionDto> Create([FromBody] CreateGroundConditionDto model)
		{
			var username = Request.HttpContext.GetUsername();
			var user = _userRepo.GetUserByUsername(username);

			var entity = new GroundConditions
			{
				Name = model.Name,
				Text = model.Text,
				EnteredDate = DateTime.Now,
				EnteredBy = user?.UserId
			};
			var created = _repo.Add(entity);
			var dto = new GroundConditionDto
			{
				Id = created.Id,
				Name = created.Name,
				Text = created.Text,
				EnteredDate = created.EnteredDate,
				EnteredBy = created.EnteredBy
			};
			return new() { Data = dto, StatusCode = 201, Message = "Ground condition created successfully" };
		}

		[HttpPatch("{id}")]
		public GenericRespond<GroundConditionDto> Update(int id, [FromBody] UpdateGroundConditionDto model)
		{
			var entity = _repo.GetById(id);
			if (entity == null)
				return new() { Data = null, StatusCode = 404, Message = "Ground condition not found" };

			var username = Request.HttpContext.GetUsername();
			var user = _userRepo.GetUserByUsername(username);

			entity.Name = model.Name;
			entity.Text = model.Text;
			entity.EnteredDate = DateTime.Now;
			entity.EnteredBy = user?.UserId;
			var updated = _repo.Update(entity);
			var dto = new GroundConditionDto
			{
				Id = updated.Id,
				Name = updated.Name,
				Text = updated.Text,
				EnteredDate = updated.EnteredDate,
				EnteredBy = updated.EnteredBy
			};
			return new() { Data = dto, StatusCode = 200, Message = "Ground condition updated successfully" };
		}

		[HttpDelete("{id}")]
		public GenericRespond<bool> Delete(int id)
		{
			var result = _repo.Delete(id);
			return new()
			{
				Data = result,
				StatusCode = result ? 200 : 404,
				Message = result ? "Ground condition deleted successfully" : "Ground condition not found"
			};
		}
	}
}