﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class ShowsPromoters
    {
        public ShowsPromoters()
        {
            ShowsPromotersTaxes = new HashSet<ShowsPromotersTaxes>();
            Contact = new HashSet<Contact>();
            ContactNavigation = new HashSet<Contact>();
        }

        public int Id { get; set; }
        public int ShowId { get; set; }
        public int CompanyId { get; set; }
        public bool? ShowSubcontact { get; set; }
        public bool? FloorPlanRequired { get; set; }

        public virtual Company Company { get; set; }
        public virtual Shows Show { get; set; }
        public virtual ICollection<ShowsPromotersTaxes> ShowsPromotersTaxes { get; set; }

        public virtual ICollection<Contact> Contact { get; set; }
        public virtual ICollection<Contact> ContactNavigation { get; set; }
    }
}