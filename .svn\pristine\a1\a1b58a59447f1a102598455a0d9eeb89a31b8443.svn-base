﻿namespace goodkey_cms.DTO.Category
{
	public class CategorySelectionDto
	{
		public int? Selection1 { get; set; }
		public int? Selection2 { get; set; }
	}

	public class CategoryDto
	{
		public int Id { get; set; }
		public string? GroupType { get; set; }
		public string? Name { get; set; }
		public string? Code { get; set; }
		public int? DisplayOrder { get; set; }
		public string? ImagePath { get; set; }
		public bool? IsSoldByQ { get; set; }
		public bool? IsInternalProduct { get; set; }
		public bool? IsAvailable { get; set; }
	}

	public class CategoryDetailDto
	{
		public int Id { get; set; }
		public int? GroupTypeId { get; set; }
		public string? Name { get; set; }
		public string? Code { get; set; }
		public int? DisplayOrder { get; set; }
		public string? ImagePath { get; set; }
		public bool? IsSoldByQ { get; set; }
		public bool? IsInternalProduct { get; set; }
		public bool? IsAvailable { get; set; }
		public int? Selection1 { get; set; }
		public int? Selection2 { get; set; }
	}


	public class CategoryCreateDto
	{
		public int? GroupTypeId { get; set; }
		public string? Name { get; set; }
		public int? DisplayOrder { get; set; }
		public IFormFile? Image { get; set; }
		public bool? IsSoldByQ { get; set; }
		public bool? IsInternalProduct { get; set; }
		public bool? IsAvailable { get; set; }
	}

	public class CategoryUpdateDto : CategoryCreateDto
	{
		public int? Id { get; set; }
		public string? ImagePath { get; set; }
	}
}
