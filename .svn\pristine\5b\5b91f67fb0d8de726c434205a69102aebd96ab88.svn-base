﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class DocCategory
    {
        public DocCategory()
        {
            DocCategoryType = new HashSet<DocCategoryType>();
            ShowDocs = new HashSet<ShowDocs>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public int SectionId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public virtual Sections Section { get; set; }
        public virtual ICollection<DocCategoryType> DocCategoryType { get; set; }
        public virtual ICollection<ShowDocs> ShowDocs { get; set; }
    }
}