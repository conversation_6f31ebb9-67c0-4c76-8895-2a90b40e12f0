﻿

namespace goodkey_public.Dto.Menu
{
    public class MenuData
    {

        public int? ParentId { get; set; }
        public Dictionary<string, string> Name { get; set; }
        public Dictionary<string, string> Description { get; set; }
        public string? Url { get; set; }
        public int DisplayOrder { get; set; }
        public string? PermissionKey { get; set; }
        public string? IconName { get; set; }
        public string? Target { get; set; }
        public bool? IsVisible { get; set; }
        public IFormFile? Image { get; set; }
        public string? ImagePath { get; set; }
    }


}
