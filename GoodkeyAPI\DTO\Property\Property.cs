﻿namespace goodkey_cms.DTO.Property
{
	// PropertyDto.cs
	public class PropertyDto : PropertyCreateDto
	{
		public int Id { get; set; }
		public string? Code { get; set; }
	}

	// PropertyCreateDto.cs
	public class PropertyCreateDto
	{
		public string? Name { get; set; }
		public string? Description { get; set; }
	}

	public class PropertyDetail : PropertyDto
	{
		public PropertyOptionDto[]? Option { get; set; }
	}

}
