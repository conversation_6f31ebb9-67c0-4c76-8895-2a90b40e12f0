-- Default Contact Types for Exhibitor Import
-- This script ensures common contact types exist for Excel import mapping

-- =====================================================
-- Insert Default Contact Types (if they don't exist)
-- =====================================================

-- Primary Contact (main contact for the company)
INSERT INTO "ContactType" ("Code", "Name")
SELECT 'PRIMARY', 'Primary Contact'
WHERE NOT EXISTS (SELECT 1 FROM "ContactType" WHERE "Code" = 'PRIMARY');

-- Manager/Executive level contact
INSERT INTO "ContactType" ("Code", "Name")
SELECT 'MANAGER', 'Manager'
WHERE NOT EXISTS (SELECT 1 FROM "ContactType" WHERE "Code" = 'MANAGER');

-- Sales representative
INSERT INTO "ContactType" ("Code", "Name")
SELECT 'SALES', 'Sales Representative'
WHERE NOT EXISTS (SELECT 1 FROM "ContactType" WHERE "Code" = 'SALES');

-- Marketing contact
INSERT INTO "ContactType" ("Code", "Name")
SELECT 'MARKETING', 'Marketing Contact'
WHERE NOT EXISTS (SELECT 1 FROM "ContactType" WHERE "Code" = 'MARKETING');

-- Technical contact
INSERT INTO "ContactType" ("Code", "Name")
SELECT 'TECHNICAL', 'Technical Contact'
WHERE NOT EXISTS (SELECT 1 FROM "ContactType" WHERE "Code" = 'TECHNICAL');

-- Administrative contact
INSERT INTO "ContactType" ("Code", "Name")
SELECT 'ADMIN', 'Administrative Contact'
WHERE NOT EXISTS (SELECT 1 FROM "ContactType" WHERE "Code" = 'ADMIN');

-- Billing/Finance contact
INSERT INTO "ContactType" ("Code", "Name")
SELECT 'BILLING', 'Billing Contact'
WHERE NOT EXISTS (SELECT 1 FROM "ContactType" WHERE "Code" = 'BILLING');

-- Operations contact
INSERT INTO "ContactType" ("Code", "Name")
SELECT 'OPERATIONS', 'Operations Contact'
WHERE NOT EXISTS (SELECT 1 FROM "ContactType" WHERE "Code" = 'OPERATIONS');

-- Support contact
INSERT INTO "ContactType" ("Code", "Name")
SELECT 'SUPPORT', 'Support Contact'
WHERE NOT EXISTS (SELECT 1 FROM "ContactType" WHERE "Code" = 'SUPPORT');

-- General/Other contact
INSERT INTO "ContactType" ("Code", "Name")
SELECT 'GENERAL', 'General Contact'
WHERE NOT EXISTS (SELECT 1 FROM "ContactType" WHERE "Code" = 'GENERAL');

-- =====================================================
-- Create a view for easy contact type lookup during import
-- =====================================================

CREATE OR REPLACE VIEW "ContactTypeLookup" AS
SELECT 
    "Id",
    "Code",
    "Name",
    -- Create variations for flexible matching during Excel import
    LOWER("Name") as "NameLower",
    LOWER("Code") as "CodeLower",
    -- Common variations that users might type in Excel
    CASE 
        WHEN "Code" = 'PRIMARY' THEN ARRAY['primary', 'main', 'principal', 'primary contact', 'main contact']
        WHEN "Code" = 'MANAGER' THEN ARRAY['manager', 'mgr', 'executive', 'director', 'supervisor']
        WHEN "Code" = 'SALES' THEN ARRAY['sales', 'sales rep', 'sales representative', 'salesperson', 'account manager']
        WHEN "Code" = 'MARKETING' THEN ARRAY['marketing', 'marketing contact', 'promo', 'promotion']
        WHEN "Code" = 'TECHNICAL' THEN ARRAY['technical', 'tech', 'technical contact', 'engineer', 'it']
        WHEN "Code" = 'ADMIN' THEN ARRAY['admin', 'administrative', 'administrator', 'office']
        WHEN "Code" = 'BILLING' THEN ARRAY['billing', 'finance', 'accounting', 'accounts', 'financial']
        WHEN "Code" = 'OPERATIONS' THEN ARRAY['operations', 'ops', 'operational', 'logistics']
        WHEN "Code" = 'SUPPORT' THEN ARRAY['support', 'customer service', 'help', 'assistance']
        WHEN "Code" = 'GENERAL' THEN ARRAY['general', 'other', 'misc', 'miscellaneous', 'contact']
        ELSE ARRAY[LOWER("Name")]
    END as "SearchTerms"
FROM "ContactType";

-- =====================================================
-- Function to find contact type by flexible text matching
-- This will be used during Excel import validation
-- =====================================================

CREATE OR REPLACE FUNCTION find_contact_type_by_text(search_text TEXT)
RETURNS TABLE (
    contact_type_id INTEGER,
    contact_type_code VARCHAR(20),
    contact_type_name VARCHAR(100),
    match_confidence INTEGER
) AS $$
BEGIN
    -- Return exact matches first (highest confidence)
    RETURN QUERY
    SELECT 
        ct."Id",
        ct."Code",
        ct."Name",
        100 as match_confidence
    FROM "ContactType" ct
    WHERE LOWER(TRIM(search_text)) = LOWER(ct."Name")
       OR LOWER(TRIM(search_text)) = LOWER(ct."Code")
    LIMIT 1;
    
    -- If no exact match, try partial matches
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT 
            ctl."Id",
            ctl."Code", 
            ctl."Name",
            80 as match_confidence
        FROM "ContactTypeLookup" ctl
        WHERE LOWER(TRIM(search_text)) = ANY(ctl."SearchTerms")
        LIMIT 1;
    END IF;
    
    -- If still no match, try fuzzy matching
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT 
            ct."Id",
            ct."Code",
            ct."Name",
            60 as match_confidence
        FROM "ContactType" ct
        WHERE LOWER(ct."Name") LIKE '%' || LOWER(TRIM(search_text)) || '%'
           OR LOWER(TRIM(search_text)) LIKE '%' || LOWER(ct."Name") || '%'
        ORDER BY LENGTH(ct."Name") ASC
        LIMIT 1;
    END IF;
    
    -- If no matches found, return default (PRIMARY)
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT 
            ct."Id",
            ct."Code",
            ct."Name",
            0 as match_confidence
        FROM "ContactType" ct
        WHERE ct."Code" = 'PRIMARY'
        LIMIT 1;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- Test the contact type lookup function
-- =====================================================

-- Example usage:
-- SELECT * FROM find_contact_type_by_text('Manager');
-- SELECT * FROM find_contact_type_by_text('Sales Rep');
-- SELECT * FROM find_contact_type_by_text('Primary');
-- SELECT * FROM find_contact_type_by_text('Unknown Type'); -- Should return PRIMARY with confidence 0

-- =====================================================
-- Comments for documentation
-- =====================================================

COMMENT ON VIEW "ContactTypeLookup" IS 'Enhanced contact type lookup with search terms for flexible Excel import matching';
COMMENT ON FUNCTION find_contact_type_by_text(TEXT) IS 'Finds contact type by flexible text matching with confidence scoring for Excel import validation';
