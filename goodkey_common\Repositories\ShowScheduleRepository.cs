using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
    public interface IShowScheduleRepository
    {
        IEnumerable<ShowSchedules> GetAll();
        ShowSchedules? GetById(int id);
        IEnumerable<ShowSchedules> GetByShowId(int showId);
        ShowSchedules? Add(ShowSchedules showSchedule);
        ShowSchedules? Update(ShowSchedules showSchedule);
        bool Delete(int id);
        bool ToggleConfirmed(int id);
        bool ToggleApplyToServiceForm(int id);
    }

    public class ShowScheduleRepository : IShowScheduleRepository
    {
        private readonly GoodkeyContext _context;

        public ShowScheduleRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public IEnumerable<ShowSchedules> GetAll()
        {
            return _context.ShowSchedules
                .Include(ss => ss.CreatedByNavigation)
                .Include(ss => ss.Show)
                .OrderBy(ss => ss.ShowScheduleDate)
                .ThenBy(ss => ss.TimeStart);
        }

        public ShowSchedules? GetById(int id)
        {
            return _context.ShowSchedules
                .Include(ss => ss.CreatedByNavigation)
                .Include(ss => ss.Show)
                .FirstOrDefault(ss => ss.Id == id);
        }

        public IEnumerable<ShowSchedules> GetByShowId(int showId)
        {
            return _context.ShowSchedules
                .Include(ss => ss.CreatedByNavigation)
                .Include(ss => ss.Show)
                .Where(ss => ss.ShowId == showId)
                .OrderBy(ss => ss.ShowScheduleDate)
                .ThenBy(ss => ss.TimeStart);
        }

        public ShowSchedules? Add(ShowSchedules showSchedule)
        {
            _context.ShowSchedules.Add(showSchedule);
            _context.SaveChanges();
            return showSchedule;
        }

        public ShowSchedules? Update(ShowSchedules showSchedule)
        {
            var existingSchedule = _context.ShowSchedules.Find(showSchedule.Id);
            if (existingSchedule == null)
                return null;

            _context.Entry(existingSchedule).CurrentValues.SetValues(showSchedule);
            _context.SaveChanges();
            return showSchedule;
        }

        public bool Delete(int id)
        {
            var showSchedule = _context.ShowSchedules.Find(id);
            if (showSchedule == null)
                return false;

            _context.ShowSchedules.Remove(showSchedule);
            _context.SaveChanges();
            return true;
        }

        public bool ToggleConfirmed(int id)
        {
            var showSchedule = _context.ShowSchedules.Find(id);
            if (showSchedule == null)
                return false;

            showSchedule.ShowScheduleConfirmed = !showSchedule.ShowScheduleConfirmed;
            _context.SaveChanges();
            return true;
        }

        public bool ToggleApplyToServiceForm(int id)
        {
            var showSchedule = _context.ShowSchedules.Find(id);
            if (showSchedule == null)
                return false;

            showSchedule.ApplyScheduleToServiceForm = !showSchedule.ApplyScheduleToServiceForm;
            _context.SaveChanges();
            return true;
        }
    }
} 