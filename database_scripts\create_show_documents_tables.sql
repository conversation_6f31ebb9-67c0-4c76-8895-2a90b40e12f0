-- Show Documents Management Tables
-- This script creates the tables for managing documents associated with shows

-- Step 1: Create DocumentType table (if it doesn't exist)
-- This table stores different types of documents (e.g., Floor Plan, Contract, Insurance, etc.)
CREATE TABLE IF NOT EXISTS "DocumentType" (
    "Id" SERIAL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "Description" TEXT,
    "Code" VARCHAR(20) UNIQUE,
    "IsActive" BOOLEAN DEFAULT true,
    "DisplayOrder" INTEGER DEFAULT 0,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "CreatedById" INTEGER,
    "UpdatedAt" TIMESTAMP,
    "UpdatedById" INTEGER,
    
    CONSTRAINT "FK_DocumentType_CreatedBy" FOREIGN KEY ("CreatedById") REFERENCES "AuthUser"("UserId"),
    CONSTRAINT "FK_DocumentType_UpdatedBy" FOREIGN KEY ("UpdatedById") REFERENCES "AuthUser"("UserId")
);

-- Step 2: Create ShowDocuments table
-- This table stores documents associated with shows
CREATE TABLE "ShowDocuments" (
    "Id" SERIAL PRIMARY KEY,
    "ShowId" INTEGER NOT NULL,
    "DocumentTypeId" INTEGER NOT NULL,
    "Name" VARCHAR(255) NOT NULL,
    "Description" TEXT,
    "FileName" VARCHAR(255) NOT NULL,
    "OriginalFileName" VARCHAR(255) NOT NULL,
    "FilePath" VARCHAR(500) NOT NULL,
    "FileSize" BIGINT,
    "MimeType" VARCHAR(100),
    "IsRequired" BOOLEAN DEFAULT false,
    "IsPublic" BOOLEAN DEFAULT false,
    "Version" INTEGER DEFAULT 1,
    "Status" VARCHAR(50) DEFAULT 'Active', -- Active, Archived, Pending, Rejected
    "UploadedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "UploadedById" INTEGER,
    "UpdatedAt" TIMESTAMP,
    "UpdatedById" INTEGER,
    "ArchivedAt" TIMESTAMP,
    "ArchivedById" INTEGER,
    "IsArchived" BOOLEAN DEFAULT false,
    
    -- Foreign Key Constraints
    CONSTRAINT "FK_ShowDocuments_Show" FOREIGN KEY ("ShowId") REFERENCES "Shows"("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_ShowDocuments_DocumentType" FOREIGN KEY ("DocumentTypeId") REFERENCES "DocumentType"("Id"),
    CONSTRAINT "FK_ShowDocuments_UploadedBy" FOREIGN KEY ("UploadedById") REFERENCES "AuthUser"("UserId"),
    CONSTRAINT "FK_ShowDocuments_UpdatedBy" FOREIGN KEY ("UpdatedById") REFERENCES "AuthUser"("UserId"),
    CONSTRAINT "FK_ShowDocuments_ArchivedBy" FOREIGN KEY ("ArchivedById") REFERENCES "AuthUser"("UserId")
);

-- Step 3: Create indexes for better performance
CREATE INDEX "IX_ShowDocuments_ShowId" ON "ShowDocuments"("ShowId");
CREATE INDEX "IX_ShowDocuments_DocumentTypeId" ON "ShowDocuments"("DocumentTypeId");
CREATE INDEX "IX_ShowDocuments_Status" ON "ShowDocuments"("Status");
CREATE INDEX "IX_ShowDocuments_IsArchived" ON "ShowDocuments"("IsArchived");
CREATE INDEX "IX_ShowDocuments_UploadedAt" ON "ShowDocuments"("UploadedAt");

-- Step 4: Insert default document types
INSERT INTO "DocumentType" ("Name", "Description", "Code", "DisplayOrder") VALUES
('Floor Plan', 'Show floor plan and layout documents', 'FLOOR_PLAN', 1),
('Contract', 'Show contracts and agreements', 'CONTRACT', 2),
('Insurance', 'Insurance documents and certificates', 'INSURANCE', 3),
('Permit', 'Permits and licenses required for the show', 'PERMIT', 4),
('Marketing Material', 'Promotional and marketing documents', 'MARKETING', 5),
('Technical Specification', 'Technical requirements and specifications', 'TECH_SPEC', 6),
('Safety Document', 'Safety protocols and emergency procedures', 'SAFETY', 7),
('Vendor Agreement', 'Vendor contracts and agreements', 'VENDOR', 8),
('Financial Document', 'Invoices, receipts, and financial records', 'FINANCIAL', 9),
('Other', 'Miscellaneous documents', 'OTHER', 99);

-- Step 5: Create ShowDocumentVersions table (optional - for version history)
CREATE TABLE "ShowDocumentVersions" (
    "Id" SERIAL PRIMARY KEY,
    "ShowDocumentId" INTEGER NOT NULL,
    "Version" INTEGER NOT NULL,
    "FileName" VARCHAR(255) NOT NULL,
    "OriginalFileName" VARCHAR(255) NOT NULL,
    "FilePath" VARCHAR(500) NOT NULL,
    "FileSize" BIGINT,
    "MimeType" VARCHAR(100),
    "UploadedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "UploadedById" INTEGER,
    "ChangeNote" TEXT,
    
    CONSTRAINT "FK_ShowDocumentVersions_ShowDocument" FOREIGN KEY ("ShowDocumentId") REFERENCES "ShowDocuments"("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_ShowDocumentVersions_UploadedBy" FOREIGN KEY ("UploadedById") REFERENCES "AuthUser"("UserId"),
    CONSTRAINT "UQ_ShowDocumentVersions_Version" UNIQUE ("ShowDocumentId", "Version")
);

-- Step 6: Create index for document versions
CREATE INDEX "IX_ShowDocumentVersions_ShowDocumentId" ON "ShowDocumentVersions"("ShowDocumentId");
CREATE INDEX "IX_ShowDocumentVersions_Version" ON "ShowDocumentVersions"("Version");

-- Step 7: Add comments for documentation
COMMENT ON TABLE "DocumentType" IS 'Defines different types of documents that can be associated with shows';
COMMENT ON TABLE "ShowDocuments" IS 'Stores documents associated with shows including file information and metadata';
COMMENT ON TABLE "ShowDocumentVersions" IS 'Maintains version history of show documents';

COMMENT ON COLUMN "ShowDocuments"."IsRequired" IS 'Indicates if this document type is required for the show';
COMMENT ON COLUMN "ShowDocuments"."IsPublic" IS 'Indicates if this document can be viewed by public/exhibitors';
COMMENT ON COLUMN "ShowDocuments"."Status" IS 'Document status: Active, Archived, Pending, Rejected';
COMMENT ON COLUMN "ShowDocuments"."Version" IS 'Current version number of the document';
