using goodkey_cms.DTO;

namespace goodkey_cms.DTO.Schedule
{
    public class ScheduleDetail
    {
        public int Id { get; set; }
        public string Code { get; set; } = null!;
        public string Name { get; set; } = null!;
        public string? Description { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; }
        public BasicDetail? CreatedBy { get; set; }
        public BasicDetail? UpdatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class BasicScheduleDetail
    {
        public int Id { get; set; }
        public string Code { get; set; } = null!;
        public string Name { get; set; } = null!;
        public string? Description { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
    }
}
