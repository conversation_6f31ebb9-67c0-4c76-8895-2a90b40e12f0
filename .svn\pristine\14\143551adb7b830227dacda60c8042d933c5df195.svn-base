﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
	public interface IGroupRepository
	{
		Task<IEnumerable<Group>> GetAllAsync();
		Task<Group> GetByIdAsync(int id);
		Task AddAsync(Group location);
		Task UpdateAsync(int id, Group updated);
		Task<Group> GetAll(int groupId);
	}

	public class GroupRepository : IGroupRepository
	{
		private readonly GoodkeyContext _context;

		public GroupRepository(GoodkeyContext context)
		{
			_context = context;
		}
		public async Task<Group> GetAll(int groupId)
		{
			return await _context.Group.Include(x => x.Category)
				.ThenInclude(x => x.Offering)
				.ThenInclude(x => x.OfferingTax)
				.ThenInclude(ot => ot.TaxType).FirstOrDefaultAsync(x => x.Id == groupId);
		}

		public async Task<IEnumerable<Group>> GetAllAsync()
		{
			return await _context.Group.Include(x => x.GroupType).ToListAsync();
		}

		public async Task<Group> GetByIdAsync(int id)
		{
			return await _context.Group.Include(x => x.GroupType).FirstOrDefaultAsync(x => x.Id == id);
		}

		public async Task AddAsync(Group category)
		{
			await _context.Group.AddAsync(category);
			await _context.SaveChangesAsync();
		}

		public async Task UpdateAsync(int id, Group updated)
		{
			var existing = await _context.Group.FindAsync(id);
			if (existing == null) return;

			existing.Name = updated.Name;
			existing.IsAvailable = updated.IsAvailable;
			existing.GroupTypeId = updated.GroupTypeId;
			existing.UpdatedById = updated.UpdatedById;
			existing.UpdatedAt = updated.UpdatedAt;

			await _context.SaveChangesAsync();
		}
	}
}
