-- This script updates the "ShowsPromoters" table to replace the single
-- BilledToContactId with a list and adds a new list for ShowManagersContactId.

-- Step 1: Add new columns to store arrays of contact IDs.
-- Using INTEGER[] allows storing a list of numbers.
ALTER TABLE "ShowsPromoters"
ADD COLUMN "BilledToContactIds" INTEGER[],
ADD COLUMN "ShowManagersContactIds" INTEGER[];

-- Step 2: Migrate existing data from the old BilledToContactId column
-- to the new BilledToContactIds column by wrapping the single ID in an array.
UPDATE "ShowsPromoters"
SET "BilledToContactIds" = ARRAY["BilledToContactId"]
WHERE "BilledToContactId" IS NOT NULL;

-- Step 3: Remove the old BilledToContactId column.
-- The associated foreign key constraint will be dropped automatically.
ALTER TABLE "ShowsPromoters"
DROP COLUMN "BilledToContactId";

-- Note: The C# model (ShowsPromoters.cs) will need to be updated to reflect
-- these database changes. The BilledToContactId property should be changed
-- to a List<int> or int[] and a new ShowManagersContactIds property should be added.
