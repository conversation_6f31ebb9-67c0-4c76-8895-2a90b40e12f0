﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace goodkey_common.Repositories
{
	public interface IOfferingTaxRepository
	{
		Task<IEnumerable<OfferingTax>> GetByOfferingIdAsync(int offeringId);
		Task AddRangeAsync(IEnumerable<OfferingTax> offeringTaxes);
		Task DeleteByOfferingIdAsync(int offeringId);
	}

	public class OfferingTaxRepository : IOfferingTaxRepository
	{
		private readonly GoodkeyContext _context;

		public OfferingTaxRepository(GoodkeyContext context)
		{
			_context = context;
		}

		public async Task<IEnumerable<OfferingTax>> GetByOfferingIdAsync(int offeringId)
		{
			return await _context.OfferingTax.Include(x => x.TaxType).Where(ot => ot.OfferingId == offeringId).ToListAsync();
		}

		public async Task AddRangeAsync(IEnumerable<OfferingTax> offeringTaxes)
		{
			await _context.OfferingTax.AddRangeAsync(offeringTaxes);
			await _context.SaveChangesAsync();
		}

		public async Task DeleteByOfferingIdAsync(int offeringId)
		{
			var taxes = await _context.OfferingTax
				.Where(ot => ot.OfferingId == offeringId)
				.ToListAsync();

			if (taxes.Any())
			{
				_context.OfferingTax.RemoveRange(taxes);
				await _context.SaveChangesAsync();
			}
		}
	}
}
