using goodkey_cms.DTO.Document;
using goodkey_cms.Infrastructure.Extensions;
using goodkey_cms.Repositories;
using goodkey_common.DTO;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class DocumentController : ControllerBase
	{
		private readonly IDocumentRepository _repo;


		public DocumentController(IDocumentRepository repo)
		{
			_repo = repo;

		}
		#region Document FileType Endpoints

		[HttpGet("filetypes")]
		public GenericRespond<IEnumerable<DocumentFileTypeListDto>> GetAllFileTypes()
		{
			var fileTypes = _repo.GetAllFileTypes();

			return new GenericRespond<IEnumerable<DocumentFileTypeListDto>>
			{
				Data = fileTypes.Select(ft => new DocumentFileTypeListDto
				{
					Id = ft.Id,
					Name = ft.Name,
					ExtensionCode = ft.ExtensionCode,
					Extension = ft.Extension,
					IsImage = ft.IsImage
				})
			};
		}

		[HttpGet("filetypes/{id:int}")]
		public GenericRespond<DocumentFileTypeDetailDto> GetFileType(int id)
		{
			var fileType = _repo.GetFileType(id);
			if (fileType == null)
			{
				return new GenericRespond<DocumentFileTypeDetailDto>
				{
					StatusCode = 404,
					Message = "File type not found"
				};
			}

			return new GenericRespond<DocumentFileTypeDetailDto>
			{
				Data = new DocumentFileTypeDetailDto
				{
					Id = fileType.Id,
					Name = fileType.Name,
					ExtensionCode = fileType.ExtensionCode,
					Extension = fileType.Extension,
					IsAvailable = fileType.IsAvailable ?? true,
					IsImage = fileType.IsImage,
					CreatedAt = fileType.CreatedAt,
					CreatedBy = fileType.CreatedBy?.Username ?? "System",
					UpdatedAt = fileType.UpdatedAt,
					UpdatedBy = fileType.UpdatedBy?.Username
				}
			};
		}

		[HttpPost("filetypes")]
		public GenericRespond<int> CreateFileType([FromBody] CreateDocumentFileTypeDto dto)
		{
			// Validate the extension code format (must be 2 uppercase letters)
			if (string.IsNullOrEmpty(dto.ExtensionCode) || dto.ExtensionCode.Length > 3 || !dto.ExtensionCode.All(char.IsLetter) || dto.ExtensionCode != dto.ExtensionCode.ToUpper())
			{
				return new GenericRespond<int>
				{
					StatusCode = 400,
					Message = "Extension code must be exactly 3 uppercase letters (A-Z)"
				};
			}

			var username = Request.HttpContext.GetUsername() ?? "System";

			try
			{
				var id = _repo.CreateFileType(dto.Name, dto.ExtensionCode, dto.Extension, dto.IsImage, username);

				if (id == 0)
				{
					return new GenericRespond<int>
					{
						StatusCode = 400,
						Message = "Failed to create file type"
					};
				}

				return new GenericRespond<int>
				{
					Data = id,
					Message = "File type created successfully"
				};
			}
			catch (Exception ex)
			{
				return new GenericRespond<int>
				{
					StatusCode = 500,
					Message = $"Error creating file type: {ex.InnerException?.Message ?? ex.Message}"
				};
			}
		}

		[HttpPut("filetypes/{id:int}")]
		public GenericRespond<bool> UpdateFileType(int id, [FromBody] UpdateDocumentFileTypeDto dto)
		{
			// Validate the extension code format (must be 2 uppercase letters)
			if (string.IsNullOrEmpty(dto.ExtensionCode) || dto.ExtensionCode.Length > 3 || !dto.ExtensionCode.All(char.IsLetter) || dto.ExtensionCode != dto.ExtensionCode.ToUpper())
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 400,
					Message = "Extension code must be exactly 3 uppercase letters (A-Z)"
				};
			}

			var username = Request.HttpContext.GetUsername() ?? "System";

			try
			{
				var success = _repo.UpdateFileType(id, dto.Name, dto.ExtensionCode, dto.Extension, dto.IsImage, username);

				return new GenericRespond<bool>
				{
					Data = success,
					StatusCode = success ? 200 : 400,
					Message = success ? "File type updated successfully" : "Failed to update file type"
				};
			}
			catch (Exception ex)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 500,
					Message = $"Error updating file type: {ex.InnerException?.Message ?? ex.Message}"
				};
			}
		}

		[HttpDelete("filetypes/{id:int}")]
		public GenericRespond<bool> DeleteFileType(int id)
		{
			try
			{
				var success = _repo.DeleteFileType(id);

				return new GenericRespond<bool>
				{
					Data = success,
					StatusCode = success ? 200 : 400,
					Message = success ? "File type deleted successfully" : "Failed to delete file type. It may be in use."
				};
			}
			catch (Exception ex)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 500,
					Message = $"Error deleting file type: {ex.InnerException?.Message ?? ex.Message}"
				};
			}
		}

		#endregion








	}
}
