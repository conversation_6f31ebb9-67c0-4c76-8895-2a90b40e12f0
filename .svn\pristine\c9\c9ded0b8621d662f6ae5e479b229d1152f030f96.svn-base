﻿using goodkey_cms.DTO.Offering;
using goodkey_cms.Services;
using goodkey_common.DTO;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;
using FileType = goodkey_cms.Services.FileType;
using StorageService = goodkey_cms.Services.StorageService;
using Visibility = goodkey_cms.Services.Visibility;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class OfferingController : ControllerBase
	{
		private readonly IOfferingRepository _offeringRepository;
		private readonly IOfferingTaxRepository _offeringTaxRepository;
		private readonly AuthService _authService;
		private readonly StorageService _storageService;

		public OfferingController(
			IOfferingRepository offeringRepository,
			IOfferingTaxRepository offeringTaxRepository,
			AuthService authService,
			StorageService storageService)
		{
			_offeringRepository = offeringRepository;
			_offeringTaxRepository = offeringTaxRepository;
			_authService = authService;
			_storageService = storageService;
		}
		// Display in datatable
		[HttpGet("{groupId}/getAll")]
		public async Task<GenericRespond<IEnumerable<OfferingDto>>> GetAll(int groupId)
		{
			var offerings = await _offeringRepository.GetAllAsync(groupId);
			var list = offerings.Select(o => new OfferingDto
			{
				Id = o.Id,
				Name = o.Name,
				Code = o.Code,
				SupplierItemNumber = o.SupplierItemNumber,
				PublicDescription = o.PublicDescription,
				IsActive = o.IsActive,
				IsObsolete = o.IsObsolete
			}).ToList();

			return new GenericRespond<IEnumerable<OfferingDto>>
			{
				Data = list,
				StatusCode = 200,
				Message = "Categories retrieved successfully"
			};
		}

		[HttpGet("{id}")]
		public async Task<GenericRespond<OfferingDetailDto>> GetById(int id)
		{
			var offering = await _offeringRepository.GetByIdAsync(id);
			if (offering == null)
			{
				return new GenericRespond<OfferingDetailDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Item not found"
				};
			}

			var taxes = await _offeringTaxRepository.GetByOfferingIdAsync(id);

			var offeringDetailDto = new OfferingDetailDto
			{
				Id = offering.Id,
				CategoryId = offering.CategoryId,
				GroupTypeId = offering.GroupTypeId,
				Name = offering.Name,
				Code = offering.Code,
				SupplierItemNumber = offering.SupplierItemNumber,
				PublicDescription = offering.PublicDescription,
				InternalDescription = offering.InternalDescription,
				DisplayOrder = offering.DisplayOrder,
				UnitChargedId = offering.UnitChargedId,
				IsUnitTypeEach = offering.IsUnitTypeEach,
				IsAddOn = offering.IsAddOn,
				IsForSmOnly = offering.IsForSmOnly,
				IsInternalOnly = offering.IsInternalOnly,
				ImagePath = offering.Image,
				IsActive = offering.IsActive,
				IsObsolete = offering.IsObsolete,
				CreatedAt = offering.CreatedAt,
				UpdatedAt = offering.UpdatedAt,
				CreatedById = offering.CreatedById,
				UpdatedById = offering.UpdatedById,
				CategoryName = offering.Category?.Name,
				GroupTypeName = offering.GroupType?.Name,
				TaxTypeIds = taxes.Select(t => t.TaxTypeId).ToList()
			};

			return new GenericRespond<OfferingDetailDto>
			{
				Data = offeringDetailDto
			};
		}

		[HttpPost]
		public async Task<GenericRespond<bool>> Create([FromForm] OfferingCreateDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			string imagePath = null;
			if (dto.Image != null && dto.Image.Length > 0)
			{
				var file = _storageService.UploadFile(
					dto.Image,
					FileType.Image,
					"Offering",
					Visibility.Public,
					true,
					"Goodkey_Offering");

				imagePath = file.RelativePath;
			}

			// Step 1: Retrieve GroupType and Category to get their codes
			var groupType = await _offeringRepository.GetGroupTypeByIdAsync(dto.GroupTypeId ?? 0);
			var category = await _offeringRepository.GetCategoryByIdAsync(dto.CategoryId ?? 0);

			if (groupType == null || category == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 400,
					Message = "Invalid GroupTypeId or CategoryId"
				};
			}

			// Step 2: Create offering WITHOUT code first (code depends on Id which is generated after save)
			var offering = new Offering
			{
				CategoryId = dto.CategoryId,
				GroupTypeId = dto.GroupTypeId,
				Name = dto.Name,
				// Code is null for now, will update after getting Id
				SupplierItemNumber = dto.SupplierItemNumber,
				DisplayOrder = dto.DisplayOrder,
				UnitChargedId = dto.UnitChargedId,
				IsUnitTypeEach = dto.IsUnitTypeEach,
				IsAddOn = dto.IsAddOn,
				IsForSmOnly = dto.IsForSmOnly,
				IsInternalOnly = dto.IsInternalOnly,
				Image = imagePath,
				IsActive = dto.IsActive,
				IsObsolete = dto.IsObsolete,
				CreatedAt = DateTime.Now,
				CreatedById = user.UserId
			};

			// Save first to get the generated Id
			await _offeringRepository.AddAsync(offering);

			// Step 3: Generate unique code with Id now assigned
			var existingCodes = await GetAllCodes();
			string newCode = GenerateUniqueCode(
				groupCode: groupType.Code,
				categoryCode: category.Code,
				productId: offering.Id,
				existingCodes: existingCodes);

			// Step 4: Update offering with generated code
			offering.Code = newCode;

			// Step 5: Save updated offering
			await _offeringRepository.UpdateAsync(offering.Id, offering);

			// Add OfferingTax associations if any
			if (dto.TaxType != null && dto.TaxType.Any())
			{
				var offeringTaxAssociations = dto.TaxType.Select(taxTypeId => new OfferingTax
				{
					OfferingId = offering.Id,
					TaxTypeId = taxTypeId,
					CreatedBy = user.UserId,
					CreatedAt = DateTime.Now
				}).ToList();

				await _offeringTaxRepository.AddRangeAsync(offeringTaxAssociations);
			}

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Offering created successfully"
			};
		}


		[HttpPut("{id}")]
		public async Task<GenericRespond<bool>> Update(int id, [FromForm] OfferingCreateDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			var existingOffering = await _offeringRepository.GetByIdAsync(id);
			if (existingOffering == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 404,
					Message = "Item not found"
				};
			}

			string imagePath = existingOffering.Image;
			if (dto.Image != null && dto.Image.Length > 0)
			{
				var file = _storageService.UploadFile(
					dto.Image,
					FileType.Image,
					"Offering",
					Visibility.Public,
					true,
					"Goodkey_Offering");

				imagePath = file.RelativePath;
			}

			existingOffering.CategoryId = dto.CategoryId;
			existingOffering.GroupTypeId = dto.GroupTypeId;
			existingOffering.Name = dto.Name;
			existingOffering.SupplierItemNumber = dto.SupplierItemNumber;
			existingOffering.DisplayOrder = dto.DisplayOrder;
			existingOffering.UnitChargedId = dto.UnitChargedId;
			existingOffering.IsUnitTypeEach = dto.IsUnitTypeEach;
			existingOffering.IsAddOn = dto.IsAddOn;
			existingOffering.IsForSmOnly = dto.IsForSmOnly;
			existingOffering.IsInternalOnly = dto.IsInternalOnly;
			existingOffering.Image = imagePath;
			existingOffering.IsActive = dto.IsActive;
			existingOffering.IsObsolete = dto.IsObsolete;
			existingOffering.UpdatedAt = DateTime.Now;
			existingOffering.UpdatedById = user.UserId;

			await _offeringRepository.UpdateAsync(id, existingOffering);

			// Update OfferingTax associations
			await _offeringTaxRepository.DeleteByOfferingIdAsync(id);

			if (dto.TaxType != null && dto.TaxType.Any())
			{
				var offeringTaxAssociations = dto.TaxType.Select(taxTypeId => new OfferingTax
				{
					OfferingId = existingOffering.Id,
					TaxTypeId = taxTypeId,
					CreatedBy = user.UserId,
					CreatedAt = DateTime.Now
				}).ToList();

				await _offeringTaxRepository.AddRangeAsync(offeringTaxAssociations);
			}

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Offering saved successfully"
			};
		}

		[HttpPut("{id}/Description")]
		public async Task<GenericRespond<bool>> UpdateDescription(int id, [FromBody] Description dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			var existingOffering = await _offeringRepository.GetByIdAsync(id);
			if (existingOffering == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 404,
					Message = "Item not found"
				};
			}

			existingOffering.PublicDescription = dto.PublicDescription;
			existingOffering.InternalDescription = dto.InternalDescription;
			existingOffering.UpdatedAt = DateTime.Now;
			existingOffering.UpdatedById = user.UserId;

			await _offeringRepository.UpdateAsync(id, existingOffering);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Offering saved successfully"
			};
		}

		private async Task<HashSet<string>> GetAllCodes()
		{
			// Assuming repository returns all offerings with their group and category codes loaded
			var offerings = await _offeringRepository.GetAll();

			return new HashSet<string>(
				offerings
					.Where(o => o.GroupType != null && !string.IsNullOrEmpty(o.GroupType.Code)
						&& o.Category != null && !string.IsNullOrEmpty(o.Category.Code))
					.Select(o => $"{o.GroupType.Code.ToUpper()}-{o.Category.Code.ToUpper()}-{o.Id}")
			);
		}

		private string GenerateUniqueCode(string groupCode, string categoryCode, int productId, HashSet<string> existingCodes)
		{
			// Normalize codes to uppercase
			string normalizedGroupCode = groupCode?.Trim().ToUpper() ?? "GRP";
			string normalizedCategoryCode = categoryCode?.Trim().ToUpper() ?? "CAT";

			string code = $"{normalizedGroupCode}-{normalizedCategoryCode}-{productId}";

			// If code already exists, try suffixing with incremental number (unlikely with productId unique, but just in case)
			int suffix = 1;
			string uniqueCode = code;
			while (existingCodes.Contains(uniqueCode))
			{
				uniqueCode = $"{code}-{suffix}";
				suffix++;
			}

			return uniqueCode;
		}

	}
}
