﻿using goodkey_cms.DTO;
using goodkey_cms.Infrastructure.Extensions;
using goodkey_cms.Repositories;
using goodkey_common.DTO;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class DepartmentController : Controller
	{
		private readonly IDepartmentRepository _repo;
		public DepartmentController(IDepartmentRepository repo)
		{
			_repo = repo;
		}

		[HttpGet]
		public GenericRespond<IEnumerable<Department>> GetAll()
		{
			return new()
			{
				Data = _repo.GetAll().OrderByDescending(x => x.DepartmentId).Select(item =>

				new Department()
				{
					Id = item.DepartmentId,
					Name = item.Name,
					Code = item.Code,
				})
			};
		}

		[HttpGet("[action]/{id:int}")]
		public GenericRespond<Department> Get(int id)
		{

			var item = _repo.Get(id);
			return new()
			{
				Data = new Department()
				{
					Id = item?.DepartmentId,
					Name = item?.Name,
					Code = item?.Code,
				}
			};
		}

		[HttpPatch("[action]/{id:int}")]
		public GenericRespond<bool> Update(int id, BasicRequest data)
		{
			var username = Request.HttpContext.GetUsername();
			if (string.IsNullOrEmpty(username))
			{
				return new GenericRespond<bool>
				{
					Data = false,
					Message = "Unauthorized",
					StatusCode = 401
				};
			}

			var success = _repo.Update(id, data.Name, username);

			return new GenericRespond<bool>
			{
				Data = success,
				Message = success
					? "Department updated successfully."
					: "Failed to update department. It may not exist or there was a database error.",
				StatusCode = success ? 200 : 400
			};
		}

		[HttpPost("[action]")]
		public GenericRespond<bool> Add(BasicRequest data)
		{
			var username = Request.HttpContext.GetUsername();
			if (string.IsNullOrEmpty(username))
			{
				return new GenericRespond<bool>
				{
					Data = false,
					Message = "Unauthorized",
					StatusCode = 401
				};
			}

			var success = _repo.Add(data.Name, username);

			return new GenericRespond<bool>
			{
				Data = success,
				Message = success
					? "Department added successfully."
					: "Failed to add department. It may already exist or there was a database error.",
				StatusCode = success ? 200 : 400
			};
		}
	}
}
