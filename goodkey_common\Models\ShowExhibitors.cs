﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class ShowExhibitors
    {
        public ShowExhibitors()
        {
            ExhibitorImportRows = new HashSet<ExhibitorImportRows>();
        }

        public int Id { get; set; }
        public int ShowId { get; set; }
        public int CompanyId { get; set; }
        public int? ContactId { get; set; }
        public string[] BoothNumber { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsArchived { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? ArchivedAt { get; set; }
        public int? ArchivedById { get; set; }

        public virtual AuthUser ArchivedBy { get; set; }
        public virtual Company Company { get; set; }
        public virtual Contact Contact { get; set; }
        public virtual AuthUser CreatedBy { get; set; }
        public virtual Shows Show { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
        public virtual ICollection<ExhibitorImportRows> ExhibitorImportRows { get; set; }
    }
}