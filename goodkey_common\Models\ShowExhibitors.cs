// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class ShowExhibitors
    {
        public int Id { get; set; }
        public int ShowId { get; set; }
        public int CompanyId { get; set; }
        public int? ContactId { get; set; }
        public string BoothNumber { get; set; }
        public string FullName { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string City { get; set; }
        public int? CountryId { get; set; }
        public int? ProvinceId { get; set; }
        public string PostalCode { get; set; }
        public string Email { get; set; }
        public string Telephone { get; set; }
        public string Fax { get; set; }
        public string Password { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsArchived { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? ArchivedAt { get; set; }
        public int? ArchivedById { get; set; }

        // Navigation properties
        public virtual Shows Show { get; set; }
        public virtual Company Company { get; set; }
        public virtual Contact Contact { get; set; }
        public virtual Countries Country { get; set; }
        public virtual Provinces Province { get; set; }
        public virtual AuthUser CreatedBy { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
        public virtual AuthUser ArchivedBy { get; set; }
    }
}
