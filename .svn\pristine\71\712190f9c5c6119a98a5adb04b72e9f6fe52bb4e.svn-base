using Microsoft.AspNetCore.Http;

namespace goodkey_common.DTO.Gallery
{
    public class GalleryCategoryDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CreateGalleryCategoryDto
    {
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public class UpdateGalleryCategoryDto
    {
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public class GallerySubcategoryDto
    {
        public int Id { get; set; }
        public int CategoryId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string CategoryName { get; set; }
    }

    public class CreateGallerySubcategoryDto
    {
        public int CategoryId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public class UpdateGallerySubcategoryDto
    {
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public class GalleryImageDto
    {
        public int Id { get; set; }
        public int SubcategoryId { get; set; }
        public string Name { get; set; }
        public string Alt { get; set; }
        public string Url { get; set; }
        public DateTime? CreatedAt { get; set; }
        public string SubcategoryName { get; set; }
        public string CategoryName { get; set; }
    }

    public class CreateGalleryImageDto
    {
        public int SubcategoryId { get; set; }
        public string Name { get; set; }
        public string Alt { get; set; }
        public IFormFile Image { get; set; }
    }

    public class UpdateGalleryImageDto
    {
        public string Name { get; set; }
        public string Alt { get; set; }
        public IFormFile Image { get; set; }
    }
} 