using goodkey_common.Context;
using goodkey_common.Middlewares;
using goodkey_common.Services;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.EntityFrameworkCore;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.AddDbContext<GoodkeyContext>(options =>
	options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));
builder.Services.AddRepositories();
builder.Services.AddCors(options =>
{
	options.AddDefaultPolicy(builder =>
	{
		// Fine-tune later.
		builder.AllowAnyOrigin()
			   .AllowAnyHeader()
			   .AllowAnyHeader().WithOrigins("http://localhost:3000")
			   .AllowAnyMethod().WithOrigins("http://dev2.goodkey.com")
			   .AllowAnyMethod()
			   .AllowAnyHeader();
	});
});

builder.Services.AddHttpContextAccessor();
builder.Services.Configure<MailSettings>(builder.Configuration.GetSection("MailSettings"));
builder.Services.AddTransient<IMailService, MailService>();
builder.Services.AddTransient<StorageService>();

var app = builder.Build();

app.UseForwardedHeaders(new ForwardedHeadersOptions
{
	ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto,
	// Optionally, set the limit of proxy servers you trust
	ForwardLimit = 100
});
app.UseMiddleware<RespondCleanserMiddleware>();
app.UseMiddleware<ErrorHandlingMiddleware>();

// Configure the HTTP request pipeline.


app.UseSwagger();
app.UseSwaggerUI();

app.UseCors();
app.UseRouting();
app.UseHttpsRedirection();
app.MapSwagger().RequireAuthorization();

app.UseAuthentication();
app.UseAuthorization();
app.MapControllerRoute(
		name: "default",
		pattern: "api/{controller=Home}/{action=Index}");


app.MapControllerRoute(
	name: "protected",
	pattern: "p/api/{controller=Home}/{action=Index}"
);

app.Run();
