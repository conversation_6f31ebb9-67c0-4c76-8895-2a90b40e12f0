﻿using goodkey_common.Models;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;

namespace goodkey_cms.DTO.Document
{
	public class ShowDocUpload
	{
		public int? DocTypeId { get; set; }
		public int? LocationId { get; set; }
		public int? HallId { get; set; }
		public int? ShowId { get; set; }
		public IFormFile? File { get; set; }
		public string? Note { get; set; }
		public DateTime? ValidUntil { get; set; }
	}

	public class ShowDocUpdate : ShowDocUpload
	{
		public int Id { get; set; }
		public string? FilePath { get; set; }
	}

	public class ShowDoc
	{
		public int Id { get; set; }
		public DateTime? ValidUntil { get; set; }
		public string? FilePath { get; set; }
		public string? OriginalFilename { get; set; }
		public string? Note { get; set; }
		public int? DocId { get; set; }
		public DateTime? CreatedAt { get; set; }
		public DateTime? UpdatedAt { get; set; }
		public string? CreatedBy { get; set; }
		public string? DocType { get; set; }
		public string? Hall { get; set; }
		public string? Location { get; set; }
		public string? UpdatedBy { get; set; }
	}

	
}
