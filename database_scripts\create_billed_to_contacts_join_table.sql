-- This script creates the join table for "Billed To" contacts and migrates existing data.

-- Step 1: Create the join table to link ShowsPromoters and Contacts.
CREATE TABLE "ShowsPromoterBilledToContacts" (
    "ShowsPromoterId" INTEGER NOT NULL,
    "ContactId" INTEGER NOT NULL,
    CONSTRAINT "PK_ShowsPromoterBilledToContacts" PRIMARY KEY ("ShowsPromoterId", "ContactId"),
    CONSTRAINT "FK_ShowsPromoterBilledToContacts_ShowsPromoters" FOREIGN KEY ("ShowsPromoterId") REFERENCES "ShowsPromoters"("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_ShowsPromoterBilledToContacts_Contact" FOREIGN KEY ("ContactId") REFERENCES "Contact"("Id") ON DELETE CASCADE
);

-- Step 2: Migrate existing data from the old BilledToContactId column.
INSERT INTO "ShowsPromoterBilledToContacts" ("ShowsPromoterId", "ContactId")
SELECT "Id", "BilledToContactId"
FROM "ShowsPromoters"
WHERE "BilledToContactId" IS NOT NULL;

-- Note: After running this script, please verify the data in the new table before proceeding.
