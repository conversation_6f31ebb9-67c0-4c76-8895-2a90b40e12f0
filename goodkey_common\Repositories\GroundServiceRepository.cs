using goodkey_common.Context;
using goodkey_common.Models;

namespace goodkey_common.Repositories
{
    public interface IGroundServiceRepository
    {
        IEnumerable<GroundServices> GetAll();
        GroundServices GetById(int id);
        GroundServices Add(GroundServices entity);
        GroundServices Update(GroundServices entity);
        bool Delete(int id);
    }

    public class GroundServiceRepository : IGroundServiceRepository
    {
        private readonly GoodkeyContext _context;
        public GroundServiceRepository(GoodkeyContext context)
        {
            _context = context;
        }
        public IEnumerable<GroundServices> GetAll()
        {
            return _context.GroundServices.ToList();
        }
        public GroundServices GetById(int id)
        {
            return _context.GroundServices.FirstOrDefault(x => x.Id == id);
        }
        public GroundServices Add(GroundServices entity)
        {
            _context.GroundServices.Add(entity);
            _context.SaveChanges();
            return entity;
        }
        public GroundServices Update(GroundServices entity)
        {
            var existing = _context.GroundServices.Find(entity.Id);
            if (existing == null) return null;
            _context.Entry(existing).CurrentValues.SetValues(entity);
            _context.SaveChanges();
            return entity;
        }
        public bool Delete(int id)
        {
            var entity = _context.GroundServices.Find(id);
            if (entity == null) return false;
            _context.GroundServices.Remove(entity);
            _context.SaveChanges();
            return true;
        }
    }
} 