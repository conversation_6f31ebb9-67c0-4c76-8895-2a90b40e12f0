using System.ComponentModel.DataAnnotations;

namespace goodkey_common.DTO.Show
{
    public class ShowGeneralInfoDto
    {
        public int Id { get; set; }
        public bool Archive { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? DisplayDate { get; set; }
        public DateTime? OrderDeadlineDate { get; set; }
        public string LateChargePercentage { get; set; }
        public string Link { get; set; }
        public string Description { get; set; }
        public bool Display { get; set; }
        public DateTime CreatedAt { get; set; }
        public int? CreatedBy { get; set; }
        public int? LocationId { get; set; }
        public DateTime? KioskPrintingQueueDate { get; set; }
        public bool View { get; set; }
        
        // Navigation properties
        public string CreatedByUsername { get; set; }
        public string LocationName { get; set; }
    }

    public class CreateShowGeneralInfoDto
    {
        public string Name { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? DisplayDate { get; set; }
        public DateTime? OrderDeadlineDate { get; set; }
        public string LateChargePercentage { get; set; }
        public string Link { get; set; }
        public string Description { get; set; }
        public bool Display { get; set; }
        public int? LocationId { get; set; }
        public DateTime? KioskPrintingQueueDate { get; set; }
        public bool View { get; set; }
    }

    public class UpdateShowGeneralInfoDto
    {
        public string Name { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? DisplayDate { get; set; }
        public DateTime? OrderDeadlineDate { get; set; }
        public string LateChargePercentage { get; set; }
        public string Link { get; set; }
        public string Description { get; set; }
        public bool Display { get; set; }
        public int? LocationId { get; set; }
        public DateTime? KioskPrintingQueueDate { get; set; }
        public bool View { get; set; }
    }

    public class ShowHallContactDto
    {
        public int ShowId { get; set; }
        public int? HallId { get; set; }
        public int? ContactId { get; set; }
        public string HallName { get; set; }
        public string HallCode { get; set; }
        public string ContactName { get; set; }
        public string ContactEmail { get; set; }
        public string ContactPhone { get; set; }
    }

    public class SetShowHallContactDto
    {
        public int? HallId { get; set; }
        public int? ContactId { get; set; }
    }
} 