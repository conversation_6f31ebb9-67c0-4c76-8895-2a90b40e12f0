﻿using goodkey_cms.DTO.Document;
using goodkey_cms.Services;
using goodkey_common.DTO;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class ShowDocController : Controller
	{
		private readonly IShowDocRepository _repo;
		private readonly StorageService _storageService;
		private readonly AuthService _authService;

		public ShowDocController(IShowDocRepository repo, StorageService storageService, AuthService authService)
		{
			_repo = repo;
			_storageService = storageService;
			_authService = authService;
		}

		//[HttpGet("fullHierarchy/{locationId}")]
		//public async Task<GenericRespond<LocationDto>> GetFullLocationHierarchy(int locationId)
		//{
		//	var location = await _repo.GetFullLocationHierarchyAsync(locationId);

		//	if (location == null)
		//	{
		//		return new GenericRespond<LocationDto>
		//		{
		//			Data = null,
		//			StatusCode = 404,
		//			Message = "Location not found"
		//		};
		//	}

			//var locationDto = new LocationDto
			//{
			//	LocationId = location.LocationId,
			//	LocationName = location.Name,
			//	AccessPlan = location.AccessPlan == null ? null : new DocumentDto
			//	{
			//		Id = location.AccessPlan.Id,
			//		FilePath = location.AccessPlan.FilePath,
			//		OriginalFilename = location.AccessPlan.OriginalFilename,
			//		Note = location.AccessPlan.Note,
			//		ValidUntil = location.AccessPlan.ValidUntil
			//	},
			//	Halls = location.ShowLocationHalls.Select(hall => new HallDto
			//	{
			//		HallId = hall.HallId,
			//		HallCode = hall.HallCode,
			//		HallName = hall.HallName,
			//		FloorPlan = hall.FloorPlan == null ? null : new DocumentDto
			//		{
			//			Id = hall.FloorPlan.Id,
			//			FilePath = hall.FloorPlan.FilePath,
			//			OriginalFilename = hall.FloorPlan.OriginalFilename,
			//			Note = hall.FloorPlan.Note,
			//			ValidUntil = hall.FloorPlan.ValidUntil
			//		},
			//		Documents = hall.ShowDocs.Select(doc => new DocumentDto
			//		{
			//			Id = doc.ShowDocId,
			//			FilePath = doc.FilePath,
			//			OriginalFilename = doc.OriginalFilename,
			//			Note = doc.Note,
			//			ValidUntil = doc.ValidUntil
			//		}).ToList()
			//	}).ToList()
			//};

		//	return new GenericRespond<LocationDto>
		//	{
		//		Data = locationDto,
		//		StatusCode = 200,
		//		Message = "Success"
		//	};
		//}



		[HttpGet("{locationId}")]
		public GenericRespond<IEnumerable<ShowDoc>> GetAll(int locationId)
		{
			var docTypes = _repo.GetAllAsync(locationId).Result;

			var result = docTypes.Select(item => new ShowDoc
			{
				Id = item.ShowDocId,
				ValidUntil = item.ValidUntil,
				FilePath = item.FilePath,
				OriginalFilename = item.OriginalFilename,
				Note = item.Note,
				DocId = item.DocId,
				CreatedAt = item.CreatedAt,
				UpdatedAt = item.UpdatedAt,
				CreatedBy = item.CreatedBy?.FirstName + item.CreatedBy?.LastName ?? $"{item.CreatedById}",
				UpdatedBy = item.UpdatedBy?.FirstName + item.UpdatedBy?.LastName ?? $"{item.UpdatedById}",
				DocType = item.DocCategory?.Name ?? "Unknown",
				Location = item.Location?.Name ?? $"Loc {item.LocationId}",
				Hall = item.Hall?.HallCode + " " + item.Hall?.HallName ?? $"Hall {item.HallId}"
			});

			return new GenericRespond<IEnumerable<ShowDoc>>
			{
				Data = result
			};
		}

		// GET: /ShowDoc/update/5
		[HttpGet("Get/{id}")]
		public async Task<GenericRespond<ShowDocUpdate>> GetForUpdate(int id)
		{
			var item = await _repo.GetByIdAsync(id);
			if (item == null)
			{
				return new GenericRespond<ShowDocUpdate>
				{
					Data = null,
					StatusCode = 404,
					Message = "Document not found"
				};
			}
			var dto = new ShowDocUpdate
			{
				Id = item.ShowDocId,
				DocCategoryId = item.DocCategoryId,
				LocationId = item.LocationId,
				HallId = item.HallId,
				ShowId = item.ShowId,
				Note = item.Note,
				ValidUntil = item.ValidUntil,
				FilePath = item.FilePath,
				File = null
			};

			return new GenericRespond<ShowDocUpdate>
			{
				Data = dto,
				StatusCode = 200,
				Message = "Success"
			};
		}

		[HttpPost("upload")]
		public async Task<GenericRespond<bool>> UploadDocument([FromForm] ShowDocUpload dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			if (dto.File == null || dto.File.Length == 0)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 400,
					Message = "No file uploaded."
				};
			}

			var path = _storageService.UploadFile(
				dto.File,
				FileType.Document,
				"Show Location",
				Visibility.Protected,
				true,
				"Goodkey_ShowLocation_" + dto.LocationId
			).RelativePath;

			var doc = new ShowDocs
			{
				DocCategoryId = dto.DocCategoryId,
				LocationId = dto.LocationId,
				HallId = dto.HallId,
				ShowId = dto.ShowId,
				Note = dto.Note,
				ValidUntil = dto.ValidUntil,
				FilePath = path,
				OriginalFilename = dto.File.FileName,
				CreatedAt = DateTime.Now,
				CreatedById = user.UserId
			};

			await _repo.AddAsync(doc);

			return new GenericRespond<bool>
			{
				Data = true,
				Message = "Document uploaded successfully.",
				StatusCode = 200
			};
		}

		[HttpPut("update")]
		public async Task<GenericRespond<bool>> UpdateDocument([FromForm] ShowDocUpdate dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			var existingDoc = await _repo.GetByIdAsync(dto.Id);
			if (existingDoc == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 404,
					Message = "Document not found"
				};
			}

			// Update file if new file is uploaded
			if (dto.File != null && dto.File.Length > 0)
			{
				var newPath = _storageService.UploadFile(
					dto.File,
					FileType.Document,
					"Show Location",
					Visibility.Protected,
					true,
					"Goodkey_ShowLocation_" + dto.LocationId
				).RelativePath;

				existingDoc.FilePath = newPath;
				existingDoc.OriginalFilename = dto.File.FileName;
			}

			// Update other fields
			existingDoc.DocCategoryId = dto.DocCategoryId;
			existingDoc.LocationId = dto.LocationId;
			existingDoc.HallId = dto.HallId;
			existingDoc.ShowId = dto.ShowId;
			existingDoc.Note = dto.Note;
			existingDoc.ValidUntil = dto.ValidUntil;
			existingDoc.UpdatedAt = DateTime.Now;
			existingDoc.UpdatedById = user.UserId;

			// Save changes
			await _repo.UpdateAsync(existingDoc);

			return new GenericRespond<bool>
			{
				Data = true,
				StatusCode = 200,
				Message = "Document updated successfully"
			};
		}
	}
}
