﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class PropertyOption
    {
        public PropertyOption()
        {
            OfferingPropertyPropertyOption1 = new HashSet<OfferingProperty>();
            OfferingPropertyPropertyOption2 = new HashSet<OfferingProperty>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public int PropertyId { get; set; }
        public string Description { get; set; }
        public int? DisplayOrder { get; set; }
        public int? CreatedById { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public virtual AuthUser CreatedBy { get; set; }
        public virtual Property Property { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
        public virtual ICollection<OfferingProperty> OfferingPropertyPropertyOption1 { get; set; }
        public virtual ICollection<OfferingProperty> OfferingPropertyPropertyOption2 { get; set; }
    }
}