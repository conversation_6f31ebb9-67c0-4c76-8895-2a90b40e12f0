-- Exhibitor Import Cleanup and Maintenance Scripts
-- These scripts help maintain the import tables and clean up expired sessions

-- =====================================================
-- 1. Cleanup Expired Import Sessions
-- Run this periodically to remove old import sessions and their data
-- =====================================================

-- Function to clean up expired import sessions
CREATE OR REPLACE FUNCTION cleanup_expired_import_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete expired sessions and their related data (CASCADE will handle related tables)
    DELETE FROM "ExhibitorImportSessions" 
    WHERE "ExpiresAt" < CURRENT_TIMESTAMP 
    AND "Status" NOT IN ('Executing', 'Completed');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 2. Get Import Session Statistics
-- Useful for monitoring and reporting
-- =====================================================

CREATE OR REPLACE VIEW "ExhibitorImportSessionStats" AS
SELECT 
    s."SessionId",
    s."ShowId",
    s."FileName",
    s."Status",
    s."TotalRows",
    s."ValidRows",
    s."ErrorRows",
    s."WarningRows",
    s."CreatedAt",
    s."CreatedById",
    u."Username" as "CreatedByUsername",
    show."Name" as "ShowName",
    show."Code" as "ShowCode",
    CASE 
        WHEN s."Status" = 'Completed' THEN s."ProcessingCompletedAt" - s."ProcessingStartedAt"
        WHEN s."Status" = 'Executing' THEN CURRENT_TIMESTAMP - s."ProcessingStartedAt"
        ELSE NULL
    END as "ProcessingDuration",
    CASE 
        WHEN s."ExpiresAt" < CURRENT_TIMESTAMP THEN true
        ELSE false
    END as "IsExpired"
FROM "ExhibitorImportSessions" s
LEFT JOIN "AuthUser" u ON s."CreatedById" = u."UserId"
LEFT JOIN "Shows" show ON s."ShowId" = show."Id";

-- =====================================================
-- 3. Get Detailed Error Report for a Session
-- =====================================================

CREATE OR REPLACE FUNCTION get_import_error_report(session_uuid UUID)
RETURNS TABLE (
    row_number INTEGER,
    field_name VARCHAR(100),
    field_value TEXT,
    error_type VARCHAR(50),
    error_message TEXT,
    severity VARCHAR(20)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        e."RowNumber",
        e."FieldName",
        e."FieldValue",
        e."ErrorType",
        e."ErrorMessage",
        e."Severity"
    FROM "ExhibitorImportErrors" e
    WHERE e."SessionId" = session_uuid
    ORDER BY e."RowNumber", e."Severity" DESC, e."ErrorType";
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. Get Import Progress Summary
-- =====================================================

CREATE OR REPLACE FUNCTION get_import_progress(session_uuid UUID)
RETURNS TABLE (
    total_rows INTEGER,
    pending_rows INTEGER,
    valid_rows INTEGER,
    error_rows INTEGER,
    warning_rows INTEGER,
    processed_rows INTEGER,
    completion_percentage DECIMAL(5,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_rows,
        COUNT(CASE WHEN r."Status" = 'Pending' THEN 1 END)::INTEGER as pending_rows,
        COUNT(CASE WHEN r."Status" = 'Valid' THEN 1 END)::INTEGER as valid_rows,
        COUNT(CASE WHEN r."Status" = 'Error' THEN 1 END)::INTEGER as error_rows,
        COUNT(CASE WHEN r."Status" = 'Warning' THEN 1 END)::INTEGER as warning_rows,
        COUNT(CASE WHEN r."Status" = 'Processed' THEN 1 END)::INTEGER as processed_rows,
        CASE 
            WHEN COUNT(*) > 0 THEN 
                ROUND((COUNT(CASE WHEN r."Status" = 'Processed' THEN 1 END)::DECIMAL / COUNT(*)::DECIMAL) * 100, 2)
            ELSE 0
        END as completion_percentage
    FROM "ExhibitorImportRows" r
    WHERE r."SessionId" = session_uuid;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. Archive Completed Import Sessions
-- Move old completed sessions to archive table for historical tracking
-- =====================================================

-- Create archive table
CREATE TABLE IF NOT EXISTS "ExhibitorImportSessionsArchive" (
    LIKE "ExhibitorImportSessions" INCLUDING ALL
);

-- Function to archive old completed sessions
CREATE OR REPLACE FUNCTION archive_completed_import_sessions(days_old INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    archived_count INTEGER;
BEGIN
    -- Move completed sessions older than specified days to archive
    WITH moved_sessions AS (
        DELETE FROM "ExhibitorImportSessions" 
        WHERE "Status" = 'Completed' 
        AND "ProcessingCompletedAt" < CURRENT_TIMESTAMP - INTERVAL '1 day' * days_old
        RETURNING *
    )
    INSERT INTO "ExhibitorImportSessionsArchive" 
    SELECT * FROM moved_sessions;
    
    GET DIAGNOSTICS archived_count = ROW_COUNT;
    
    RETURN archived_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. Maintenance Queries
-- =====================================================

-- Find sessions that might be stuck in processing
SELECT 
    "SessionId",
    "ShowId", 
    "Status",
    "CreatedAt",
    "ProcessingStartedAt",
    CURRENT_TIMESTAMP - "ProcessingStartedAt" as "ProcessingDuration"
FROM "ExhibitorImportSessions" 
WHERE "Status" = 'Executing' 
AND "ProcessingStartedAt" < CURRENT_TIMESTAMP - INTERVAL '1 hour';

-- Get summary of import activity by show
SELECT 
    s."ShowId",
    show."Name" as "ShowName",
    COUNT(*) as "TotalImports",
    COUNT(CASE WHEN s."Status" = 'Completed' THEN 1 END) as "CompletedImports",
    COUNT(CASE WHEN s."Status" = 'Failed' THEN 1 END) as "FailedImports",
    SUM(s."ValidRows") as "TotalValidRows",
    SUM(s."ErrorRows") as "TotalErrorRows",
    AVG(s."TotalRows") as "AvgRowsPerImport"
FROM "ExhibitorImportSessions" s
LEFT JOIN "Shows" show ON s."ShowId" = show."Id"
GROUP BY s."ShowId", show."Name"
ORDER BY "TotalImports" DESC;

-- =====================================================
-- 7. Scheduled Cleanup Job (Example)
-- This could be run as a scheduled job to maintain the tables
-- =====================================================

-- Example cleanup procedure that could be scheduled
CREATE OR REPLACE FUNCTION scheduled_import_maintenance()
RETURNS TEXT AS $$
DECLARE
    expired_cleaned INTEGER;
    archived_count INTEGER;
    result_message TEXT;
BEGIN
    -- Clean up expired sessions
    SELECT cleanup_expired_import_sessions() INTO expired_cleaned;
    
    -- Archive old completed sessions (older than 30 days)
    SELECT archive_completed_import_sessions(30) INTO archived_count;
    
    -- Build result message
    result_message := FORMAT('Maintenance completed: %s expired sessions cleaned, %s sessions archived', 
                            expired_cleaned, archived_count);
    
    -- Log the maintenance activity (you could insert into a log table here)
    RAISE NOTICE '%', result_message;
    
    RETURN result_message;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 8. Useful Queries for Monitoring
-- =====================================================

-- Current active import sessions
-- SELECT * FROM "ExhibitorImportSessionStats" WHERE "Status" IN ('Validating', 'Validated', 'Executing');

-- Recent import activity (last 7 days)
-- SELECT * FROM "ExhibitorImportSessionStats" WHERE "CreatedAt" > CURRENT_TIMESTAMP - INTERVAL '7 days' ORDER BY "CreatedAt" DESC;

-- Sessions with high error rates
-- SELECT * FROM "ExhibitorImportSessionStats" WHERE "ErrorRows" > "ValidRows" AND "TotalRows" > 0;
