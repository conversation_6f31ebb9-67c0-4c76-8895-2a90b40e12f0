using goodkey_common.Context;
using goodkey_common.Models;
using goodkey_common.DTO.Show;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
    public interface IShowDocumentRepository
    {
        // Basic CRUD operations
        Task<IEnumerable<ShowDocuments>> GetShowDocumentsAsync(int showId, ShowDocumentFilterDto? filter = null);
        Task<ShowDocuments?> GetShowDocumentByIdAsync(int id);
        Task<ShowDocuments> CreateShowDocumentAsync(ShowDocuments document);
        Task<ShowDocuments> UpdateShowDocumentAsync(ShowDocuments document);
        Task<bool> DeleteShowDocumentAsync(int id);

        // Archive/Restore operations
        Task<bool> ArchiveShowDocumentAsync(int id, int archivedById, string? reason = null);
        Task<bool> RestoreShowDocumentAsync(int id, int restoredById, string? reason = null);

        // Bulk operations
        Task<bool> BulkArchiveDocumentsAsync(List<int> documentIds, int archivedById, string? reason = null);
        Task<bool> BulkRestoreDocumentsAsync(List<int> documentIds, int restoredById, string? reason = null);
        Task<bool> BulkUpdateStatusAsync(List<int> documentIds, string status, int updatedById);

        // File operations
        Task<bool> UpdateFilePathAsync(int id, string filePath, string fileName, string originalFileName, long fileSize, string mimeType);
        Task<string?> GetFilePathAsync(int id);

        // Statistics and reporting
        Task<ShowDocumentStatsDto> GetShowDocumentStatsAsync(int showId);
        Task<IEnumerable<DocumentFileTypes>> GetAvailableDocumentTypesAsync();

        // Validation
        Task<bool> DocumentExistsAsync(int id);
        Task<bool> ShowHasDocumentTypeAsync(int showId, int documentTypeId);
        Task<Shows?> GetShowAsync(int showId);
    }

    public class ShowDocumentRepository : IShowDocumentRepository
    {
        private readonly GoodkeyContext _context;

        public ShowDocumentRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<ShowDocuments>> GetShowDocumentsAsync(int showId, ShowDocumentFilterDto? filter = null)
        {
            var query = _context.ShowDocuments
                .Include(d => d.DocumentType)
                .Include(d => d.Show)
                .Include(d => d.UploadedBy)
                .Include(d => d.UpdatedBy)
                .Include(d => d.ArchivedBy)
                .Where(d => d.ShowId == showId);

            if (filter != null)
            {
                if (filter.DocumentTypeId.HasValue)
                    query = query.Where(d => d.DocumentTypeId == filter.DocumentTypeId.Value);

                if (!string.IsNullOrEmpty(filter.Status))
                    query = query.Where(d => d.Status == filter.Status);

                if (filter.IsRequired.HasValue)
                    query = query.Where(d => d.IsRequired == filter.IsRequired.Value);

                if (filter.IsPublic.HasValue)
                    query = query.Where(d => d.IsPublic == filter.IsPublic.Value);

                if (filter.IsArchived.HasValue)
                    query = query.Where(d => d.IsArchived == filter.IsArchived.Value);

                if (filter.UploadedAfter.HasValue)
                    query = query.Where(d => d.UploadedAt >= filter.UploadedAfter.Value);

                if (filter.UploadedBefore.HasValue)
                    query = query.Where(d => d.UploadedAt <= filter.UploadedBefore.Value);

                if (!string.IsNullOrEmpty(filter.SearchTerm))
                {
                    var searchTerm = filter.SearchTerm.ToLower();
                    query = query.Where(d => 
                        d.Name.ToLower().Contains(searchTerm) ||
                        d.Description.ToLower().Contains(searchTerm) ||
                        d.OriginalFileName.ToLower().Contains(searchTerm));
                }
            }

            return await query
                .OrderByDescending(d => d.UploadedAt)
                .ToListAsync();
        }

        public async Task<ShowDocuments?> GetShowDocumentByIdAsync(int id)
        {
            return await _context.ShowDocuments
                .Include(d => d.DocumentType)
                .Include(d => d.Show)
                .Include(d => d.UploadedBy)
                .Include(d => d.UpdatedBy)
                .Include(d => d.ArchivedBy)
                .FirstOrDefaultAsync(d => d.Id == id);
        }

        public async Task<ShowDocuments> CreateShowDocumentAsync(ShowDocuments document)
        {
            document.UploadedAt = DateTime.UtcNow;
            document.Version = 1;
            document.Status = "Active";
            document.IsArchived = false;

            _context.ShowDocuments.Add(document);
            await _context.SaveChangesAsync();
            return document;
        }

        public async Task<ShowDocuments> UpdateShowDocumentAsync(ShowDocuments document)
        {
            document.UpdatedAt = DateTime.UtcNow;
            _context.ShowDocuments.Update(document);
            await _context.SaveChangesAsync();
            return document;
        }

        public async Task<bool> DeleteShowDocumentAsync(int id)
        {
            var document = await _context.ShowDocuments.FindAsync(id);
            if (document == null)
                return false;

            _context.ShowDocuments.Remove(document);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ArchiveShowDocumentAsync(int id, int archivedById, string? reason = null)
        {
            var document = await _context.ShowDocuments.FindAsync(id);
            if (document == null)
                return false;

            document.IsArchived = true;
            document.ArchivedAt = DateTime.UtcNow;
            document.ArchivedById = archivedById;
            document.Status = "Archived";

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RestoreShowDocumentAsync(int id, int restoredById, string? reason = null)
        {
            var document = await _context.ShowDocuments.FindAsync(id);
            if (document == null)
                return false;

            document.IsArchived = false;
            document.ArchivedAt = null;
            document.ArchivedById = null;
            document.Status = "Active";
            document.UpdatedAt = DateTime.UtcNow;
            document.UpdatedById = restoredById;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> BulkArchiveDocumentsAsync(List<int> documentIds, int archivedById, string? reason = null)
        {
            var documents = await _context.ShowDocuments
                .Where(d => documentIds.Contains(d.Id))
                .ToListAsync();

            foreach (var document in documents)
            {
                document.IsArchived = true;
                document.ArchivedAt = DateTime.UtcNow;
                document.ArchivedById = archivedById;
                document.Status = "Archived";
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> BulkRestoreDocumentsAsync(List<int> documentIds, int restoredById, string? reason = null)
        {
            var documents = await _context.ShowDocuments
                .Where(d => documentIds.Contains(d.Id))
                .ToListAsync();

            foreach (var document in documents)
            {
                document.IsArchived = false;
                document.ArchivedAt = null;
                document.ArchivedById = null;
                document.Status = "Active";
                document.UpdatedAt = DateTime.UtcNow;
                document.UpdatedById = restoredById;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> BulkUpdateStatusAsync(List<int> documentIds, string status, int updatedById)
        {
            var documents = await _context.ShowDocuments
                .Where(d => documentIds.Contains(d.Id))
                .ToListAsync();

            foreach (var document in documents)
            {
                document.Status = status;
                document.UpdatedAt = DateTime.UtcNow;
                document.UpdatedById = updatedById;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UpdateFilePathAsync(int id, string filePath, string fileName, string originalFileName, long fileSize, string mimeType)
        {
            var document = await _context.ShowDocuments.FindAsync(id);
            if (document == null)
                return false;

            // Increment version when file is replaced
            document.Version = (document.Version ?? 1) + 1;
            document.FilePath = filePath;
            document.FileName = fileName;
            document.OriginalFileName = originalFileName;
            document.FileSize = fileSize;
            document.MimeType = mimeType;
            document.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<string?> GetFilePathAsync(int id)
        {
            var document = await _context.ShowDocuments.FindAsync(id);
            return document?.FilePath;
        }

        public async Task<ShowDocumentStatsDto> GetShowDocumentStatsAsync(int showId)
        {
            var documents = await _context.ShowDocuments
                .Include(d => d.DocumentType)
                .Where(d => d.ShowId == showId)
                .ToListAsync();

            return new ShowDocumentStatsDto
            {
                TotalDocuments = documents.Count,
                ActiveDocuments = documents.Count(d => d.IsArchived != true),
                ArchivedDocuments = documents.Count(d => d.IsArchived == true),
                RequiredDocuments = documents.Count(d => d.IsRequired == true),
                PublicDocuments = documents.Count(d => d.IsPublic == true),
                TotalFileSize = documents.Sum(d => d.FileSize ?? 0),
                DocumentsByType = documents
                    .GroupBy(d => d.DocumentType?.Name ?? "Unknown")
                    .ToDictionary(g => g.Key, g => g.Count()),
                DocumentsByStatus = documents
                    .GroupBy(d => d.Status ?? "Unknown")
                    .ToDictionary(g => g.Key, g => g.Count())
            };
        }

        public async Task<IEnumerable<DocumentFileTypes>> GetAvailableDocumentTypesAsync()
        {
            return await _context.DocumentFileTypes
                .Where(dt => dt.IsAvailable == true)
                .OrderBy(dt => dt.Name)
                .ToListAsync();
        }

        public async Task<bool> DocumentExistsAsync(int id)
        {
            return await _context.ShowDocuments.AnyAsync(d => d.Id == id);
        }

        public async Task<bool> ShowHasDocumentTypeAsync(int showId, int documentTypeId)
        {
            return await _context.ShowDocuments
                .AnyAsync(d => d.ShowId == showId && d.DocumentTypeId == documentTypeId && d.IsArchived != true);
        }

        public async Task<Shows?> GetShowAsync(int showId)
        {
            return await _context.Shows.FindAsync(showId);
        }
    }
}
