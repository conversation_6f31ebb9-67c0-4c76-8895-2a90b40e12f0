﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
	public interface ICategoryRepository
	{
		Task<IEnumerable<Category>> GetAllAsync();
		Task<Category> GetByIdAsync(int id);
		Task AddAsync(Category location);
		Task UpdateAsync(int id, Category updated);	

	}

	public class CategoryRepository : ICategoryRepository
	{
		private readonly GoodkeyContext _context;

		public CategoryRepository(GoodkeyContext context)
		{
			_context = context;
		}

		public async Task<IEnumerable<Category>> GetAllAsync()
		{
			return await _context.Category.Include(x => x.GroupType).ToListAsync();
		}

		public async Task<Category> GetByIdAsync(int id)
		{
			return await _context.Category.Include(x => x.GroupType).FirstOrDefaultAsync(x => x.Id == id);
		}

		public async Task AddAsync(Category category)
		{
			await _context.Category.AddAsync(category);
			await _context.SaveChangesAsync();
		}

		public async Task UpdateAsync(int id, Category updated)
		{
			var existing = await _context.Category.FindAsync(id);
			if (existing == null) return;

			existing.Name = updated.Name;
			
			existing.UpdatedById = updated.UpdatedById;
			existing.UpdatedAt = updated.UpdatedAt;			

			await _context.SaveChangesAsync();
		}		
	}
}
