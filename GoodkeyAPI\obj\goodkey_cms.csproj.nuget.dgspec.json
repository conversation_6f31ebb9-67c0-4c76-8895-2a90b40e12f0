{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\goodkey_cms.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\goodkey_cms.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\goodkey_cms.csproj", "projectName": "goodkey_cms", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\goodkey_cms.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "C:\\Users\\<USER>\\source\\repos\\c-sharp-packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\goodkey_common.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\goodkey_common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"Handlebars.Net": {"target": "Package", "version": "[2.1.4, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[7.0.20, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.20, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[7.0.20, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.20, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.20, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[7.0.18, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[7.0.20, 7.0.20]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\goodkey_common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\goodkey_common.csproj", "projectName": "goodkey_common", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\goodkey_common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "C:\\Users\\<USER>\\source\\repos\\c-sharp-packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"FileSignatures": {"target": "Package", "version": "[4.4.1, )"}, "Handlebars.Net": {"target": "Package", "version": "[2.1.4, )"}, "MailKit": {"target": "Package", "version": "[4.3.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[7.0.20, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[7.0.20, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.20, )"}, "Microsoft.EntityFrameworkCore.Analyzers": {"target": "Package", "version": "[7.0.20, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[7.0.20, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.20, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.20, )"}, "MimeKit": {"target": "Package", "version": "[4.4.0, )"}, "MimeTypesMap": {"target": "Package", "version": "[1.0.8, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[6.0.29, )"}, "PdfiumViewer": {"target": "Package", "version": "[2.13.0, )"}, "SixLabors.ImageSharp": {"target": "Package", "version": "[3.1.4, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}, "System.Drawing.Common": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[7.0.20, 7.0.20]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}}}