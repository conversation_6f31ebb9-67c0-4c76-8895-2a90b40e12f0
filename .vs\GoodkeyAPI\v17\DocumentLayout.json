{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Project\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkey_common\\context\\goodkeycontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|solutionrelative:goodkey_common\\context\\goodkeycontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkey_common\\models\\authuser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|solutionrelative:goodkey_common\\models\\authuser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkey_common\\goodkey_common.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|solutionrelative:goodkey_common\\goodkey_common.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{73B259ED-FD23-431B-ADDD-B0C7C2AC97DA}|goodkey_public\\goodkey_public.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkey_public\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{73B259ED-FD23-431B-ADDD-B0C7C2AC97DA}|goodkey_public\\goodkey_public.csproj|solutionrelative:goodkey_public\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{73B259ED-FD23-431B-ADDD-B0C7C2AC97DA}|goodkey_public\\goodkey_public.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkey_public\\controllers\\storagecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{73B259ED-FD23-431B-ADDD-B0C7C2AC97DA}|goodkey_public\\goodkey_public.csproj|solutionrelative:goodkey_public\\controllers\\storagecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{73B259ED-FD23-431B-ADDD-B0C7C2AC97DA}|goodkey_public\\goodkey_public.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkey_public\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{73B259ED-FD23-431B-ADDD-B0C7C2AC97DA}|goodkey_public\\goodkey_public.csproj|solutionrelative:goodkey_public\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{73B259ED-FD23-431B-ADDD-B0C7C2AC97DA}|goodkey_public\\goodkey_public.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkey_public\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{73B259ED-FD23-431B-ADDD-B0C7C2AC97DA}|goodkey_public\\goodkey_public.csproj|solutionrelative:goodkey_public\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkeyapi\\controllers\\gallerycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\controllers\\gallerycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkeyapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkeyapi\\controllers\\showcontactcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\controllers\\showcontactcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkey_common\\repositories\\showcontactrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|solutionrelative:goodkey_common\\repositories\\showcontactrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkeyapi\\controllers\\companycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\controllers\\companycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkeyapi\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{73B259ED-FD23-431B-ADDD-B0C7C2AC97DA}|goodkey_public\\goodkey_public.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkey_public\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{73B259ED-FD23-431B-ADDD-B0C7C2AC97DA}|goodkey_public\\goodkey_public.csproj|solutionrelative:goodkey_public\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkeyapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkey_common\\models\\contact.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|solutionrelative:goodkey_common\\models\\contact.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkeyapi\\controllers\\propertyoptioncontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\controllers\\propertyoptioncontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkeyapi\\controllers\\propertycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\controllers\\propertycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkeyapi\\controllers\\grouptypecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\controllers\\grouptypecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkeyapi\\controllers\\categorycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\controllers\\categorycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkey_common\\repositories\\companyrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|solutionrelative:goodkey_common\\repositories\\companyrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkeyapi\\controllers\\showhallcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\controllers\\showhallcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkeyapi\\goodkey_cms.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\goodkey_cms.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkeyapi\\controllers\\showdoccontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\controllers\\showdoccontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkey_common\\repositories\\menurepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|solutionrelative:goodkey_common\\repositories\\menurepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\project\\goodkeyapi\\controllers\\provincescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\controllers\\provincescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 27, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "AuthUser.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\Models\\AuthUser.cs", "RelativeDocumentMoniker": "goodkey_common\\Models\\AuthUser.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\Models\\AuthUser.cs", "RelativeToolTip": "goodkey_common\\Models\\AuthUser.cs", "ViewState": "AQIAAHEAAAAAAAAAAAAIwH8AAAADAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T14:52:39.078Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "GalleryController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\GalleryController.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\Controllers\\GalleryController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\GalleryController.cs", "RelativeToolTip": "GoodkeyAPI\\Controllers\\GalleryController.cs", "ViewState": "AQIAABMAAAAAAAAAAAAQwCgAAAARAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T17:52:18.548Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "ShowContactRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\Repositories\\ShowContactRepository.cs", "RelativeDocumentMoniker": "goodkey_common\\Repositories\\ShowContactRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\Repositories\\ShowContactRepository.cs", "RelativeToolTip": "goodkey_common\\Repositories\\ShowContactRepository.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAgAAAAhAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T15:59:14.685Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "StorageController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_public\\Controllers\\StorageController.cs", "RelativeDocumentMoniker": "goodkey_public\\Controllers\\StorageController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_public\\Controllers\\StorageController.cs", "RelativeToolTip": "goodkey_public\\Controllers\\StorageController.cs", "ViewState": "AQIAAAYAAAAAAAAAAAAAABEAAAADAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T17:55:36.829Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_public\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "goodkey_public\\Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_public\\Properties\\launchSettings.json", "RelativeToolTip": "goodkey_public\\Properties\\launchSettings.json", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAsAAAABAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-11T20:10:12.794Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_public\\appsettings.Development.json", "RelativeDocumentMoniker": "goodkey_public\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_public\\appsettings.Development.json", "RelativeToolTip": "goodkey_public\\appsettings.Development.json", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-11T20:07:44.564Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\appsettings.Development.json", "RelativeDocumentMoniker": "GoodkeyAPI\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\appsettings.Development.json", "RelativeToolTip": "GoodkeyAPI\\appsettings.Development.json", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-11T20:07:37.483Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_public\\appsettings.json", "RelativeDocumentMoniker": "goodkey_public\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_public\\appsettings.json", "RelativeToolTip": "goodkey_public\\appsettings.json", "ViewState": "AQIAAAAAAAAAAAAAAAAAABEAAAABAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-11T20:06:00.16Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_public\\Program.cs", "RelativeDocumentMoniker": "goodkey_public\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_public\\Program.cs", "RelativeToolTip": "goodkey_public\\Program.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAMAAAApAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T19:58:55.893Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\appsettings.json", "RelativeDocumentMoniker": "GoodkeyAPI\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\appsettings.json", "RelativeToolTip": "GoodkeyAPI\\appsettings.json", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-11T19:47:47.331Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Program.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Program.cs", "RelativeToolTip": "GoodkeyAPI\\Program.cs", "ViewState": "AQIAAEgAAAAAAAAAAAAAAFkAAAAzAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T19:46:16.299Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "Contact.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\Models\\Contact.cs", "RelativeDocumentMoniker": "goodkey_common\\Models\\Contact.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\Models\\Contact.cs", "RelativeToolTip": "goodkey_common\\Models\\Contact.cs", "ViewState": "AQIAAAYAAAAAAAAAAAAAAA8AAAApAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T19:45:43.299Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "PropertyOptionController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\PropertyOptionController.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\Controllers\\PropertyOptionController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\PropertyOptionController.cs", "RelativeToolTip": "GoodkeyAPI\\Controllers\\PropertyOptionController.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAABEAAAADAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T19:44:53.893Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "PropertyController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\PropertyController.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\Controllers\\PropertyController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\PropertyController.cs", "RelativeToolTip": "GoodkeyAPI\\Controllers\\PropertyController.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAA0AAAATAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T19:44:44.237Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "GroupTypeController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\GroupTypeController.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\Controllers\\GroupTypeController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\GroupTypeController.cs", "RelativeToolTip": "GoodkeyAPI\\Controllers\\GroupTypeController.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAwAAAAdAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T19:44:41.346Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "CategoryController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\CategoryController.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\Controllers\\CategoryController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\CategoryController.cs", "RelativeToolTip": "GoodkeyAPI\\Controllers\\CategoryController.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAABAAAAATAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T19:44:37.612Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "ShowContactController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\ShowContactController.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\Controllers\\ShowContactController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\ShowContactController.cs", "RelativeToolTip": "GoodkeyAPI\\Controllers\\ShowContactController.cs", "ViewState": "AQIAAAsAAAAAAAAAAAAiwBoAAAArAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T18:40:27.455Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "ShowHallController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\ShowHallController.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\Controllers\\ShowHallController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\ShowHallController.cs", "RelativeToolTip": "GoodkeyAPI\\Controllers\\ShowHallController.cs", "ViewState": "AQIAAGUAAAAAAAAAAAAqwHgAAAAjAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T14:02:22.851Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "goodkey_cms.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\goodkey_cms.csproj", "RelativeDocumentMoniker": "GoodkeyAPI\\goodkey_cms.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\goodkey_cms.csproj", "RelativeToolTip": "GoodkeyAPI\\goodkey_cms.csproj", "ViewState": "AQIAAAAAAAAAAAAAAAAAABUAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-09T15:40:52.062Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "goodkey_common.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\goodkey_common.csproj", "RelativeDocumentMoniker": "goodkey_common\\goodkey_common.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\goodkey_common.csproj", "RelativeToolTip": "goodkey_common\\goodkey_common.csproj", "ViewState": "AQIAAAAAAAAAAAAAAAAAABkAAABZAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-09T15:40:00.079Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "ShowDocController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\ShowDocController.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\Controllers\\ShowDocController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\ShowDocController.cs", "RelativeToolTip": "GoodkeyAPI\\Controllers\\ShowDocController.cs", "ViewState": "AQIAAEUAAAAAAAAAAAAswEsAAAAdAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T15:22:45.138Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "MenuRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\Repositories\\MenuRepository.cs", "RelativeDocumentMoniker": "goodkey_common\\Repositories\\MenuRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\Repositories\\MenuRepository.cs", "RelativeToolTip": "goodkey_common\\Repositories\\MenuRepository.cs", "ViewState": "AQIAACUAAAAAAAAAAAAUwBEAAAAhAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T15:22:43.793Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "CompanyController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\CompanyController.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\Controllers\\CompanyController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\CompanyController.cs", "RelativeToolTip": "GoodkeyAPI\\Controllers\\CompanyController.cs", "ViewState": "AQIAAPAAAAAAAAAAAAAQwFEBAAA0AAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T15:22:01.748Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "CompanyRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\Repositories\\CompanyRepository.cs", "RelativeDocumentMoniker": "goodkey_common\\Repositories\\CompanyRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\Repositories\\CompanyRepository.cs", "RelativeToolTip": "goodkey_common\\Repositories\\CompanyRepository.cs", "ViewState": "AQIAAAIAAAAAAAAAAAAqwAUAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T15:19:55.183Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "ProvincesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\ProvincesController.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\Controllers\\ProvincesController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\GoodkeyAPI\\Controllers\\ProvincesController.cs", "RelativeToolTip": "GoodkeyAPI\\Controllers\\ProvincesController.cs", "ViewState": "AQIAAAsAAAAAAAAAAAAQwCQAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T14:42:48.734Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "GoodkeyContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\Context\\GoodkeyContext.cs", "RelativeDocumentMoniker": "goodkey_common\\Context\\GoodkeyContext.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Project\\goodkey_common\\Context\\GoodkeyContext.cs", "RelativeToolTip": "goodkey_common\\Context\\GoodkeyContext.cs", "ViewState": "AQIAAGoDAAAAAAAAAAAQwIIDAAAnAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T14:42:48.753Z", "EditorCaption": ""}]}]}]}