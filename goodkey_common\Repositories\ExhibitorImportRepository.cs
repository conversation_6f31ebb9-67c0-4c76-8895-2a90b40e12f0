using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
    public interface IExhibitorImportRepository
    {
        // Session Management
        ExhibitorImportSessions CreateSession(int showId, string fileName, string originalFileName,
            string filePath, long fileSize, string mimeType, int createdById);
        ExhibitorImportSessions? GetSession(Guid sessionId);
        ExhibitorImportSessions? GetSessionBySessionId(string sessionId);
        bool UpdateSessionStatus(Guid sessionId, string status, bool canProceed = false);
        bool UpdateSessionCounts(Guid sessionId, int totalRows, int validRows, int errorRows, int warningRows);
        bool UpdateSessionValidationSummary(Guid sessionId, string validationSummary);
        bool SetSessionExecutionStart(Guid sessionId, int executedById);
        bool SetSessionExecutionComplete(Guid sessionId);
        bool DeleteExpiredSessions();

        // Row Management
        int CreateImportRow(Guid sessionId, int rowNumber, string companyName, string contactFirstName,
            string contactLastName, string contactEmail, string contactPhone, string contactMobile,
            string contactExt, string boothNumbers, string contactType);
        bool UpdateRowValidationResults(int rowId, string status, bool hasErrors, bool hasWarnings,
            int errorCount, int warningCount);
        bool UpdateRowResolvedValues(int rowId, int? resolvedCompanyId, int? resolvedContactTypeId,
            string[] resolvedBoothNumbers, bool isNewCompany, bool isNewContact, bool isDuplicate);
        bool UpdateRowProcessingResults(int rowId, int? createdCompanyId, int? createdContactId,
            int? createdExhibitorId, int? createdUserId);
        List<ExhibitorImportRows> GetSessionRows(Guid sessionId);
        ExhibitorImportRows? GetRowById(int rowId);

        // Validation Messages
        int CreateValidationMessage(Guid sessionId, int rowId, int rowNumber, string fieldName,
            string fieldValue, string messageType, string validationRule, string messageCode, string message);
        List<ExhibitorImportValidationMessages> GetSessionValidationMessages(Guid sessionId);
        List<ExhibitorImportValidationMessages> GetRowValidationMessages(int rowId);

        // Duplicate Management
        int CreateDuplicate(Guid sessionId, string duplicateType, string duplicateValue,
            string rowNumbers, string rowIds, string conflictResolution);
        List<ExhibitorImportDuplicates> GetSessionDuplicates(Guid sessionId);
        bool UpdateDuplicateResolution(int duplicateId, string conflictResolution);

        // Lookup and Validation Helpers
        List<Company> FindCompaniesByName(string companyName);
        List<Contact> FindContactsByEmail(string email);
        List<ContactType> GetAllContactTypes();
        ContactType? FindContactTypeByText(string contactTypeText);
        bool IsBoothNumberAvailable(int showId, string boothNumber, Guid? excludeSessionId = null);
        List<string> GetUsedBoothNumbers(int showId);

        // Statistics and Reporting
        Dictionary<string, int> GetSessionStatistics(Guid sessionId);
        List<ExhibitorImportSessions> GetRecentSessions(int showId, int limit = 10);
        List<ExhibitorImportSessions> GetActiveSessionsByUser(int userId);
    }

    public class ExhibitorImportRepository : IExhibitorImportRepository
    {
        private readonly GoodkeyContext _context;

        public ExhibitorImportRepository(GoodkeyContext context)
        {
            _context = context;
        }

        // =====================================================
        // Session Management
        // =====================================================

        public ExhibitorImportSessions CreateSession(int showId, string fileName, string originalFileName,
            string filePath, long fileSize, string mimeType, int createdById)
        {
            var session = new ExhibitorImportSessions
            {
                SessionId = Guid.NewGuid(),
                ShowId = showId,
                FileName = fileName,
                OriginalFileName = originalFileName,
                FilePath = filePath,
                FileSize = fileSize,
                MimeType = mimeType,
                Status = "Validating",
                CanProceed = false,
                ExpiresAt = DateTime.UtcNow.AddHours(24),
                CreatedAt = DateTime.UtcNow,
                CreatedById = createdById
            };

            _context.ExhibitorImportSessions.Add(session);
            _context.SaveChanges();
            return session;
        }

        public ExhibitorImportSessions? GetSession(Guid sessionId)
        {
            return _context.ExhibitorImportSessions
                .Include(s => s.Show)
                .Include(s => s.CreatedBy)
                .Include(s => s.ExecutedBy)
                .FirstOrDefault(s => s.SessionId == sessionId);
        }

        public ExhibitorImportSessions? GetSessionBySessionId(string sessionId)
        {
            if (Guid.TryParse(sessionId, out var guid))
            {
                return GetSession(guid);
            }
            return null;
        }

        public bool UpdateSessionStatus(Guid sessionId, string status, bool canProceed = false)
        {
            var session = _context.ExhibitorImportSessions.FirstOrDefault(s => s.SessionId == sessionId);
            if (session == null) return false;

            session.Status = status;
            session.CanProceed = canProceed;
            _context.SaveChanges();
            return true;
        }

        public bool UpdateSessionCounts(Guid sessionId, int totalRows, int validRows, int errorRows, int warningRows)
        {
            var session = _context.ExhibitorImportSessions.FirstOrDefault(s => s.SessionId == sessionId);
            if (session == null) return false;

            session.TotalRows = totalRows;
            session.ValidRows = validRows;
            session.ErrorRows = errorRows;
            session.WarningRows = warningRows;
            _context.SaveChanges();
            return true;
        }

        public bool UpdateSessionValidationSummary(Guid sessionId, string validationSummary)
        {
            var session = _context.ExhibitorImportSessions.FirstOrDefault(s => s.SessionId == sessionId);
            if (session == null) return false;

            session.ValidationSummary = validationSummary;
            _context.SaveChanges();
            return true;
        }

        public bool SetSessionExecutionStart(Guid sessionId, int executedById)
        {
            var session = _context.ExhibitorImportSessions.FirstOrDefault(s => s.SessionId == sessionId);
            if (session == null) return false;

            session.Status = "Executing";
            session.ProcessingStartedAt = DateTime.UtcNow;
            session.ExecutedById = executedById;
            _context.SaveChanges();
            return true;
        }

        public bool SetSessionExecutionComplete(Guid sessionId)
        {
            var session = _context.ExhibitorImportSessions.FirstOrDefault(s => s.SessionId == sessionId);
            if (session == null) return false;

            session.Status = "Completed";
            session.ProcessingCompletedAt = DateTime.UtcNow;
            _context.SaveChanges();
            return true;
        }

        public bool DeleteExpiredSessions()
        {
            var expiredSessions = _context.ExhibitorImportSessions
                .Where(s => s.ExpiresAt < DateTime.UtcNow && s.Status != "Executing")
                .ToList();

            if (expiredSessions.Any())
            {
                _context.ExhibitorImportSessions.RemoveRange(expiredSessions);
                _context.SaveChanges();
                return true;
            }
            return false;
        }

        // =====================================================
        // Row Management
        // =====================================================

        public int CreateImportRow(Guid sessionId, int rowNumber, string companyName, string contactFirstName,
            string contactLastName, string contactEmail, string contactPhone, string contactMobile,
            string contactExt, string boothNumbers, string contactType)
        {
            var row = new ExhibitorImportRows
            {
                SessionId = sessionId,
                RowNumber = rowNumber,
                Status = "Pending",
                CompanyName = companyName,
                ContactFirstName = contactFirstName,
                ContactLastName = contactLastName,
                ContactEmail = contactEmail,
                ContactPhone = contactPhone,
                ContactMobile = contactMobile,
                ContactExt = contactExt,
                BoothNumbers = boothNumbers,
                ContactType = contactType,
                IsNewCompany = false,
                IsNewContact = false,
                IsDuplicate = false,
                HasErrors = false,
                HasWarnings = false,
                ErrorCount = 0,
                WarningCount = 0
            };

            _context.ExhibitorImportRows.Add(row);
            _context.SaveChanges();
            return row.Id;
        }

        public bool UpdateRowValidationResults(int rowId, string status, bool hasErrors, bool hasWarnings,
            int errorCount, int warningCount)
        {
            var row = _context.ExhibitorImportRows.FirstOrDefault(r => r.Id == rowId);
            if (row == null) return false;

            row.Status = status;
            row.HasErrors = hasErrors;
            row.HasWarnings = hasWarnings;
            row.ErrorCount = errorCount;
            row.WarningCount = warningCount;
            _context.SaveChanges();
            return true;
        }

        public bool UpdateRowResolvedValues(int rowId, int? resolvedCompanyId, int? resolvedContactTypeId,
            string[] resolvedBoothNumbers, bool isNewCompany, bool isNewContact, bool isDuplicate)
        {
            var row = _context.ExhibitorImportRows.FirstOrDefault(r => r.Id == rowId);
            if (row == null) return false;

            row.ResolvedCompanyId = resolvedCompanyId;
            row.ResolvedContactTypeId = resolvedContactTypeId;
            row.ResolvedBoothNumbersArray = resolvedBoothNumbers;
            row.IsNewCompany = isNewCompany;
            row.IsNewContact = isNewContact;
            row.IsDuplicate = isDuplicate;
            _context.SaveChanges();
            return true;
        }

        public bool UpdateRowProcessingResults(int rowId, int? createdCompanyId, int? createdContactId,
            int? createdExhibitorId, int? createdUserId)
        {
            var row = _context.ExhibitorImportRows.FirstOrDefault(r => r.Id == rowId);
            if (row == null) return false;

            row.CreatedCompanyId = createdCompanyId;
            row.CreatedContactId = createdContactId;
            row.CreatedExhibitorId = createdExhibitorId;
            row.CreatedUserId = createdUserId;
            row.ProcessedAt = DateTime.UtcNow;
            _context.SaveChanges();
            return true;
        }

        public List<ExhibitorImportRows> GetSessionRows(Guid sessionId)
        {
            return _context.ExhibitorImportRows
                .Where(r => r.SessionId == sessionId)
                .OrderBy(r => r.RowNumber)
                .ToList();
        }

        public ExhibitorImportRows? GetRowById(int rowId)
        {
            return _context.ExhibitorImportRows.FirstOrDefault(r => r.Id == rowId);
        }

        // =====================================================
        // Validation Messages
        // =====================================================

        public int CreateValidationMessage(Guid sessionId, int rowId, int rowNumber, string fieldName,
            string fieldValue, string messageType, string validationRule, string messageCode, string message)
        {
            var validationMessage = new ExhibitorImportValidationMessages
            {
                SessionId = sessionId,
                RowId = rowId,
                RowNumber = rowNumber,
                FieldName = fieldName,
                FieldValue = fieldValue,
                MessageType = messageType,
                ValidationRule = validationRule,
                MessageCode = messageCode,
                Message = message,
                CreatedAt = DateTime.UtcNow
            };

            _context.ExhibitorImportValidationMessages.Add(validationMessage);
            _context.SaveChanges();
            return validationMessage.Id;
        }

        public List<ExhibitorImportValidationMessages> GetSessionValidationMessages(Guid sessionId)
        {
            return _context.ExhibitorImportValidationMessages
                .Where(vm => vm.SessionId == sessionId)
                .OrderBy(vm => vm.RowNumber)
                .ThenBy(vm => vm.MessageType)
                .ToList();
        }

        public List<ExhibitorImportValidationMessages> GetRowValidationMessages(int rowId)
        {
            return _context.ExhibitorImportValidationMessages
                .Where(vm => vm.RowId == rowId)
                .OrderBy(vm => vm.MessageType)
                .ToList();
        }

        // =====================================================
        // Duplicate Management
        // =====================================================

        public int CreateDuplicate(Guid sessionId, string duplicateType, string duplicateValue,
            string rowNumbers, string rowIds, string conflictResolution)
        {
            var duplicate = new ExhibitorImportDuplicates
            {
                SessionId = sessionId,
                DuplicateType = duplicateType,
                DuplicateValue = duplicateValue,
                RowNumbers = rowNumbers,
                RowIds = rowIds,
                ConflictResolution = conflictResolution,
                CreatedAt = DateTime.UtcNow
            };

            _context.ExhibitorImportDuplicates.Add(duplicate);
            _context.SaveChanges();
            return duplicate.Id;
        }

        public List<ExhibitorImportDuplicates> GetSessionDuplicates(Guid sessionId)
        {
            return _context.ExhibitorImportDuplicates
                .Where(d => d.SessionId == sessionId)
                .OrderBy(d => d.DuplicateType)
                .ThenBy(d => d.DuplicateValue)
                .ToList();
        }

        public bool UpdateDuplicateResolution(int duplicateId, string conflictResolution)
        {
            var duplicate = _context.ExhibitorImportDuplicates.FirstOrDefault(d => d.Id == duplicateId);
            if (duplicate == null) return false;

            duplicate.ConflictResolution = conflictResolution;
            _context.SaveChanges();
            return true;
        }

        // =====================================================
        // Lookup and Validation Helpers
        // =====================================================

        public List<Company> FindCompaniesByName(string companyName)
        {
            return _context.Company
                .Where(c => c.CompanyName.ToLower().Contains(companyName.ToLower()))
                .OrderBy(c => c.CompanyName)
                .ToList();
        }

        public List<Contact> FindContactsByEmail(string email)
        {
            return _context.Contact
                .Include(c => c.Company)
                .Where(c => c.Email.ToLower() == email.ToLower())
                .ToList();
        }

        public List<ContactType> GetAllContactTypes()
        {
            return _context.ContactType
                .OrderBy(ct => ct.Name)
                .ToList();
        }

        public ContactType? FindContactTypeByText(string contactTypeText)
        {
            if (string.IsNullOrWhiteSpace(contactTypeText))
                return _context.ContactType.FirstOrDefault(ct => ct.Code == "PRIMARY");

            var cleanText = contactTypeText.Trim().ToLower();

            // Try exact match first
            var exactMatch = _context.ContactType
                .FirstOrDefault(ct => ct.Name.ToLower() == cleanText || ct.Code.ToLower() == cleanText);
            if (exactMatch != null) return exactMatch;

            // Try partial match
            var partialMatch = _context.ContactType
                .FirstOrDefault(ct => ct.Name.ToLower().Contains(cleanText) || cleanText.Contains(ct.Name.ToLower()));
            if (partialMatch != null) return partialMatch;

            // Common mappings
            var mappings = new Dictionary<string, string>
            {
                { "primary", "PRIMARY" },
                { "main", "PRIMARY" },
                { "principal", "PRIMARY" },
                { "manager", "MANAGER" },
                { "mgr", "MANAGER" },
                { "executive", "MANAGER" },
                { "director", "MANAGER" },
                { "sales", "SALES" },
                { "sales rep", "SALES" },
                { "salesperson", "SALES" },
                { "marketing", "MARKETING" },
                { "tech", "TECHNICAL" },
                { "technical", "TECHNICAL" },
                { "admin", "ADMIN" },
                { "administrative", "ADMIN" },
                { "billing", "BILLING" },
                { "finance", "BILLING" },
                { "support", "SUPPORT" },
                { "general", "GENERAL" },
                { "other", "GENERAL" }
            };

            if (mappings.ContainsKey(cleanText))
            {
                return _context.ContactType.FirstOrDefault(ct => ct.Code == mappings[cleanText]);
            }

            // Default to PRIMARY if no match found
            return _context.ContactType.FirstOrDefault(ct => ct.Code == "PRIMARY");
        }

        public bool IsBoothNumberAvailable(int showId, string boothNumber, Guid? excludeSessionId = null)
        {
            // Check existing exhibitors
            var existingBooth = _context.ShowExhibitors
                .Any(e => e.ShowId == showId && e.BoothNumber != null && e.BoothNumber.Contains(boothNumber));

            if (existingBooth) return false;

            // Check other import sessions (excluding current session)
            var importedBooth = _context.ExhibitorImportRows
                .Where(r => r.SessionNavigation.ShowId == showId &&
                           r.ResolvedBoothNumbersArray != null &&
                           r.ResolvedBoothNumbersArray.Contains(boothNumber))
                .Where(r => excludeSessionId == null || r.SessionId != excludeSessionId)
                .Any();

            return !importedBooth;
        }

        public List<string> GetUsedBoothNumbers(int showId)
        {
            var existingBooths = _context.ShowExhibitors
                .Where(e => e.ShowId == showId && e.BoothNumber != null)
                .AsEnumerable()
                .SelectMany(e => e.BoothNumber)
                .ToList();

            var importedBooths = _context.ExhibitorImportRows
                .Where(r => r.SessionNavigation.ShowId == showId && r.ResolvedBoothNumbersArray != null)
                .AsEnumerable()
                .SelectMany(r => r.ResolvedBoothNumbersArray)
                .ToList();

            return existingBooths.Concat(importedBooths).Distinct().OrderBy(b => b).ToList();
        }

        // =====================================================
        // Statistics and Reporting
        // =====================================================

        public Dictionary<string, int> GetSessionStatistics(Guid sessionId)
        {
            var session = _context.ExhibitorImportSessions.FirstOrDefault(s => s.SessionId == sessionId);
            if (session == null) return new Dictionary<string, int>();

            var rows = _context.ExhibitorImportRows.Where(r => r.SessionId == sessionId);

            return new Dictionary<string, int>
            {
                { "TotalRows", session.TotalRows },
                { "ValidRows", session.ValidRows },
                { "ErrorRows", session.ErrorRows },
                { "WarningRows", session.WarningRows },
                { "NewCompanies", rows.Count(r => r.IsNewCompany) },
                { "ExistingCompanies", rows.Count(r => !r.IsNewCompany && r.ResolvedCompanyId.HasValue) },
                { "NewContacts", rows.Count(r => r.IsNewContact) },
                { "ProcessedRows", rows.Count(r => r.ProcessedAt.HasValue) }
            };
        }

        public List<ExhibitorImportSessions> GetRecentSessions(int showId, int limit = 10)
        {
            return _context.ExhibitorImportSessions
                .Include(s => s.CreatedBy)
                .Where(s => s.ShowId == showId)
                .OrderByDescending(s => s.CreatedAt)
                .Take(limit)
                .ToList();
        }

        public List<ExhibitorImportSessions> GetActiveSessionsByUser(int userId)
        {
            return _context.ExhibitorImportSessions
                .Include(s => s.Show)
                .Where(s => s.CreatedById == userId && s.Status != "Completed" && s.Status != "Failed")
                .OrderByDescending(s => s.CreatedAt)
                .ToList();
        }
    }
}
