﻿using goodkey_cms.DTO;
using goodkey_cms.DTO.Offering;
using goodkey_common.DTO;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace Goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class GroupTypeController : Controller
	{
		private readonly IGroupTypeRepository _repo;
		public GroupTypeController(IGroupTypeRepository repo)
		{
			_repo = repo;
		}

		[HttpGet]
		public async Task<GenericRespond<IEnumerable<BasicDetail>>> GetBrief()
		{
			var data = await _repo.GetAllAsync();

			var list = data.Select(x => new BasicDetail
			{
				Id = x.Id,
				Name = x.Name
			}).ToList();

			return new GenericRespond<IEnumerable<BasicDetail>>
			{
				Data = list,
				StatusCode = 200,
				Message = "Group Type retrieved successfully"
			};
		}


		[HttpGet("{groupId}/get")]
		public async Task<GenericRespond<GroupTypeWithCategoriesDto>> GetAllSorted(int groupId)
		{
			// Fetch the GroupType data with related categories and offerings
			var groupType = await _repo.GetAll(groupId);

			// Check if the groupType exists (important for handling the case where groupId is not found)
			if (groupType == null)
			{
				return new GenericRespond<GroupTypeWithCategoriesDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Group type not found."
				};
			}

			// Prepare the grouped offerings by category
			var groupedByCategory = groupType.Category
				.Select(category => new CategoryWithOfferingsDto
				{
					CategoryId = category.Id,
					CategoryName = category.Name,
					Offerings = category.Offering
						.Select(offering => new OfferingDto
						{
							Id = offering.Id,
							Name = offering.Name,
							Code = offering.Code,
							SupplierItemNumber = offering.SupplierItemNumber,
							PublicDescription = offering.PublicDescription,
							IsActive = offering.IsActive,
							IsObsolete = offering.IsObsolete
						}).ToList()
				}).ToList();

			// Create the result object to structure the response
			var result = new GroupTypeWithCategoriesDto
			{
				GroupTypeId = groupId,
				GroupTypeName = groupType.Name,  // GroupType Name
				Categories = groupedByCategory  // Grouped Categories with Offerings
			};

			// Return the response with the structured data
			return new GenericRespond<GroupTypeWithCategoriesDto>
			{
				Data = result,
				StatusCode = 200,
				Message = "Offerings grouped by categories under group type retrieved successfully"
			};
		}

	}
}
