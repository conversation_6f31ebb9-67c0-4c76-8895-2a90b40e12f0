using goodkey_cms.Infrastructure.Extensions;
using goodkey_cms.Repositories;
using goodkey_common.DTO;
using goodkey_common.DTO.Ground;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class GroundServicesController : ControllerBase
	{
		private readonly IGroundServiceRepository _repo;
		private readonly IUserRepository _userRepo;

		public GroundServicesController(IGroundServiceRepository repo, IUserRepository userRepo)
		{
			_repo = repo;
			_userRepo = userRepo;
		}

		[HttpGet]
		public GenericRespond<IEnumerable<GroundServiceDto>> GetAll()
		{
			var data = _repo.GetAll().OrderByDescending(x => x.Id).Select(x => new GroundServiceDto
			{
				Id = x.Id,
				Name = x.Name,
				Description = x.Description,
				Conditions = x.Conditions,
				EnteredDate = x.EnteredDate,
				EnteredBy = x.EnteredBy,
				LocalCartageAppliable = x.LocalCartageAppliable,
				FlatRate = x.FlatRate
			});
			return new()
			{
				Data = data,
				StatusCode = 200,
				Message = "Ground services retrieved successfully"
			};
		}

		[HttpGet("{id}")]
		public GenericRespond<GroundServiceDto> GetById(int id)
		{
			var x = _repo.GetById(id);
			if (x == null)
				return new() { Data = null, StatusCode = 404, Message = "Ground service not found" };
			var dto = new GroundServiceDto
			{
				Id = x.Id,
				Name = x.Name,
				Description = x.Description,
				Conditions = x.Conditions,
				EnteredDate = x.EnteredDate,
				EnteredBy = x.EnteredBy,
				LocalCartageAppliable = x.LocalCartageAppliable,
				FlatRate = x.FlatRate
			};
			return new() { Data = dto, StatusCode = 200, Message = "Ground service found" };
		}

		[HttpPost]
		public GenericRespond<GroundServiceDto> Create([FromBody] CreateGroundServiceDto model)
		{
			var username = Request.HttpContext.GetUsername();
			var user = _userRepo.GetUserByUsername(username);

			var entity = new GroundServices
			{
				Name = model.Name,
				Description = model.Description,
				Conditions = model.Conditions,
				EnteredDate = DateTime.Now,
				EnteredBy = user?.UserId,
				LocalCartageAppliable = model.LocalCartageAppliable,
				FlatRate = model.FlatRate
			};
			var created = _repo.Add(entity);
			var dto = new GroundServiceDto
			{
				Id = created.Id,
				Name = created.Name,
				Description = created.Description,
				Conditions = created.Conditions,
				EnteredDate = created.EnteredDate,
				EnteredBy = created.EnteredBy,
				LocalCartageAppliable = created.LocalCartageAppliable,
				FlatRate = created.FlatRate
			};
			return new() { Data = dto, StatusCode = 201, Message = "Ground service created successfully" };
		}

		[HttpPatch("{id}")]
		public GenericRespond<GroundServiceDto> Update(int id, [FromBody] UpdateGroundServiceDto model)
		{
			var entity = _repo.GetById(id);
			if (entity == null)
				return new() { Data = null, StatusCode = 404, Message = "Ground service not found" };

			var username = Request.HttpContext.GetUsername();
			var user = _userRepo.GetUserByUsername(username);

			entity.Name = model.Name;
			entity.Description = model.Description;
			entity.Conditions = model.Conditions;
			entity.EnteredDate = DateTime.Now;
			entity.EnteredBy = user?.UserId;
			entity.LocalCartageAppliable = model.LocalCartageAppliable;
			entity.FlatRate = model.FlatRate;
			var updated = _repo.Update(entity);
			var dto = new GroundServiceDto
			{
				Id = updated.Id,
				Name = updated.Name,
				Description = updated.Description,
				Conditions = updated.Conditions,
				EnteredDate = updated.EnteredDate,
				EnteredBy = updated.EnteredBy,
				LocalCartageAppliable = updated.LocalCartageAppliable,
				FlatRate = updated.FlatRate
			};
			return new() { Data = dto, StatusCode = 200, Message = "Ground service updated successfully" };
		}

		[HttpDelete("{id}")]
		public GenericRespond<bool> Delete(int id)
		{
			var result = _repo.Delete(id);
			return new()
			{
				Data = result,
				StatusCode = result ? 200 : 404,
				Message = result ? "Ground service deleted successfully" : "Ground service not found"
			};
		}
	}
}