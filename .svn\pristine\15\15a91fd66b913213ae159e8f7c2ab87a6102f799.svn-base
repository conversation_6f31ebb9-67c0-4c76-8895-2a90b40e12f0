using goodkey_common.Models;
using goodkey_common.Context;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
    public interface IShowRepository
    {
        IEnumerable<Shows> GetAll();
        Shows GetById(int id);
        Shows Add(Shows show);
        Shows Update(Shows show);
        bool Delete(int id);
        bool ToggleArchive(int id);
    }

    public class ShowRepository : IShowRepository
    {
        private readonly GoodkeyContext _context;

        public ShowRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public IEnumerable<Shows> GetAll()
        {
            return _context.Shows
                .Include(s => s.CreatedByNavigation)
                .Include(s => s.LocationNavigation)
                .Include(s => s.Province)
                .ToList();
        }

        public Shows GetById(int id)
        {
            return _context.Shows
                .Include(s => s.CreatedByNavigation)
                .Include(s => s.LocationNavigation)
                .Include(s => s.Province)
                .FirstOrDefault(s => s.Id == id);
        }

        public Shows Add(Shows show)
        {
            _context.Shows.Add(show);
            _context.SaveChanges();
            return show;
        }

        public Shows Update(Shows show)
        {
            var existingShow = _context.Shows.Find(show.Id);
            if (existingShow == null)
                return null;

            _context.Entry(existingShow).CurrentValues.SetValues(show);
            _context.SaveChanges();
            return show;
        }

        public bool Delete(int id)
        {
            var show = _context.Shows.Find(id);
            if (show == null)
                return false;

            _context.Shows.Remove(show);
            _context.SaveChanges();
            return true;
        }

        public bool ToggleArchive(int id)
        {
            var show = _context.Shows.Find(id);
            if (show == null)
                return false;

            show.Archive = !show.Archive;
            _context.SaveChanges();
            return true;
        }
    }
} 