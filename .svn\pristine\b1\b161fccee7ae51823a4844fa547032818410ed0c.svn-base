using goodkey_common.DTO;
using goodkey_common.DTO.ContactType;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class ContactTypeController : ControllerBase
	{
		private readonly IContactTypeRepository _repo;

		public ContactTypeController(IContactTypeRepository repo)
		{
			_repo = repo;
		}

		[HttpGet]
		public GenericRespond<IEnumerable<ContactTypeDto>> GetAll()
		{
			var contactTypes = _repo.GetAll();

			var result = contactTypes.OrderByDescending(x => x.Id).Select(item => new ContactTypeDto
			{
				Id = item.Id,
				Name = item.Name,
				Code = item.Code
			});

			return new GenericRespond<IEnumerable<ContactTypeDto>>
			{
				Data = result
			};
		}

		[HttpGet("{id:int}")]
		public GenericRespond<ContactTypeDto> Get(int id)
		{
			var item = _repo.Get(id);
			if (item == null)
			{
				return new GenericRespond<ContactTypeDto>
				{
					Data = null,
					Message = "Contact type not found",
					StatusCode = 404
				};
			}

			return new GenericRespond<ContactTypeDto>
			{
				Data = new ContactTypeDto
				{
					Id = item.Id,
					Name = item.Name,
					Code = item.Code
				}
			};
		}
	}
}
