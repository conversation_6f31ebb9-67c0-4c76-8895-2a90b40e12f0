﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class DocumentFileTypes
    {
        public DocumentFileTypes()
        {
            DocCategoryType = new HashSet<DocCategoryType>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public string ExtensionCode { get; set; }
        public string Extension { get; set; }
        public bool? IsAvailable { get; set; }
        public DateTime CreatedAt { get; set; }
        public int CreatedById { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? UpdatedById { get; set; }
        public bool IsImage { get; set; }

        public virtual AuthUser CreatedBy { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
        public virtual ICollection<DocCategoryType> DocCategoryType { get; set; }
    }
}