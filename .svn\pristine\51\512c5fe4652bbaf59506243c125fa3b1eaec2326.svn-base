﻿using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.DTO.Property
{
	// PropertyOptionDto.cs
	public class PropertyOptionDto : PropertyOptionCreateDto
	{
		public int Id { get; set; }
	}

	// PropertyOptionCreateDto.cs
	public class PropertyOptionCreateDto
	{
		public int PropertyId { get; set; }
		public string? Name { get; set; }
		public string? Description { get; set; }
		public int? DisplayOrder { get; set; }
	}
}
