﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using goodkey_common.Models;

namespace goodkey_common.Context
{
    public partial class GoodkeyContext : DbContext
    {
        public GoodkeyContext()
        {
        }

        public GoodkeyContext(DbContextOptions<GoodkeyContext> options)
            : base(options)
        {
        }

        public virtual DbSet<AuthGroup> AuthGroup { get; set; }
        public virtual DbSet<AuthGroupRole> AuthGroupRole { get; set; }
        public virtual DbSet<AuthPermission> AuthPermission { get; set; }
        public virtual DbSet<AuthRole> AuthRole { get; set; }
        public virtual DbSet<AuthStatus> AuthStatus { get; set; }
        public virtual DbSet<AuthUser> AuthUser { get; set; }
        public virtual DbSet<Category> Category { get; set; }
        public virtual DbSet<CategoryProperty> CategoryProperty { get; set; }
        public virtual DbSet<Company> Company { get; set; }
        public virtual DbSet<CompanyGroup> CompanyGroup { get; set; }
        public virtual DbSet<Contact> Contact { get; set; }
        public virtual DbSet<ContactType> ContactType { get; set; }
        public virtual DbSet<Countries> Countries { get; set; }
        public virtual DbSet<Department> Department { get; set; }
        public virtual DbSet<DocCategory> DocCategory { get; set; }
        public virtual DbSet<DocCategoryType> DocCategoryType { get; set; }
        public virtual DbSet<DocumentFileTypes> DocumentFileTypes { get; set; }
        public virtual DbSet<GroupType> GroupType { get; set; }
        public virtual DbSet<Language> Language { get; set; }
        public virtual DbSet<Menu> Menu { get; set; }
        public virtual DbSet<MenuItem> MenuItem { get; set; }
        public virtual DbSet<MenuSection> MenuSection { get; set; }
        public virtual DbSet<Property> Property { get; set; }
        public virtual DbSet<PropertyOption> PropertyOption { get; set; }
        public virtual DbSet<Provinces> Provinces { get; set; }
        public virtual DbSet<Salutation> Salutation { get; set; }
        public virtual DbSet<Schedules> Schedules { get; set; }
        public virtual DbSet<Sections> Sections { get; set; }
        public virtual DbSet<ShowDocs> ShowDocs { get; set; }
        public virtual DbSet<ShowLocationHalls> ShowLocationHalls { get; set; }
        public virtual DbSet<ShowLocations> ShowLocations { get; set; }
        public virtual DbSet<TaxProvince> TaxProvince { get; set; }
        public virtual DbSet<TaxType> TaxType { get; set; }
        public virtual DbSet<Warehouse> Warehouse { get; set; }
        public virtual DbSet<WarehouseTypes> WarehouseTypes { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasPostgresExtension("dblink");

            modelBuilder.Entity<AuthGroup>(entity =>
            {
                entity.HasKey(e => e.GroupId)
                    .HasName("client_group_pkey");

                entity.ToTable("auth_group");

                entity.Property(e => e.GroupId).HasColumnName("group_id");

                entity.Property(e => e.GroupName)
                    .HasMaxLength(100)
                    .HasColumnName("group_name");

                entity.Property(e => e.MaxLevel).HasColumnName("max_level");

                entity.Property(e => e.MinLevel).HasColumnName("min_level");
            });

            modelBuilder.Entity<AuthGroupRole>(entity =>
            {
                entity.HasKey(e => new { e.GroupId, e.RoleId })
                    .HasName("auth_group_role_pkey");

                entity.ToTable("auth_group_role");

                entity.Property(e => e.GroupId).HasColumnName("group_id");

                entity.Property(e => e.RoleId).HasColumnName("role_id");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.CreatedDate)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_date")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.AuthGroupRole)
                    .HasForeignKey(d => d.CreatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_group_role_created_by");

                entity.HasOne(d => d.Group)
                    .WithMany(p => p.AuthGroupRole)
                    .HasForeignKey(d => d.GroupId)
                    .HasConstraintName("fk_group_role_group");

                entity.HasOne(d => d.Role)
                    .WithMany(p => p.AuthGroupRole)
                    .HasForeignKey(d => d.RoleId)
                    .HasConstraintName("fk_group_role_role");
            });

            modelBuilder.Entity<AuthPermission>(entity =>
            {
                entity.HasKey(e => e.PermissionId)
                    .HasName("auth_permission_pkey");

                entity.ToTable("auth_permission");

                entity.Property(e => e.PermissionId).HasColumnName("permission_id");

                entity.Property(e => e.Code)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("code");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("name");
            });

            modelBuilder.Entity<AuthRole>(entity =>
            {
                entity.HasKey(e => e.RoleId)
                    .HasName("auth_role_pkey");

                entity.ToTable("auth_role");

                entity.Property(e => e.RoleId).HasColumnName("role_id");

                entity.Property(e => e.Description)
                    .HasMaxLength(300)
                    .HasColumnName("description");

                entity.Property(e => e.Level).HasColumnName("level");

                entity.Property(e => e.Name)
                    .HasMaxLength(150)
                    .HasColumnName("name");

                entity.HasMany(d => d.Permission)
                    .WithMany(p => p.Role)
                    .UsingEntity<Dictionary<string, object>>(
                        "AuthRolePermission",
                        l => l.HasOne<AuthPermission>().WithMany().HasForeignKey("PermissionId").HasConstraintName("fk_role_permission_permission"),
                        r => r.HasOne<AuthRole>().WithMany().HasForeignKey("RoleId").HasConstraintName("fk_role_permission_role"),
                        j =>
                        {
                            j.HasKey("RoleId", "PermissionId").HasName("auth_role_permission_pkey");

                            j.ToTable("auth_role_permission");

                            j.IndexerProperty<int>("RoleId").HasColumnName("role_id");

                            j.IndexerProperty<int>("PermissionId").HasColumnName("permission_id");
                        });
            });

            modelBuilder.Entity<AuthStatus>(entity =>
            {
                entity.HasKey(e => e.StatusId)
                    .HasName("employee_status_pkey");

                entity.ToTable("auth_status");

                entity.Property(e => e.StatusId).HasColumnName("status_id");

                entity.Property(e => e.StatusName)
                    .HasMaxLength(100)
                    .HasColumnName("status_name");
            });

            modelBuilder.Entity<AuthUser>(entity =>
            {
                entity.HasKey(e => e.UserId)
                    .HasName("auth_user_pkey");

                entity.ToTable("auth_user");

                entity.Property(e => e.UserId).HasColumnName("user_id");

                entity.Property(e => e.ArchivedById).HasColumnName("archived_by_id");

                entity.Property(e => e.ArchivedDate)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("archived_date");

                entity.Property(e => e.AuthGroupId).HasColumnName("auth_group_id");

                entity.Property(e => e.CompanyId).HasColumnName("company_id");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.CreatedDate)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_date");

                entity.Property(e => e.DepartmentId).HasColumnName("department_id");

                entity.Property(e => e.FirstName)
                    .HasMaxLength(255)
                    .HasColumnName("first_name");

                entity.Property(e => e.IsActive)
                    .HasColumnName("is_active")
                    .HasDefaultValueSql("true");

                entity.Property(e => e.IsArchived)
                    .HasColumnName("is_archived")
                    .HasDefaultValueSql("false");

                entity.Property(e => e.IsVerified)
                    .HasColumnName("is_verified")
                    .HasDefaultValueSql("false");

                entity.Property(e => e.LastName)
                    .HasMaxLength(255)
                    .HasColumnName("last_name");

                entity.Property(e => e.MobileNumber)
                    .HasMaxLength(15)
                    .HasColumnName("mobile_number");

                entity.Property(e => e.PasswordHash)
                    .HasMaxLength(255)
                    .HasColumnName("password_hash");

                entity.Property(e => e.RoleId).HasColumnName("role_id");

                entity.Property(e => e.SalutationId).HasColumnName("salutation_id");

                entity.Property(e => e.StatusId).HasColumnName("status_id");

                entity.Property(e => e.UpdateDate)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("update_date");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.Property(e => e.Username)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("username");

                entity.Property(e => e.VerificationEmail)
                    .HasMaxLength(150)
                    .HasColumnName("verification_email");

                entity.Property(e => e.VerificationSentDate)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("verification_sent_date");

                entity.Property(e => e.VerificationToken)
                    .HasMaxLength(255)
                    .HasColumnName("verification_token");

                entity.Property(e => e.WorkEmail)
                    .HasMaxLength(255)
                    .HasColumnName("work_email");

                entity.Property(e => e.WorkPhoneNumber)
                    .HasMaxLength(15)
                    .HasColumnName("work_phone_number");

                entity.HasOne(d => d.ArchivedBy)
                    .WithMany(p => p.InverseArchivedBy)
                    .HasForeignKey(d => d.ArchivedById)
                    .HasConstraintName("fk_auth_user_archived_by");

                entity.HasOne(d => d.AuthGroup)
                    .WithMany(p => p.AuthUser)
                    .HasForeignKey(d => d.AuthGroupId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_auth_group");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.AuthUser)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_auth_user_company");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.InverseCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .HasConstraintName("fk_auth_user_created_by");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.AuthUser)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("fk_auth_user_department");

                entity.HasOne(d => d.Role)
                    .WithMany(p => p.AuthUser)
                    .HasForeignKey(d => d.RoleId)
                    .HasConstraintName("auth_user_role_id_fkey");

                entity.HasOne(d => d.Salutation)
                    .WithMany(p => p.AuthUser)
                    .HasForeignKey(d => d.SalutationId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_auth_user_salutation");

                entity.HasOne(d => d.Status)
                    .WithMany(p => p.AuthUser)
                    .HasForeignKey(d => d.StatusId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_auth_user_status");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.InverseUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .HasConstraintName("fk_auth_user_updated_by");
            });

            modelBuilder.Entity<Category>(entity =>
            {
                entity.ToTable("category");

                entity.HasIndex(e => e.Code, "category_code_key")
                    .IsUnique();

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Code)
                    .HasMaxLength(50)
                    .HasColumnName("code");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.DisplayOrder).HasColumnName("display_order");

                entity.Property(e => e.GroupTypeId).HasColumnName("group_type_id");

                entity.Property(e => e.ImagePath)
                    .HasMaxLength(512)
                    .HasColumnName("image_path");

                entity.Property(e => e.IsAvailable)
                    .HasColumnName("is_available")
                    .HasDefaultValueSql("true");

                entity.Property(e => e.IsInternalProduct)
                    .HasColumnName("is_internal_product")
                    .HasDefaultValueSql("false");

                entity.Property(e => e.IsSoldByQ)
                    .HasColumnName("is_sold_by_q")
                    .HasDefaultValueSql("false");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("name");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updated_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.CategoryCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_category_created_by");

                entity.HasOne(d => d.GroupType)
                    .WithMany(p => p.Category)
                    .HasForeignKey(d => d.GroupTypeId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("fk_group_type");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.CategoryUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_category_updated_by");
            });

            modelBuilder.Entity<CategoryProperty>(entity =>
            {
                entity.HasKey(e => new { e.CategoryId, e.PropertyId })
                    .HasName("category_property_pkey");

                entity.ToTable("category_property");

                entity.Property(e => e.CategoryId).HasColumnName("category_id");

                entity.Property(e => e.PropertyId).HasColumnName("property_id");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_at");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.DisplayOrder)
                    .HasColumnName("display_order")
                    .HasDefaultValueSql("0");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updated_at");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.CategoryProperty)
                    .HasForeignKey(d => d.CategoryId)
                    .HasConstraintName("fk_category");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.CategoryPropertyCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_category_property_created_by");

                entity.HasOne(d => d.Property)
                    .WithMany(p => p.CategoryProperty)
                    .HasForeignKey(d => d.PropertyId)
                    .HasConstraintName("fk_property");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.CategoryPropertyUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_category_property_updated_by");
            });

            modelBuilder.Entity<Company>(entity =>
            {
                entity.ToTable("company");

                entity.Property(e => e.CompanyId).HasColumnName("company_id");

                entity.Property(e => e.AccountNumber)
                    .HasMaxLength(100)
                    .HasColumnName("account_number");

                entity.Property(e => e.Address1)
                    .HasMaxLength(255)
                    .HasColumnName("address1");

                entity.Property(e => e.Address2)
                    .HasMaxLength(255)
                    .HasColumnName("address2");

                entity.Property(e => e.City)
                    .HasMaxLength(255)
                    .HasColumnName("city");

                entity.Property(e => e.CompanyGroupId).HasColumnName("company_group_id");

                entity.Property(e => e.CompanyName)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("company_name");

                entity.Property(e => e.CountryId).HasColumnName("country_id");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.Email)
                    .HasMaxLength(255)
                    .HasColumnName("email");

                entity.Property(e => e.IsArchived)
                    .HasColumnName("is_archived")
                    .HasDefaultValueSql("false");

                entity.Property(e => e.Note).HasColumnName("note");

                entity.Property(e => e.Phone)
                    .HasMaxLength(20)
                    .HasColumnName("phone");

                entity.Property(e => e.PostalCode)
                    .HasMaxLength(20)
                    .HasColumnName("postal_code");

                entity.Property(e => e.ProvinceId).HasColumnName("province_id");

                entity.Property(e => e.Updatedat)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updatedat");

                entity.Property(e => e.Updatedbyid).HasColumnName("updatedbyid");

                entity.Property(e => e.WebsiteUrl)
                    .HasMaxLength(255)
                    .HasColumnName("website_url");

                entity.HasOne(d => d.CompanyGroup)
                    .WithMany(p => p.Company)
                    .HasForeignKey(d => d.CompanyGroupId)
                    .HasConstraintName("fk_company_company_group");

                entity.HasOne(d => d.Country)
                    .WithMany(p => p.Company)
                    .HasForeignKey(d => d.CountryId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_country");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.CompanyCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .HasConstraintName("fk_company_created_by");

                entity.HasOne(d => d.Province)
                    .WithMany(p => p.Company)
                    .HasForeignKey(d => d.ProvinceId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_province");

                entity.HasOne(d => d.Updatedby)
                    .WithMany(p => p.CompanyUpdatedby)
                    .HasForeignKey(d => d.Updatedbyid)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_company_updatedby");
            });

            modelBuilder.Entity<CompanyGroup>(entity =>
            {
                entity.ToTable("company_group");

                entity.Property(e => e.CompanyGroupId).HasColumnName("company_group_id");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("name");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updated_at");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.CompanyGroupCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .HasConstraintName("fk_company_group_created_by");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.CompanyGroupUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .HasConstraintName("fk_company_group_updated_by");
            });

            modelBuilder.Entity<Contact>(entity =>
            {
                entity.ToTable("contact");

                entity.Property(e => e.ContactId).HasColumnName("contact_id");

                entity.Property(e => e.ArchivedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("archived_at");

                entity.Property(e => e.ArchivedById).HasColumnName("archived_by_id");

                entity.Property(e => e.Authuserid).HasColumnName("authuserid");

                entity.Property(e => e.Cellphone)
                    .HasMaxLength(20)
                    .HasColumnName("cellphone");

                entity.Property(e => e.CompanyId).HasColumnName("company_id");

                entity.Property(e => e.ContactTypeId).HasColumnName("contact_type_id");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_at");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.Email)
                    .HasMaxLength(250)
                    .HasColumnName("email");

                entity.Property(e => e.Ext)
                    .HasMaxLength(10)
                    .HasColumnName("ext");

                entity.Property(e => e.FirstName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("first_name");

                entity.Property(e => e.IsArchived)
                    .HasColumnName("is_archived")
                    .HasDefaultValueSql("false");

                entity.Property(e => e.LastName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("last_name");

                entity.Property(e => e.LocationId).HasColumnName("location_id");

                entity.Property(e => e.Telephone)
                    .HasMaxLength(20)
                    .HasColumnName("telephone");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updated_at");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.HasOne(d => d.ArchivedBy)
                    .WithMany(p => p.ContactArchivedBy)
                    .HasForeignKey(d => d.ArchivedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_contact_archived_by");

                entity.HasOne(d => d.Authuser)
                    .WithMany(p => p.ContactAuthuser)
                    .HasForeignKey(d => d.Authuserid)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("contact_authuserid_fkey");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Contact)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("fk_company_contact");

                entity.HasOne(d => d.ContactType)
                    .WithMany(p => p.Contact)
                    .HasForeignKey(d => d.ContactTypeId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("fk_contact_type");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.ContactCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_contact_created_by");

                entity.HasOne(d => d.Location)
                    .WithMany(p => p.Contact)
                    .HasForeignKey(d => d.LocationId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_location_contact");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.ContactUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_contact_updated_by");
            });

            modelBuilder.Entity<ContactType>(entity =>
            {
                entity.ToTable("contact_type");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Code)
                    .HasMaxLength(20)
                    .HasColumnName("code");

                entity.Property(e => e.Name)
                    .HasMaxLength(100)
                    .HasColumnName("name");
            });

            modelBuilder.Entity<Countries>(entity =>
            {
                entity.HasKey(e => e.CountryId)
                    .HasName("Countries_pkey");

                entity.ToTable("countries");

                entity.Property(e => e.CountryId)
                    .HasColumnName("Country_ID")
                    .UseIdentityAlwaysColumn();

                entity.Property(e => e.CountryCode).HasColumnName("Country_Code");

                entity.Property(e => e.CountryName).HasColumnName("Country_Name");

                entity.Property(e => e.CountryNameFr).HasColumnName("Country_Name_Fr");
            });

            modelBuilder.Entity<Department>(entity =>
            {
                entity.ToTable("department");

                entity.Property(e => e.DepartmentId)
                    .HasColumnName("department_id")
                    .HasDefaultValueSql("nextval('employee_department_department_id_seq'::regclass)");

                entity.Property(e => e.Code)
                    .HasMaxLength(3)
                    .HasColumnName("code");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_at");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.Name)
                    .HasMaxLength(150)
                    .HasColumnName("name");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updated_at");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.DepartmentCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .HasConstraintName("fk_department_created_by");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.DepartmentUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .HasConstraintName("fk_department_updated_by");
            });

            modelBuilder.Entity<DocCategory>(entity =>
            {
                entity.ToTable("doc_category");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasDefaultValueSql("nextval('doc_group_id_seq'::regclass)");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("name");

                entity.Property(e => e.SectionId).HasColumnName("section_id");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updated_at");

                entity.HasOne(d => d.Section)
                    .WithMany(p => p.DocCategory)
                    .HasForeignKey(d => d.SectionId)
                    .HasConstraintName("fk_doc_group_section");
            });

            modelBuilder.Entity<DocCategoryType>(entity =>
            {
                entity.ToTable("doc_category_type");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.DocCategoryId).HasColumnName("doc_category_id");

                entity.Property(e => e.DocTypeId).HasColumnName("doc_type_id");

                entity.HasOne(d => d.DocCategory)
                    .WithMany(p => p.DocCategoryType)
                    .HasForeignKey(d => d.DocCategoryId)
                    .HasConstraintName("fk_doc_category_type_category");

                entity.HasOne(d => d.DocType)
                    .WithMany(p => p.DocCategoryType)
                    .HasForeignKey(d => d.DocTypeId)
                    .HasConstraintName("fk_doc_category_type_type");
            });

            modelBuilder.Entity<DocumentFileTypes>(entity =>
            {
                entity.ToTable("document_file_types");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.CreatedAt)
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.Extension)
                    .IsRequired()
                    .HasMaxLength(10)
                    .HasColumnName("extension");

                entity.Property(e => e.ExtensionCode)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("extension_code");

                entity.Property(e => e.IsAvailable)
                    .HasColumnName("is_available")
                    .HasDefaultValueSql("true");

                entity.Property(e => e.IsImage).HasColumnName("is_image");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("name");

                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.DocumentFileTypesCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("fk_document_file_types_created_by");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.DocumentFileTypesUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_document_file_types_updated_by");
            });

            modelBuilder.Entity<GroupType>(entity =>
            {
                entity.ToTable("group_type");

                entity.HasIndex(e => e.Name, "group_type_name_key")
                    .IsUnique();

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasColumnName("name");
            });

            modelBuilder.Entity<Language>(entity =>
            {
                entity.HasKey(e => e.LangCode)
                    .HasName("language_pkey");

                entity.ToTable("language");

                entity.Property(e => e.LangCode)
                    .HasMaxLength(3)
                    .HasColumnName("lang_code");

                entity.Property(e => e.DisplayOrder).HasColumnName("display_order");

                entity.Property(e => e.LangCulture)
                    .IsRequired()
                    .HasMaxLength(10)
                    .HasColumnName("lang_culture");

                entity.Property(e => e.LangEng)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("lang_eng");

                entity.Property(e => e.LangFra)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("lang_fra");

                entity.Property(e => e.LangNative)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("lang_native");
            });

            modelBuilder.Entity<Menu>(entity =>
            {
                entity.ToTable("menu");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.HasMany(d => d.Role)
                    .WithMany(p => p.Menu)
                    .UsingEntity<Dictionary<string, object>>(
                        "MenuRole",
                        l => l.HasOne<AuthRole>().WithMany().HasForeignKey("RoleId").HasConstraintName("fk_menu_role_role"),
                        r => r.HasOne<Menu>().WithMany().HasForeignKey("MenuId").HasConstraintName("fk_menu_role_menu"),
                        j =>
                        {
                            j.HasKey("MenuId", "RoleId").HasName("pk_menu_role");

                            j.ToTable("menu_role");

                            j.IndexerProperty<int>("MenuId").HasColumnName("menu_id");

                            j.IndexerProperty<int>("RoleId").HasColumnName("role_id");
                        });
            });

            modelBuilder.Entity<MenuItem>(entity =>
            {
                entity.ToTable("menu_item");

                entity.Property(e => e.MenuItemId).HasColumnName("menu_item_id");

                entity.Property(e => e.Description).HasColumnName("description");

                entity.Property(e => e.Direction)
                    .HasMaxLength(3)
                    .HasColumnName("direction");

                entity.Property(e => e.DisplayOrder).HasColumnName("display_order");

                entity.Property(e => e.IconName).HasColumnName("icon_name");

                entity.Property(e => e.ImagePath).HasColumnName("image_path");

                entity.Property(e => e.IsDashboard).HasColumnName("is_dashboard");

                entity.Property(e => e.IsParent).HasColumnName("is_parent");

                entity.Property(e => e.IsStatic).HasColumnName("is_static");

                entity.Property(e => e.IsVisible).HasColumnName("is_visible");

                entity.Property(e => e.Level).HasColumnName("level");

                entity.Property(e => e.MenuId).HasColumnName("menu_id");

                entity.Property(e => e.MetaDescription).HasColumnName("meta_description");

                entity.Property(e => e.MetaKeywords).HasColumnName("meta_keywords");

                entity.Property(e => e.Name).HasColumnName("name");

                entity.Property(e => e.ParentId).HasColumnName("parent_id");

                entity.Property(e => e.PermissionKey)
                    .HasMaxLength(255)
                    .HasColumnName("permission_key");

                entity.Property(e => e.SectionId).HasColumnName("section_id");

                entity.Property(e => e.Target).HasColumnName("target");

                entity.Property(e => e.Url).HasColumnName("url");

                entity.HasOne(d => d.Menu)
                    .WithMany(p => p.MenuItem)
                    .HasForeignKey(d => d.MenuId)
                    .HasConstraintName("fk_menu_item_menu");

                entity.HasOne(d => d.Parent)
                    .WithMany(p => p.InverseParent)
                    .HasForeignKey(d => d.ParentId)
                    .HasConstraintName("fk_menu_item_parent");

                entity.HasOne(d => d.Section)
                    .WithMany(p => p.MenuItem)
                    .HasForeignKey(d => d.SectionId)
                    .HasConstraintName("fk_menu_item_menu_section");

                entity.HasMany(d => d.Role)
                    .WithMany(p => p.MenuItem)
                    .UsingEntity<Dictionary<string, object>>(
                        "MenuItemRole",
                        l => l.HasOne<AuthRole>().WithMany().HasForeignKey("RoleId").HasConstraintName("fk_menu_item_role_role"),
                        r => r.HasOne<MenuItem>().WithMany().HasForeignKey("MenuItemId").HasConstraintName("fk_menu_item_role_menu_item"),
                        j =>
                        {
                            j.HasKey("MenuItemId", "RoleId").HasName("menu_item_role_pkey");

                            j.ToTable("menu_item_role");

                            j.IndexerProperty<int>("MenuItemId").HasColumnName("menu_item_id");

                            j.IndexerProperty<int>("RoleId").HasColumnName("role_id");
                        });
            });

            modelBuilder.Entity<MenuSection>(entity =>
            {
                entity.ToTable("menu_section");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.IsDashboard).HasColumnName("is_dashboard");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasColumnName("name");
            });

            modelBuilder.Entity<Property>(entity =>
            {
                entity.ToTable("property");

                entity.HasIndex(e => e.Code, "property_code_key")
                    .IsUnique();

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Code)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("code");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_at");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.Description).HasColumnName("description");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("name");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updated_at");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.PropertyCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_property_created_by");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.PropertyUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_property_updated_by");
            });

            modelBuilder.Entity<PropertyOption>(entity =>
            {
                entity.ToTable("property_option");

                entity.HasIndex(e => e.Code, "property_option_code_key")
                    .IsUnique();

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Code)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("code");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_at");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.Description).HasColumnName("description");

                entity.Property(e => e.DisplayOrder)
                    .HasColumnName("display_order")
                    .HasDefaultValueSql("0");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("name");

                entity.Property(e => e.PropertyId).HasColumnName("property_id");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updated_at");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.PropertyOptionCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_property_option_created_by");

                entity.HasOne(d => d.Property)
                    .WithMany(p => p.PropertyOption)
                    .HasForeignKey(d => d.PropertyId)
                    .HasConstraintName("fk_property");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.PropertyOptionUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_property_option_updated_by");
            });

            modelBuilder.Entity<Provinces>(entity =>
            {
                entity.HasKey(e => e.ProvinceId)
                    .HasName("provinces_pkey");

                entity.ToTable("provinces");

                entity.Property(e => e.ProvinceId)
                    .HasColumnName("Province_ID")
                    .UseIdentityAlwaysColumn();

                entity.Property(e => e.CountryId).HasColumnName("Country_ID");

                entity.Property(e => e.DisplayOrder)
                    .HasColumnName("Display_Order")
                    .HasDefaultValueSql("1");

                entity.Property(e => e.ProvinceCode).HasColumnName("Province_Code");

                entity.Property(e => e.ProvinceName).HasColumnName("Province_Name");

                entity.Property(e => e.ProvinceNameFr).HasColumnName("Province_Name_Fr");

                entity.HasOne(d => d.Country)
                    .WithMany(p => p.Provinces)
                    .HasForeignKey(d => d.CountryId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("Provinces_Country_ID_fkey");
            });

            modelBuilder.Entity<Salutation>(entity =>
            {
                entity.ToTable("salutation");

                entity.Property(e => e.SalutationId).HasColumnName("salutation_id");

                entity.Property(e => e.SalutationName)
                    .HasMaxLength(255)
                    .HasColumnName("salutation_name");
            });

            modelBuilder.Entity<Schedules>(entity =>
            {
                entity.ToTable("schedules");

                entity.HasIndex(e => e.Code, "schedules_code_key")
                    .IsUnique();

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Code)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("code");

                entity.Property(e => e.CreatedAt)
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.Description).HasColumnName("description");

                entity.Property(e => e.IsActive)
                    .IsRequired()
                    .HasColumnName("is_active")
                    .HasDefaultValueSql("true");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("name");

                entity.Property(e => e.Notes).HasColumnName("notes");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnName("updated_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.SchedulesCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_schedules_created_by");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.SchedulesUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_schedules_updated_by");
            });

            modelBuilder.Entity<Sections>(entity =>
            {
                entity.ToTable("sections");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Name)
                    .HasMaxLength(100)
                    .HasColumnName("name");
            });

            modelBuilder.Entity<ShowDocs>(entity =>
            {
                entity.HasKey(e => e.ShowDocId)
                    .HasName("show_docs_pkey");

                entity.ToTable("show_docs");

                entity.Property(e => e.ShowDocId).HasColumnName("show_doc_id");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.DocCategoryId).HasColumnName("doc_category_id");

                entity.Property(e => e.DocId)
                    .HasColumnName("doc_id")
                    .HasDefaultValueSql("0");

                entity.Property(e => e.FilePath).HasColumnName("file_path");

                entity.Property(e => e.HallId).HasColumnName("hall_id");

                entity.Property(e => e.LocationId).HasColumnName("location_id");

                entity.Property(e => e.Note).HasColumnName("note");

                entity.Property(e => e.OriginalFilename).HasColumnName("original_filename");

                entity.Property(e => e.ShowId).HasColumnName("show_id");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updated_at")
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.Property(e => e.ValidUntil)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("valid_until");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.ShowDocsCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .HasConstraintName("fk_show_docs_created_by");

                entity.HasOne(d => d.DocCategory)
                    .WithMany(p => p.ShowDocs)
                    .HasForeignKey(d => d.DocCategoryId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("fk_show_doc_category");

                entity.HasOne(d => d.Hall)
                    .WithMany(p => p.ShowDocs)
                    .HasForeignKey(d => d.HallId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_show_docs_hall");

                entity.HasOne(d => d.Location)
                    .WithMany(p => p.ShowDocs)
                    .HasForeignKey(d => d.LocationId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_show_docs_location");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.ShowDocsUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .HasConstraintName("fk_show_docs_updated_by");
            });

            modelBuilder.Entity<ShowLocationHalls>(entity =>
            {
                entity.HasKey(e => e.HallId)
                    .HasName("show_location_halls_pkey");

                entity.ToTable("show_location_halls");

                entity.Property(e => e.HallId).HasColumnName("hall_id");

                entity.Property(e => e.AccessDoor)
                    .HasMaxLength(255)
                    .HasColumnName("access_door");

                entity.Property(e => e.ArchivedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("archived_at");

                entity.Property(e => e.ArchivedById).HasColumnName("archived_by_id");

                entity.Property(e => e.BanquetCapacity).HasColumnName("banquet_capacity");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_at");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.FloorPlan)
                    .HasMaxLength(400)
                    .HasColumnName("floor_plan");

                entity.Property(e => e.FloorPlanId).HasColumnName("floor_plan_id");

                entity.Property(e => e.HallArea)
                    .HasPrecision(10, 2)
                    .HasColumnName("hall_area");

                entity.Property(e => e.HallBoothCount).HasColumnName("hall_booth_count");

                entity.Property(e => e.HallCeilingHeight)
                    .HasPrecision(10, 2)
                    .HasColumnName("hall_ceiling_height");

                entity.Property(e => e.HallCode)
                    .HasMaxLength(10)
                    .HasColumnName("hall_code");

                entity.Property(e => e.HallFloorType)
                    .HasColumnType("character varying")
                    .HasColumnName("hall_floor_type");

                entity.Property(e => e.HallLength)
                    .HasPrecision(10, 2)
                    .HasColumnName("hall_length");

                entity.Property(e => e.HallName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("hall_name");

                entity.Property(e => e.HallStyle)
                    .HasMaxLength(100)
                    .HasColumnName("hall_style");

                entity.Property(e => e.HallSurface)
                    .HasPrecision(10, 2)
                    .HasColumnName("hall_surface");

                entity.Property(e => e.HallWidth)
                    .HasPrecision(10, 2)
                    .HasColumnName("hall_width");

                entity.Property(e => e.IsArchived)
                    .HasColumnName("is_archived")
                    .HasDefaultValueSql("false");

                entity.Property(e => e.IsElecOnCeiling).HasColumnName("is_elec_on_ceiling");

                entity.Property(e => e.IsElecOnFloor).HasColumnName("is_elec_on_floor");

                entity.Property(e => e.LoadingDocks).HasColumnName("loading_docks");

                entity.Property(e => e.LocationId).HasColumnName("location_id");

                entity.Property(e => e.OverheadHeight)
                    .HasPrecision(10, 2)
                    .HasColumnName("overhead_height");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updated_at");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.HasOne(d => d.ArchivedBy)
                    .WithMany(p => p.ShowLocationHallsArchivedBy)
                    .HasForeignKey(d => d.ArchivedById)
                    .HasConstraintName("fk_show_location_halls_archived_by");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.ShowLocationHallsCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .HasConstraintName("fk_show_location_halls_created_by");

                entity.HasOne(d => d.FloorPlanNavigation)
                    .WithMany(p => p.ShowLocationHalls)
                    .HasForeignKey(d => d.FloorPlanId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_floor_plan");

                entity.HasOne(d => d.Location)
                    .WithMany(p => p.ShowLocationHalls)
                    .HasForeignKey(d => d.LocationId)
                    .HasConstraintName("fk_location");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.ShowLocationHallsUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .HasConstraintName("fk_show_location_halls_updated_by");
            });

            modelBuilder.Entity<ShowLocations>(entity =>
            {
                entity.HasKey(e => e.LocationId)
                    .HasName("show_locations_pkey");

                entity.ToTable("show_locations");

                entity.Property(e => e.LocationId).HasColumnName("location_id");

                entity.Property(e => e.AccessPlan)
                    .HasMaxLength(255)
                    .HasColumnName("access_plan");

                entity.Property(e => e.AccessPlanId).HasColumnName("access_plan_id");

                entity.Property(e => e.Address1)
                    .HasMaxLength(100)
                    .HasColumnName("address1");

                entity.Property(e => e.Address2)
                    .HasMaxLength(100)
                    .HasColumnName("address2");

                entity.Property(e => e.ArchivedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("archived_at");

                entity.Property(e => e.ArchivedById).HasColumnName("archived_by_id");

                entity.Property(e => e.City)
                    .HasMaxLength(100)
                    .HasColumnName("city");

                entity.Property(e => e.CountryId).HasColumnName("country_id");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_at");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.Email)
                    .HasMaxLength(100)
                    .HasColumnName("email");

                entity.Property(e => e.Fax)
                    .HasMaxLength(20)
                    .HasColumnName("fax");

                entity.Property(e => e.IsArchived)
                    .HasColumnName("is_archived")
                    .HasDefaultValueSql("false");

                entity.Property(e => e.LocationCode)
                    .IsRequired()
                    .HasMaxLength(10)
                    .HasColumnName("location_code");

                entity.Property(e => e.MapLink)
                    .HasMaxLength(255)
                    .HasColumnName("map_link");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("name");

                entity.Property(e => e.PostalCode)
                    .HasMaxLength(20)
                    .HasColumnName("postal_code");

                entity.Property(e => e.ProvinceId).HasColumnName("province_id");

                entity.Property(e => e.ShippingAddress1)
                    .HasMaxLength(100)
                    .HasColumnName("shipping_address1");

                entity.Property(e => e.ShippingAddress2)
                    .HasMaxLength(100)
                    .HasColumnName("shipping_address2");

                entity.Property(e => e.ShippingCity)
                    .HasMaxLength(100)
                    .HasColumnName("shipping_city");

                entity.Property(e => e.ShippingCountryId).HasColumnName("shipping_country_id");

                entity.Property(e => e.ShippingPostalCode)
                    .HasMaxLength(20)
                    .HasColumnName("shipping_postal_code");

                entity.Property(e => e.ShippingProvinceId).HasColumnName("shipping_province_id");

                entity.Property(e => e.ShippingUsingMain).HasColumnName("shipping_using_main");

                entity.Property(e => e.Telephone)
                    .HasMaxLength(20)
                    .HasColumnName("telephone");

                entity.Property(e => e.Tollfree)
                    .HasMaxLength(20)
                    .HasColumnName("tollfree");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updated_at");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.Property(e => e.Website)
                    .HasMaxLength(100)
                    .HasColumnName("website");

                entity.HasOne(d => d.AccessPlanNavigation)
                    .WithMany(p => p.ShowLocations)
                    .HasForeignKey(d => d.AccessPlanId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_access_plan");

                entity.HasOne(d => d.ArchivedBy)
                    .WithMany(p => p.ShowLocationsArchivedBy)
                    .HasForeignKey(d => d.ArchivedById)
                    .HasConstraintName("fk_show_locations_archived_by");

                entity.HasOne(d => d.Country)
                    .WithMany(p => p.ShowLocationsCountry)
                    .HasForeignKey(d => d.CountryId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_location_country");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.ShowLocationsCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .HasConstraintName("fk_show_locations_created_by");

                entity.HasOne(d => d.Province)
                    .WithMany(p => p.ShowLocationsProvince)
                    .HasForeignKey(d => d.ProvinceId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_location_province");

                entity.HasOne(d => d.ShippingCountry)
                    .WithMany(p => p.ShowLocationsShippingCountry)
                    .HasForeignKey(d => d.ShippingCountryId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_shipping_country");

                entity.HasOne(d => d.ShippingProvince)
                    .WithMany(p => p.ShowLocationsShippingProvince)
                    .HasForeignKey(d => d.ShippingProvinceId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_shipping_province");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.ShowLocationsUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .HasConstraintName("fk_show_locations_updated_by");
            });

            modelBuilder.Entity<TaxProvince>(entity =>
            {
                entity.ToTable("tax_province");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.DisplayOrder)
                    .HasColumnName("display_order")
                    .HasDefaultValueSql("1");

                entity.Property(e => e.IsActive)
                    .HasColumnName("is_active")
                    .HasDefaultValueSql("true");

                entity.Property(e => e.ProvinceId).HasColumnName("province_id");

                entity.Property(e => e.TaxRate)
                    .HasPrecision(6, 2)
                    .HasColumnName("tax_rate");

                entity.Property(e => e.TaxTypeId).HasColumnName("tax_type_id");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updated_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.Property(e => e.UpdatedIsActiveById).HasColumnName("updated_is_active_by_id");

                entity.Property(e => e.UpdatedIsActiveDate)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updated_is_active_date")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.TaxProvinceCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_tax_province_created_by");

                entity.HasOne(d => d.Province)
                    .WithMany(p => p.TaxProvince)
                    .HasForeignKey(d => d.ProvinceId)
                    .HasConstraintName("fk_province");

                entity.HasOne(d => d.TaxType)
                    .WithMany(p => p.TaxProvince)
                    .HasForeignKey(d => d.TaxTypeId)
                    .HasConstraintName("fk_tax_type");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.TaxProvinceUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_tax_province_updated_by");

                entity.HasOne(d => d.UpdatedIsActiveBy)
                    .WithMany(p => p.TaxProvinceUpdatedIsActiveBy)
                    .HasForeignKey(d => d.UpdatedIsActiveById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_tax_province_updated_is_active_by");
            });

            modelBuilder.Entity<TaxType>(entity =>
            {
                entity.ToTable("tax_type");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.TaxAbr)
                    .HasMaxLength(6)
                    .HasColumnName("tax_abr");

                entity.Property(e => e.TaxName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("tax_name");
            });

            modelBuilder.Entity<Warehouse>(entity =>
            {
                entity.ToTable("warehouse");

                entity.Property(e => e.WarehouseId).HasColumnName("warehouse_id");

                entity.Property(e => e.AddressLine1)
                    .HasMaxLength(200)
                    .HasColumnName("address_line1");

                entity.Property(e => e.AddressLine2)
                    .HasMaxLength(200)
                    .HasColumnName("address_line2");

                entity.Property(e => e.City)
                    .HasMaxLength(200)
                    .HasColumnName("city");

                entity.Property(e => e.Code)
                    .HasMaxLength(4)
                    .HasColumnName("code");

                entity.Property(e => e.ContactPersonId).HasColumnName("contact_person_id");

                entity.Property(e => e.CountryId).HasColumnName("country_id");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.IsActive)
                    .IsRequired()
                    .HasColumnName("is_active")
                    .HasDefaultValueSql("true");

                entity.Property(e => e.LastUpdateActiveById).HasColumnName("last_update_active_by_id");

                entity.Property(e => e.LastUpdatedActiveAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("last_updated_active_at");

                entity.Property(e => e.Phone)
                    .HasMaxLength(15)
                    .HasColumnName("phone");

                entity.Property(e => e.PostalCode)
                    .HasMaxLength(10)
                    .HasColumnName("postal_code");

                entity.Property(e => e.ProvinceId).HasColumnName("province_id");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updated_at");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.Property(e => e.WarehouseName)
                    .HasMaxLength(200)
                    .HasColumnName("warehouse_name");

                entity.Property(e => e.WarehouseTypeId).HasColumnName("warehouse_type_id");

                entity.HasOne(d => d.ContactPerson)
                    .WithMany(p => p.WarehouseContactPerson)
                    .HasForeignKey(d => d.ContactPersonId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_contact_person");

                entity.HasOne(d => d.Country)
                    .WithMany(p => p.Warehouse)
                    .HasForeignKey(d => d.CountryId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("fk_country");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.WarehouseCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_created_by");

                entity.HasOne(d => d.LastUpdateActiveBy)
                    .WithMany(p => p.WarehouseLastUpdateActiveBy)
                    .HasForeignKey(d => d.LastUpdateActiveById)
                    .HasConstraintName("fk_last_update_user");

                entity.HasOne(d => d.Province)
                    .WithMany(p => p.Warehouse)
                    .HasForeignKey(d => d.ProvinceId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("fk_province");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.WarehouseUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_updated_by");

                entity.HasOne(d => d.WarehouseType)
                    .WithMany(p => p.Warehouse)
                    .HasForeignKey(d => d.WarehouseTypeId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("fk_warehouse_type");
            });

            modelBuilder.Entity<WarehouseTypes>(entity =>
            {
                entity.HasKey(e => e.WarehouseTypeId)
                    .HasName("warehouse_types_pkey");

                entity.ToTable("warehouse_types");

                entity.Property(e => e.WarehouseTypeId).HasColumnName("warehouse_type_id");

                entity.Property(e => e.Code)
                    .HasMaxLength(3)
                    .HasColumnName("code");

                entity.Property(e => e.Description)
                    .HasMaxLength(200)
                    .HasColumnName("description");

                entity.Property(e => e.TypeName).HasColumnName("type_name");
            });

            modelBuilder.HasSequence("Auth_Permission_id_seq");

            modelBuilder.HasSequence("Auth_Role_id_seq");

            modelBuilder.HasSequence("Auth_User_id_seq");

            modelBuilder.HasSequence("doc_group_id_seq");

            modelBuilder.HasSequence("Menu_Item_MenuItemID_seq");

            modelBuilder.HasSequence("Menu_Section_Id_seq");

            modelBuilder.HasSequence("sections_id_seq");

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}