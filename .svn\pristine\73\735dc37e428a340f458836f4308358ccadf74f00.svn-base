﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class CompanyGroup
    {
        public CompanyGroup()
        {
            Company = new HashSet<Company>();
        }

        public int CompanyGroupId { get; set; }
        public string Name { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? UpdatedById { get; set; }

        public virtual AuthUser CreatedBy { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
        public virtual ICollection<Company> Company { get; set; }
    }
}