﻿using goodkey_cms.DTO.Offering;
using goodkey_cms.Services;
using goodkey_common.DTO;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class OfferingRateController : Controller
	{
		private readonly AuthService _authService;
		private readonly IGroupTypeRepository _repo;
		private readonly IOfferingRateRepository _repoRate;
		private readonly IOfferingRepository _repoOffering;


		public OfferingRateController(AuthService authService, IGroupTypeRepository repo, IOfferingRateRepository repoRate, IOfferingRepository repoOffering)
		{
			_repo = repo;
			_repoRate = repoRate;
			_authService = authService;
			_repoOffering = repoOffering;
		}


		[HttpGet("warehouse/{warehouseId}")]
		public async Task<GenericRespond<GroupTypeWithGroupDto>> GetAllSortedProduct(int warehouseId)
		{
			var groupType = await _repo.GetAll(1);

			if (groupType == null)
			{
				return new GenericRespond<GroupTypeWithGroupDto>
				{
					Data = null,
					StatusCode = 404,
					Message = "Group type not found."
				};
			}

			// Load offering_rate records separately (you can filter by warehouseId if needed)
			var offeringRates = await _repoRate.GetByWarehouseIdAsync(warehouseId);

			var groupWithCategoriesDtos = groupType.Group?.Select(group => new GroupWithCategoriesDto
			{
				GroupId = group.Id,
				GroupName = group.Name,
				IsAvailable = group.IsAvailable,
				Code = group.Code,
				Categories = group.Category?.Select(category => new CategoryWithOfferingsDto
				{
					CategoryId = category.Id,
					Code = category.Code,
					CategoryName = category.Name,
					IsAvailable = category.IsAvailable,

					Offerings = category.Offering?.Select(offering =>
					{
						// Offering-level rate (offeringPropertyId == null)
						var offeringRate = offeringRates.FirstOrDefault(rate =>
							rate.OfferingId == offering.Id &&
							rate.OfferingPropertyId == null
						);

						var propertyOptions = offering.OfferingProperty.Select(p =>
						{
							// Property-level rate
							var propRate = offeringRates.FirstOrDefault(rate =>
								rate.OfferingId == offering.Id &&
								rate.OfferingPropertyId == p.Id
							);

							return new PropertyOptionsDto
							{
								Id = p.Id,
								Name = p.PropertyOption1?.Name + (p.PropertyOption2 != null ? $", {p.PropertyOption2.Name}" : ""),
								Code = p.Code,
								IsActive = p.IsActive,
								UnitPrice = propRate?.UnitPrice,
								Quantity = propRate?.Quantity,
								IsDiscontinued = propRate?.IsDiscontinued
							};
						}).ToList();

						// Compute offering.Quantity as sum of all its properties' quantities
						var totalPropertyQuantity = offeringRates
							.Where(r => r.OfferingId == offering.Id && r.OfferingPropertyId != null)
							.Sum(r => r.Quantity ?? 0);

						return new OfferingDto
						{
							Id = offering.Id,
							Name = offering.Name,
							Code = offering.Code,
							IsActive = offering.IsActive,
							IsObsolete = offering.IsObsolete,
							UnitPrice = offeringRate?.UnitPrice,
							Quantity = offeringRate?.Quantity ?? totalPropertyQuantity,
							IsDiscontinued = offeringRate?.IsDiscontinued,
							Options = propertyOptions
						};
					}).ToList()
				}).ToList()
			}).ToList();

			var result = new GroupTypeWithGroupDto
			{
				GroupTypeId = groupType.Id,
				GroupTypeName = groupType.Name,
				Group = groupWithCategoriesDtos
			};

			return new GenericRespond<GroupTypeWithGroupDto>
			{
				Data = result,
				StatusCode = 200
			};
		}

		[HttpPost("SaveProperty")]
		public async Task<GenericRespond<bool>> SaveOfferingRate([FromBody] OfferingRateUpsertDto dto)
		{
			var user = _authService.Current;
			if (user == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 401,
					Message = "Unauthorized"
				};
			}

			if (dto == null)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 400,
					Message = "Invalid request body"
				};
			}

			try
			{
				if (dto.OfferingPropertyId == null)
				{
					// Get all properties of this offering
					var offeringProperty = await _repoOffering.GetOfferingPropertiesAsync(dto.OfferingId);
					if (offeringProperty == null)
					{
						return new GenericRespond<bool>
						{
							Data = false,
							StatusCode = 404,
							Message = "No offering properties found for this offering."
						};
					}

					foreach (var prop in offeringProperty)
					{
						var rate = new OfferingRate
						{
							OfferingId = dto.OfferingId,
							OfferingPropertyId = prop.Id,
							WarehouseId = dto.WarehouseId,
							Quantity = dto.Quantity,
							UnitPrice = dto.UnitPrice,
							IsDiscontinued = dto.IsDiscontinued,
							UpdatedById = user.UserId,
							CreatedById = user.UserId
						};

						await _repoRate.UpsertAsync(rate);
					}
				}
				else
				{
					// Update just the specific property
					var rate = new OfferingRate
					{
						OfferingId = dto.OfferingId,
						OfferingPropertyId = dto.OfferingPropertyId,
						WarehouseId = dto.WarehouseId,
						Quantity = dto.Quantity,
						UnitPrice = dto.UnitPrice,
						IsDiscontinued = dto.IsDiscontinued,
						UpdatedById = user.UserId,
						CreatedById = user.UserId
					};

					await _repoRate.UpsertAsync(rate);
				}

				return new GenericRespond<bool>
				{
					Data = true,
					StatusCode = 200,
					Message = "Offering rate saved successfully"
				};
			}
			catch (Exception ex)
			{
				return new GenericRespond<bool>
				{
					Data = false,
					StatusCode = 500,
					Message = $"Failed to save offering rate: {ex.Message}"
				};
			}
		}

	}
}
