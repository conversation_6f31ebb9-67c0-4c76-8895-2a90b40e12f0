﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class ShowLocations
    {
        public ShowLocations()
        {
            Contact = new HashSet<Contact>();
            ShowDocs = new HashSet<ShowDocs>();
            ShowLocationHalls = new HashSet<ShowLocationHalls>();
            Shows = new HashSet<Shows>();
        }

        public int LocationId { get; set; }
        public string LocationCode { get; set; }
        public string Name { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string PostalCode { get; set; }
        public string City { get; set; }
        public int? ProvinceId { get; set; }
        public int? CountryId { get; set; }
        public string Telephone { get; set; }
        public string Tollfree { get; set; }
        public string Fax { get; set; }
        public string MapLink { get; set; }
        public string Website { get; set; }
        public string Email { get; set; }
        public string AccessPlan { get; set; }
        public string ShippingAddress1 { get; set; }
        public string ShippingAddress2 { get; set; }
        public string ShippingPostalCode { get; set; }
        public string ShippingCity { get; set; }
        public int? ShippingCountryId { get; set; }
        public int? ShippingProvinceId { get; set; }
        public int? CreatedById { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool? IsArchived { get; set; }
        public DateTime? ArchivedAt { get; set; }
        public int? ArchivedById { get; set; }
        public bool? ShippingUsingMain { get; set; }
        public int? AccessPlanId { get; set; }

        public virtual ShowDocs AccessPlanNavigation { get; set; }
        public virtual AuthUser ArchivedBy { get; set; }
        public virtual Countries Country { get; set; }
        public virtual AuthUser CreatedBy { get; set; }
        public virtual Provinces Province { get; set; }
        public virtual Countries ShippingCountry { get; set; }
        public virtual Provinces ShippingProvince { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
        public virtual ICollection<Contact> Contact { get; set; }
        public virtual ICollection<ShowDocs> ShowDocs { get; set; }
        public virtual ICollection<ShowLocationHalls> ShowLocationHalls { get; set; }
        public virtual ICollection<Shows> Shows { get; set; }
    }
}