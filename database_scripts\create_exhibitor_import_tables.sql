-- Exhibitor Excel Import Tables
-- This script creates the tables needed for the two-phase Excel import feature
-- Phase 1: Validation and temporary storage
-- Phase 2: Execution and final import

-- =====================================================
-- Table 1: ExhibitorImportSessions
-- Stores import session information and metadata
-- =====================================================
CREATE TABLE "ExhibitorImportSessions" (
    "Id" SERIAL PRIMARY KEY,
    "SessionId" UUID NOT NULL UNIQUE DEFAULT gen_random_uuid(),
    "ShowId" INTEGER NOT NULL,
    "FileName" VARCHAR(255) NOT NULL,
    "OriginalFileName" VARCHAR(255) NOT NULL,
    "FilePath" VARCHAR(500) NOT NULL,
    "FileSize" BIGINT,
    "MimeType" VARCHAR(100),
    "TotalRows" INTEGER NOT NULL DEFAULT 0,
    "ValidRows" INTEGER NOT NULL DEFAULT 0,
    "ErrorRows" INTEGER NOT NULL DEFAULT 0,
    "WarningRows" INTEGER NOT NULL DEFAULT 0,
    "Status" VARCHAR(50) NOT NULL DEFAULT 'Validating', -- Validating, Validated, Executing, Completed, Failed, Expired
    "CanProceed" BOOLEAN NOT NULL DEFAULT false,
    "ValidationReport" JSONB, -- Stores detailed validation results
    "ProcessingStartedAt" TIMESTAMP,
    "ProcessingCompletedAt" TIMESTAMP,
    "ExpiresAt" TIMESTAMP NOT NULL DEFAULT (CURRENT_TIMESTAMP + INTERVAL '24 hours'),
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "CreatedById" INTEGER NOT NULL,
    "ExecutedAt" TIMESTAMP,
    "ExecutedById" INTEGER,
    
    -- Foreign Key Constraints
    CONSTRAINT "FK_ExhibitorImportSessions_Show" FOREIGN KEY ("ShowId") REFERENCES "Shows"("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_ExhibitorImportSessions_CreatedBy" FOREIGN KEY ("CreatedById") REFERENCES "AuthUser"("UserId"),
    CONSTRAINT "FK_ExhibitorImportSessions_ExecutedBy" FOREIGN KEY ("ExecutedById") REFERENCES "AuthUser"("UserId")
);

-- =====================================================
-- Table 2: ExhibitorImportRows
-- Stores individual row data from Excel file for validation
-- =====================================================
CREATE TABLE "ExhibitorImportRows" (
    "Id" SERIAL PRIMARY KEY,
    "SessionId" UUID NOT NULL,
    "RowNumber" INTEGER NOT NULL,
    "Status" VARCHAR(50) NOT NULL DEFAULT 'Pending', -- Pending, Valid, Error, Warning, Processed, Skipped
    
    -- Excel Data Fields (as strings for validation)
    "CompanyName" VARCHAR(255),
    "ContactFirstName" VARCHAR(100),
    "ContactLastName" VARCHAR(100),
    "ContactEmail" VARCHAR(255),
    "ContactPhone" VARCHAR(50),
    "ContactMobile" VARCHAR(50),
    "ContactExt" VARCHAR(20),
    "BoothNumbers" TEXT, -- Comma-separated booth numbers
    "ContactType" VARCHAR(100), -- Text value from Excel (will be mapped to ContactTypeId)
    
    -- Resolved/Mapped Values (after validation)
    "ResolvedCompanyId" INTEGER,
    "ResolvedContactTypeId" INTEGER,
    "ResolvedBoothNumbersArray" TEXT[], -- Parsed booth numbers array
    "IsNewCompany" BOOLEAN DEFAULT false,
    "IsNewContact" BOOLEAN DEFAULT false,
    "IsDuplicate" BOOLEAN DEFAULT false,
    
    -- Validation Results
    "HasErrors" BOOLEAN NOT NULL DEFAULT false,
    "HasWarnings" BOOLEAN NOT NULL DEFAULT false,
    "ErrorMessages" JSONB, -- Array of error messages
    "WarningMessages" JSONB, -- Array of warning messages
    
    -- Processing Results
    "CreatedCompanyId" INTEGER,
    "CreatedContactId" INTEGER,
    "CreatedExhibitorId" INTEGER,
    "CreatedUserId" INTEGER,
    "ProcessedAt" TIMESTAMP,
    
    CONSTRAINT "FK_ExhibitorImportRows_Session" FOREIGN KEY ("SessionId") REFERENCES "ExhibitorImportSessions"("SessionId") ON DELETE CASCADE,
    CONSTRAINT "FK_ExhibitorImportRows_ResolvedCompany" FOREIGN KEY ("ResolvedCompanyId") REFERENCES "Company"("CompanyId"),
    CONSTRAINT "FK_ExhibitorImportRows_ResolvedContactType" FOREIGN KEY ("ResolvedContactTypeId") REFERENCES "ContactType"("Id"),
    CONSTRAINT "FK_ExhibitorImportRows_CreatedCompany" FOREIGN KEY ("CreatedCompanyId") REFERENCES "Company"("CompanyId"),
    CONSTRAINT "FK_ExhibitorImportRows_CreatedContact" FOREIGN KEY ("CreatedContactId") REFERENCES "Contact"("ContactId"),
    CONSTRAINT "FK_ExhibitorImportRows_CreatedExhibitor" FOREIGN KEY ("CreatedExhibitorId") REFERENCES "ShowExhibitors"("Id"),
    CONSTRAINT "FK_ExhibitorImportRows_CreatedUser" FOREIGN KEY ("CreatedUserId") REFERENCES "AuthUser"("UserId")
);

-- =====================================================
-- Table 3: ExhibitorImportErrors
-- Detailed error tracking for better reporting
-- =====================================================
CREATE TABLE "ExhibitorImportErrors" (
    "Id" SERIAL PRIMARY KEY,
    "SessionId" UUID NOT NULL,
    "RowId" INTEGER,
    "RowNumber" INTEGER NOT NULL,
    "FieldName" VARCHAR(100),
    "FieldValue" TEXT,
    "ErrorType" VARCHAR(50) NOT NULL, -- Required, Format, Duplicate, Lookup, System, Business
    "ErrorCode" VARCHAR(50),
    "ErrorMessage" TEXT NOT NULL,
    "Severity" VARCHAR(20) NOT NULL DEFAULT 'Error', -- Error, Warning, Info
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT "FK_ExhibitorImportErrors_Session" FOREIGN KEY ("SessionId") REFERENCES "ExhibitorImportSessions"("SessionId") ON DELETE CASCADE,
    CONSTRAINT "FK_ExhibitorImportErrors_Row" FOREIGN KEY ("RowId") REFERENCES "ExhibitorImportRows"("Id") ON DELETE CASCADE
);

-- =====================================================
-- Indexes for Performance
-- =====================================================

-- ExhibitorImportSessions indexes
CREATE INDEX "IX_ExhibitorImportSessions_SessionId" ON "ExhibitorImportSessions"("SessionId");
CREATE INDEX "IX_ExhibitorImportSessions_ShowId" ON "ExhibitorImportSessions"("ShowId");
CREATE INDEX "IX_ExhibitorImportSessions_Status" ON "ExhibitorImportSessions"("Status");
CREATE INDEX "IX_ExhibitorImportSessions_CreatedById" ON "ExhibitorImportSessions"("CreatedById");
CREATE INDEX "IX_ExhibitorImportSessions_ExpiresAt" ON "ExhibitorImportSessions"("ExpiresAt");

-- ExhibitorImportRows indexes
CREATE INDEX "IX_ExhibitorImportRows_SessionId" ON "ExhibitorImportRows"("SessionId");
CREATE INDEX "IX_ExhibitorImportRows_Status" ON "ExhibitorImportRows"("Status");
CREATE INDEX "IX_ExhibitorImportRows_RowNumber" ON "ExhibitorImportRows"("RowNumber");
CREATE INDEX "IX_ExhibitorImportRows_HasErrors" ON "ExhibitorImportRows"("HasErrors");
CREATE INDEX "IX_ExhibitorImportRows_CompanyName" ON "ExhibitorImportRows"("CompanyName");
CREATE INDEX "IX_ExhibitorImportRows_ContactEmail" ON "ExhibitorImportRows"("ContactEmail");

-- ExhibitorImportErrors indexes
CREATE INDEX "IX_ExhibitorImportErrors_SessionId" ON "ExhibitorImportErrors"("SessionId");
CREATE INDEX "IX_ExhibitorImportErrors_RowId" ON "ExhibitorImportErrors"("RowId");
CREATE INDEX "IX_ExhibitorImportErrors_ErrorType" ON "ExhibitorImportErrors"("ErrorType");
CREATE INDEX "IX_ExhibitorImportErrors_Severity" ON "ExhibitorImportErrors"("Severity");

-- =====================================================
-- Comments for Documentation
-- =====================================================
COMMENT ON TABLE "ExhibitorImportSessions" IS 'Stores Excel import session metadata and validation results for two-phase import process';
COMMENT ON TABLE "ExhibitorImportRows" IS 'Stores individual Excel rows with validation results and processing status';
COMMENT ON TABLE "ExhibitorImportErrors" IS 'Detailed error tracking for import validation and processing';

COMMENT ON COLUMN "ExhibitorImportSessions"."SessionId" IS 'Unique session identifier for tracking import progress';
COMMENT ON COLUMN "ExhibitorImportSessions"."ValidationReport" IS 'JSON summary of validation results including counts and duplicate analysis';
COMMENT ON COLUMN "ExhibitorImportRows"."BoothNumbers" IS 'Raw comma-separated booth numbers from Excel';
COMMENT ON COLUMN "ExhibitorImportRows"."ResolvedBoothNumbersArray" IS 'Parsed and validated booth numbers array';
COMMENT ON COLUMN "ExhibitorImportRows"."ErrorMessages" IS 'JSON array of validation error messages for this row';
COMMENT ON COLUMN "ExhibitorImportRows"."WarningMessages" IS 'JSON array of validation warning messages for this row';
