using System;

namespace goodkey_common.DTO.Show
{
    public class ShowScheduleDto
    {
        public int Id { get; set; }
        public DateOnly? ShowScheduleDate { get; set; }
        public TimeOnly? TimeStart { get; set; }
        public TimeOnly? TimeEnd { get; set; }
        public int? ShowId { get; set; }
        public bool? ShowScheduleConfirmed { get; set; }
        public string ShowScheduleComments { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? CreatedBy { get; set; }
        public bool? ApplyScheduleToServiceForm { get; set; }
        
        // Navigation properties
        public string CreatedByUsername { get; set; }
        public string ShowName { get; set; }
        public string ShowCode { get; set; }
    }

    public class CreateShowScheduleDto
    {
        public DateOnly? ShowScheduleDate { get; set; }
        public TimeOnly? TimeStart { get; set; }
        public TimeOnly? TimeEnd { get; set; }
        public bool? ShowScheduleConfirmed { get; set; }
        public string ShowScheduleComments { get; set; }
        public bool? ApplyScheduleToServiceForm { get; set; }
    }

    public class UpdateShowScheduleDto
    {
        public DateOnly? ShowScheduleDate { get; set; }
        public TimeOnly? TimeStart { get; set; }
        public TimeOnly? TimeEnd { get; set; }
        public bool? ShowScheduleConfirmed { get; set; }
        public string ShowScheduleComments { get; set; }
        public bool? ApplyScheduleToServiceForm { get; set; }
    }
} 