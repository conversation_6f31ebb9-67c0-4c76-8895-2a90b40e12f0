using goodkey_common.Context;
using goodkey_common.Models;

namespace goodkey_common.Repositories
{
    public interface IGroundConditionRepository
    {
        IEnumerable<GroundConditions> GetAll();
        GroundConditions GetById(int id);
        GroundConditions Add(GroundConditions entity);
        GroundConditions Update(GroundConditions entity);
        bool Delete(int id);
    }

    public class GroundConditionRepository : IGroundConditionRepository
    {
        private readonly GoodkeyContext _context;
        public GroundConditionRepository(GoodkeyContext context)
        {
            _context = context;
        }
        public IEnumerable<GroundConditions> GetAll()
        {
            return _context.GroundConditions.ToList();
        }
        public GroundConditions GetById(int id)
        {
            return _context.GroundConditions.FirstOrDefault(x => x.Id == id);
        }
        public GroundConditions Add(GroundConditions entity)
        {
            _context.GroundConditions.Add(entity);
            _context.SaveChanges();
            return entity;
        }
        public GroundConditions Update(GroundConditions entity)
        {
            var existing = _context.GroundConditions.Find(entity.Id);
            if (existing == null) return null;
            _context.Entry(existing).CurrentValues.SetValues(entity);
            _context.SaveChanges();
            return entity;
        }
        public bool Delete(int id)
        {
            var entity = _context.GroundConditions.Find(id);
            if (entity == null) return false;
            _context.GroundConditions.Remove(entity);
            _context.SaveChanges();
            return true;
        }
    }
} 