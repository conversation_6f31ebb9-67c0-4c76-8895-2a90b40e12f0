﻿using goodkey_cms.DTO.Auth;
using goodkey_cms.DTO.User;
using goodkey_cms.Infrastructure.Extensions;
using goodkey_cms.Repositories;
using goodkey_cms.Services;
using goodkey_common.Context;
using goodkey_common.DTO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly AuthService _service;
        private readonly IUserRepository _userRepo;


        private readonly GoodkeyContext _context;
        public AuthController(AuthService service, IUserRepository userRepo, GoodkeyContext context)
        {
            _service = service;
            _userRepo = userRepo;
            _context = context;
        }

        [HttpGet("[action]")]
        public GenericRespond<UserDetail> Me()
        {
            var user = _service.Current;

            if (user != null)
            {
                _context.Entry(user).Reference(x => x.Role).Load();


                return new()
                {
                    Data = new()
                    {
                        Email = user.VerificationEmail,
                        Username = user.Username,
                        Name = user.FirstName + " " + user.LastName,
                        Role = user.Role == null ? null : new() { Id = user.Role.RoleId, Name = user.Role.Name },
                        IsSuper = (user.VerificationEmail.ToLower().Equals("<EMAIL>") || user.VerificationEmail.ToLower().Equals("<EMAIL>")),
                        MenuItems = user.Role?.MenuItem.Select(c => c.MenuItemId) ?? Enumerable.Empty<int>(),
                        Permission = user.Role?.Permission.Select(x => x.Code),
                    }
                };
            }
            return new() { StatusCode = 401 };
        }

        [HttpGet("[action]/{key}")]
        [AllowAnonymous]
        public GenericRespond<UserDetail> Verify(string key)
        {
            var user = _service.GetUserByToken(key);
            if (user != null && user.VerificationSentDate != null && user.VerificationSentDate > DateTime.UtcNow.AddDays(-7))
            {
                return new()
                {
                    Data = new()
                    {
                        Email = user.Username,
                    }
                };
            }
            return new() { StatusCode = 404 };
        }

        [HttpPost("[action]")]
        [AllowAnonymous]
        public GenericRespond<bool> RequestPasswordReset([FromBody] RequestPasswordReset data)
        {
            return new() { Data = _service.RequestReset(data.Email) };
        }


        [AllowAnonymous]
        [HttpPost("[action]/{key}")]
        public GenericRespond<AuthRespond> Verify(string key, [FromBody] VerificationCredintals data)
        {
            var tokens = _service.Verify(key, data.Password);
            if (tokens != null)
                return new() { Data = new() { AccessToken = tokens.AccessToken, RefreshToken = tokens.RefreshToken } };
            else
            {
                return new() { StatusCode = 401 };
            }
        }

        [AllowAnonymous]
        [HttpPost("[action]")]
        public GenericRespond<AuthRespond> Login(LoginCredintals data)
        {
            var tokens = _service.Login(data);
            if (tokens != null)
                return new() { Data = new() { AccessToken = tokens.AccessToken, RefreshToken = tokens.RefreshToken } };
            else
            {
                return new() { StatusCode = 401 };
            }
        }

        [HttpPatch("[action]")]
        public GenericRespond<bool?> ChangePassword([FromBody] ChangePassword data)
        {

            return new()
            {
                Data = _service.ChangePassword(Request.HttpContext.GetUsername(), data.OldPassword, data.Password)
            };
        }


    }
}
