using goodkey_cms.DTO.User;
using goodkey_cms.Infrastructure.Extensions;
using goodkey_cms.Infrastructure.Utils;
using goodkey_cms.Repositories;
using goodkey_common.DTO;
using goodkey_common.DTO.Company;
using goodkey_common.DTO.Contact;
using goodkey_common.Repositories;
using goodkey_common.Context;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class CompanyController : Controller
	{
		private readonly ICompanyRepository _repo;
		private readonly IUserRepository _repoUser;
		private readonly IShowRepository _showRepo;
		private readonly ILogger<CompanyController> _logger;
		private readonly GoodkeyContext _context;

		public CompanyController(ICompanyRepository repo, IUserRepository repoUser, IShowRepository showRepo, ILogger<CompanyController> logger, GoodkeyContext context)
		{
			_repo = repo;
			_repoUser = repoUser;
			_showRepo = showRepo;
			_logger = logger;
			_context = context;
		}

		/// <summary>
		/// Generates a unique username with sequence numbers to handle duplicates
		/// </summary>
		private string GenerateUniqueUsername(string baseUsername)
		{
			// Start with the base username (email or generated)
			string candidateUsername = baseUsername;

			// Check if it already exists
			if (!_context.AuthUser.Any(u => u.Username == candidateUsername))
			{
				return candidateUsername;
			}

			// If it exists, try with sequence numbers
			for (int sequence = 1; sequence <= 999; sequence++)
			{
				candidateUsername = $"{baseUsername}_{sequence}";

				if (!_context.AuthUser.Any(u => u.Username == candidateUsername))
				{
					return candidateUsername;
				}
			}

			// If all sequences are taken, use GUID as fallback
			return $"{baseUsername}_{Guid.NewGuid().ToString().Substring(0, 8)}";
		}

		[HttpGet]
		public GenericRespond<IEnumerable<Company>> GetAll([FromQuery] string? companyGroupName = null)
		{
			var companies = string.IsNullOrWhiteSpace(companyGroupName)
				? _repo.GetAll()
				: _repo.GetByCompanyGroup(companyGroupName);

			return new GenericRespond<IEnumerable<Company>>
			{
				Data = companies.OrderByDescending(x => x.CompanyId).Select(item =>
				{
					var company = new Company
					{
						Id = item.CompanyId,
						Name = item.CompanyName,
						Phone = item.Phone,
						Email = item.Email,
						Address1 = item.Address1,
						Address2 = item.Address2,
						City = item.City,
						Province = item.Province?.ProvinceName,
						PostalCode = item.PostalCode,
						Country = item.Country?.CountryName,
						WebsiteUrl = item.WebsiteUrl,
						AccountNumber = item.AccountNumber,
						CompanyGroup = item.CompanyGroup?.Name,
						Note = item.Note,
						IsArchived = item.IsArchived
					};

					// Add number of shows for "Show manager" companies
					if (item.CompanyGroup?.Name?.ToLower() == "show manager")
					{
						var showPromoters = _showRepo.GetShowPromotersByCompany(item.CompanyId);
						company.NumberOfShows = showPromoters.Count();
					}

					return company;
				})
			};
		}

		[HttpGet("[action]/{id:int}")]
		public GenericRespond<CompanyDto> Get(int id)
		{
			var item = _repo.Get(id);
			if (item == null)
			{
				return new GenericRespond<CompanyDto>
				{
					Data = null,
					Message = "Company not found",
					StatusCode = 404
				};
			}

			return new GenericRespond<CompanyDto>
			{
				Data = new CompanyDto
				{
					Id = item.CompanyId,
					Name = item.CompanyName,
					Phone = item.Phone,
					Email = item.Email,
					Address1 = item.Address1,
					Address2 = item.Address2,
					City = item.City,
					ProvinceId = item.ProvinceId,
					PostalCode = item.PostalCode,
					CountryId = item.CountryId,
					WebsiteUrl = item.WebsiteUrl,
					AccountNumber = item.AccountNumber,
					CompanyGroup = item.CompanyGroup?.Name,
					Note = item.Note,
					IsArchived = item.IsArchived
				}
			};
		}

		[HttpPatch("[action]/{id:int}")]
		public GenericRespond<bool> Update(int id, CompanyCreateUpdateDto data)
		{
			var username = Request.HttpContext.GetUsername();
			if (string.IsNullOrEmpty(username))
			{
				return new GenericRespond<bool>
				{
					Data = false,
					Message = "Unauthorized",
					StatusCode = 401
				};
			}

			// Validate required fields if needed
			var success = _repo.Update(
				id,
				data.Name ?? string.Empty,
				data.Phone ?? string.Empty,
				data.Email ?? string.Empty,
				data.Address1 ?? string.Empty,
				data.Address2 ?? string.Empty,
				data.PostalCode ?? string.Empty,
				data.City ?? string.Empty,
				data.ProvinceId ?? 0,
				data.CountryId ?? 0,
				data.WebsiteUrl ?? string.Empty,
				data.AccountNumber ?? string.Empty,
				data.CompanyGroup ?? string.Empty,
				data.Note ?? string.Empty,
				data.IsArchived ?? false,
				username
			);

			return new GenericRespond<bool>
			{
				Data = success,
				Message = success
					? "Company updated successfully."
					: "Failed to update company. It may not exist or there was a database error.",
				StatusCode = success ? 200 : 400
			};
		}

		[HttpPost("[action]")]
		public GenericRespond<bool> Add(CompanyCreateUpdateDto data)
		{
			var username = Request.HttpContext.GetUsername();
			if (string.IsNullOrEmpty(username))
			{
				return new GenericRespond<bool>
				{
					Data = false,
					Message = "Unauthorized",
					StatusCode = 401
				};
			}

			var success = _repo.Add(
				data.Name ?? string.Empty,
				data.Phone ?? string.Empty,
				data.Email ?? string.Empty,
				data.Address1 ?? string.Empty,
				data.Address2 ?? string.Empty,
				data.PostalCode ?? string.Empty,
				data.City ?? string.Empty,
				data.ProvinceId ?? 0,
				data.CountryId ?? 0,
				data.WebsiteUrl ?? string.Empty,
				data.AccountNumber ?? string.Empty,
				data.CompanyGroup ?? string.Empty,
				data.Note ?? string.Empty,
				data.IsArchived ?? false,
				username
			);

			return new GenericRespond<bool>
			{
				Data = success,
				Message = success
					? "Company added successfully."
					: "Failed to add company. It may already exist or there was a database error.",
				StatusCode = success ? 200 : 400
			};
		}

		// Contact endpoints for companies
		[HttpGet("{companyId}/contacts")]
		public GenericRespond<IEnumerable<Contact>> GetCompanyContacts(int companyId)
		{
			var contacts = _repo.GetContactsByCompanyId(companyId);

			return new GenericRespond<IEnumerable<Contact>>
			{
				Data = contacts.Select(item => new Contact
				{
					Id = item.ContactId,
					Name = $"{item.FirstName} {item.LastName}".Trim(),
					ContactType = item.ContactType?.Name,
					Company = item.Company?.CompanyName,
					FirstName = item.FirstName,
					LastName = item.LastName,
					Email = item.Email,
					Telephone = item.Telephone,
					Ext = item.Ext,
					Cellphone = item.Cellphone,
					IsArchived = item.IsArchived,
					FullName = $"{item.FirstName} {item.LastName}".Trim()
				})
			};
		}

		[HttpGet("{companyId}/contacts/{contactId:int}")]
		public GenericRespond<CompanyContactDto> GetContact(int companyId, int contactId)
		{
			var item = _repo.GetContact(contactId);
			if (item == null)
			{
				return new GenericRespond<CompanyContactDto>
				{
					Data = null,
					Message = "Contact not found",
					StatusCode = 404
				};
			}

			return new GenericRespond<CompanyContactDto>
			{
				Data = new CompanyContactDto
				{
					Id = item.ContactId,
					Name = $"{item.FirstName} {item.LastName}".Trim(),
					ContactTypeId = item.ContactTypeId,
					LocationId = item.LocationId,
					CompanyId = item.CompanyId,
					FirstName = item.FirstName,
					LastName = item.LastName,
					Email = item.Email,
					Telephone = item.Telephone,
					Ext = item.Ext,
					Cellphone = item.Cellphone,
					IsArchived = item.IsArchived,
					Username = item.Authuser?.Username,
					Password = "blue" // Always return the default password
				}
			};
		}

		[HttpPost("{companyId}/contacts/add")]
		public GenericRespond<bool> AddContact(int companyId, [FromBody] CreateCompanyContactDto data)
		{
			try
			{

				if (data == null)
				{
					return new GenericRespond<bool>
					{
						Data = false,
						Message = "DEBUG: Request data is null",
						StatusCode = 400
					};
				}

				string username = HttpContext.GetUsername();


				if (string.IsNullOrEmpty(username))
				{
					return new GenericRespond<bool>
					{
						Data = false,
						Message = "DEBUG: Unauthorized - username is null or empty",
						StatusCode = 401
					};
				}



				var contactId = _repo.AddContact(
					data.ContactTypeId,
					companyId,
					data.FirstName ?? string.Empty,
					data.LastName ?? string.Empty,
					data.Email ?? string.Empty,
					data.Telephone ?? string.Empty,
					data.Ext ?? string.Empty,
					data.Cellphone ?? string.Empty,
					data.IsArchived ?? false,
					username
				);



				if (contactId == null)
				{
					return new GenericRespond<bool>
					{
						Data = false,
						Message = "Failed to add contact. Repository returned null contactId.",
						StatusCode = 400
					};
				}

				// Generate unique username following standard pattern
				var baseUsername = !string.IsNullOrEmpty(data.Email) ? data.Email : $"user_{Guid.NewGuid().ToString().Substring(0, 8)}";
				var uniqueUsername = GenerateUniqueUsername(baseUsername);

				var userDto = new CreateUserDto
				{
					FirstName = data.FirstName ?? "Contact",
					LastName = data.LastName ?? "User",
					Email = data.Email ?? string.Empty,
					WorkEmail = data.Email ?? string.Empty,
					VerificationEmail = uniqueUsername, // Use unique username as verification email
					WorkPhoneNumber = data.Telephone ?? string.Empty,
					MobileNumber = data.Cellphone ?? string.Empty,
					StatusId = 1, // Active status
					RoleId = 10, // Default role for contacts
					SalutationId = 1, // Default salutation
					DepartmentId = 1 // Default department
				};

				var userId = _repoUser.CreateUser(username, userDto); // Use the creator's username, not the new username

				if (userId == null)
				{
					// ROLLBACK: If user creation failed, delete the contact to avoid orphaned records
					_repo.DeleteContact(contactId.Value, username);

					return new GenericRespond<bool>
					{
						Data = false,
						Message = "Failed to create user account for contact. Contact creation rolled back.",
						StatusCode = 400
					};
				}

				// Set default password "blue"

				var password = "blue";
				var hashedPassword = HashUtility.HashPassword(password);
				_repoUser.SetPassword(userId.Value, hashedPassword);
				Console.WriteLine($"DEBUG: Password set successfully");


				var contact = _repo.GetContact(contactId.Value);
				if (contact != null)
				{

					contact.Authuserid = userId.Value;
					_repo.UpdateContact(
						contactId.Value, contact.ContactTypeId,
						contact.CompanyId, contact.FirstName, contact.LastName, contact.Email, contact.Telephone,
						contact.Ext, contact.Cellphone, contact.IsArchived ?? false, username
					);

				}



				return new GenericRespond<bool>
				{
					Data = true,
					Message = "Contact and user account created successfully.",
					StatusCode = 200
				};
			}
			catch (Exception ex)
			{

				return new GenericRespond<bool>
				{
					Data = false,
					Message = $"DEBUG: Exception occurred: {ex.Message}",
					StatusCode = 500
				};
			}
		}

		[HttpPatch("{companyId}/contacts/update/{contactId:int}")]
		public GenericRespond<bool> UpdateContact(int companyId, int contactId, [FromBody] UpdateCompanyContactDto data)
		{
			string username = HttpContext.GetUsername();

			var success = _repo.UpdateContact(
				contactId,
				data.ContactTypeId,
				companyId,
				data.FirstName ?? string.Empty,
				data.LastName ?? string.Empty,
				data.Email ?? string.Empty,
				data.Telephone ?? string.Empty,
				data.Ext ?? string.Empty,
				data.Cellphone ?? string.Empty,
				data.IsArchived ?? false,
				username
			);

			return new GenericRespond<bool>
			{
				Data = success,
				Message = success ? "Contact updated successfully." : "Failed to update contact. It may not exist or there was a database error.",
				StatusCode = success ? 200 : 400
			};
		}

		[HttpDelete("{companyId}/contacts/delete/{contactId:int}")]
		public GenericRespond<bool> DeleteContact(int companyId, int contactId)
		{
			string username = HttpContext.GetUsername();

			var success = _repo.DeleteContact(contactId, username);

			return new GenericRespond<bool>
			{
				Data = success,
				Message = success ? "Contact archived successfully." : "Failed to archive contact. It may not exist or there was a database error.",
				StatusCode = success ? 200 : 400
			};
		}

		// Get shows for a specific company (only for Show manager companies)
		[HttpGet("{companyId}/shows")]
		public GenericRespond<IEnumerable<CompanyShowDto>> GetCompanyShows(int companyId)
		{
			// Verify company exists and is a Show manager
			var company = _repo.Get(companyId);
			if (company == null)
			{
				return new GenericRespond<IEnumerable<CompanyShowDto>>
				{
					Data = null,
					StatusCode = 404,
					Message = "Company not found"
				};
			}

			if (company.CompanyGroup?.Name?.ToLower() != "show manager")
			{
				return new GenericRespond<IEnumerable<CompanyShowDto>>
				{
					Data = new List<CompanyShowDto>(),
					StatusCode = 200,
					Message = "Only Show manager companies have shows"
				};
			}

			// Get shows for the company
			var showPromoters = _showRepo.GetShowPromotersByCompany(companyId);
			var shows = showPromoters.Select(sp => new CompanyShowDto
			{
				Id = sp.Show?.Id ?? 0,
				Name = sp.Show?.Name,
				Code = sp.Show?.Code,
				StartDate = sp.Show?.StartDate,
				EndDate = sp.Show?.EndDate,
				VenueName = sp.Show?.LocationNavigation?.Name,
				City = sp.Show?.LocationNavigation?.City,
				Province = sp.Show?.Province?.ProvinceName,
				Archive = sp.Show?.Archive
			}).OrderByDescending(s => s.StartDate);

			return new GenericRespond<IEnumerable<CompanyShowDto>>
			{
				Data = shows,
				StatusCode = 200,
				Message = "Company shows retrieved successfully"
			};
		}
	}
}
