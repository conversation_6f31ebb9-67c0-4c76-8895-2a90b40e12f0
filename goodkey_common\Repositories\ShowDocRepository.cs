﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
	public interface IShowDocRepository
	{
		Task<IEnumerable<ShowDocs>> GetAllAsync(int locId);
		Task<ShowDocs?> GetByIdAsync(int id);
		Task AddAsync(ShowDocs doc);
		Task UpdateAsync(ShowDocs existingDoc);
		Task<ShowLocations> GetFullLocationHierarchyAsync(int id);
		Task<int> AddShowDoc(ShowDocs doc);
	}
	public class ShowDocRepository : IShowDocRepository
	{
		private readonly GoodkeyContext _context;

		public ShowDocRepository(GoodkeyContext context)
		{
			_context = context;
		}

		public async Task<ShowLocations> GetFullLocationHierarchyAsync(int id)
		{
			return await _context.ShowLocations.Include(x => x.ShowDocs).Include(x => x.ShowLocationHalls).Include(x => x.ShowDocs).FirstOrDefaultAsync(x => x.LocationId == id);
		}

		public async Task<IEnumerable<ShowDocs>> GetAllAsync(int locId)
		{
			return await _context.ShowDocs.Include(d => d.CreatedBy)
					.Include(d => d.UpdatedBy)
					.Include(d => d.DocCategory)
					.Include(d => d.Hall)
					.Include(d => d.Location)
					.Where(x => x.LocationId == locId).ToListAsync();
		}

		public async Task<ShowDocs?> GetByIdAsync(int id)
		{
			return await _context.ShowDocs
				.Include(d => d.DocCategory)
				.Include(d => d.Location)
				.Include(d => d.Hall)
				.Include(d => d.CreatedBy)
				.Include(d => d.UpdatedBy)
				.FirstOrDefaultAsync(d => d.ShowDocId == id);
		}

		public async Task AddAsync(ShowDocs doc)
		{
			_context.ShowDocs.Add(doc);
			await _context.SaveChangesAsync();
		}

		public async Task UpdateAsync(ShowDocs doc)
		{
			_context.ShowDocs.Update(doc);
			await _context.SaveChangesAsync();
		}

		public async Task<int> AddShowDoc(ShowDocs doc)
		{
			_context.ShowDocs.Add(doc);
			await _context.SaveChangesAsync();
			return doc.ShowDocId;
		}


	}
}
