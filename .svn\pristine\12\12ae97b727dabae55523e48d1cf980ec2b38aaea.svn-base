﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
	public interface IOfferingRepository
	{
		Task<IEnumerable<Offering>> GetAllAsync(int groupId);
		Task<IEnumerable<Offering>> GetAll();
		Task<Offering> GetByIdAsync(int id);
		Task AddAsync(Offering offering);
		Task UpdateAsync(int id, Offering updated);
		Task<IEnumerable<OfferingTax>> GetTaxesByOfferingIdAsync(int offeringId);
		Task AddTaxToOfferingAsync(OfferingTax tax);
		Task<GroupType> GetGroupTypeByIdAsync(int id);
		Task<Category> GetCategoryByIdAsync(int id);
		Task<IEnumerable<OfferingProperty>> GetOfferingPropertiesAsync(int offeringId);
		Task<List<OfferingProperty>> AddOfferingPropertiesAsync(IEnumerable<OfferingProperty> properties);
		Task<OfferingProperty> GetOfferingPropertyAsync(int offeringId);
		Task UpdateOfferingPropertyAsync(int id, OfferingProperty updated);
		Task UpdateOfferingPropertiesAsync(IEnumerable<OfferingProperty> properties);
		Task DeleteAddonsByOfferingIdAsync(int offeringId);  // Method to delete old add-ons
		Task AddAddonsAsync(List<OfferingAddOn> addons);  // Method to add new add-ons
	}

	public class OfferingRepository : IOfferingRepository
	{
		private readonly GoodkeyContext _context;

		public OfferingRepository(GoodkeyContext context)
		{
			_context = context;
		}

		public async Task<GroupType> GetGroupTypeByIdAsync(int id)
		{
			return await _context.GroupType
				.FirstOrDefaultAsync(gt => gt.Id == id);
		}

		public async Task<Category> GetCategoryByIdAsync(int id)
		{
			return await _context.Category
				.FirstOrDefaultAsync(c => c.Id == id);
		}

		public async Task<IEnumerable<Offering>> GetAll()
		{
			return await _context.Offering
				.Include(x => x.Category)
				.Include(x => x.GroupType)
				.Include(x => x.OfferingTax)
					.ThenInclude(ot => ot.TaxType)
				.ToListAsync();
		}

		public async Task<IEnumerable<Offering>> GetAllAsync(int groupId)
		{
			return await _context.Offering
				.Include(x => x.Category)
				.Include(x => x.GroupType)
				.Include(x => x.OfferingTax)
					.ThenInclude(ot => ot.TaxType).Where(x => x.GroupTypeId == groupId)
				.ToListAsync();
		}

		public async Task<Offering> GetByIdAsync(int id)
		{
			return await _context.Offering
				.Include(x => x.Category)
				.Include(x => x.GroupType)
				.Include(x => x.OfferingTax)
					.ThenInclude(ot => ot.TaxType)
				.FirstOrDefaultAsync(x => x.Id == id);
		}

		public async Task AddAsync(Offering offering)
		{
			await _context.Offering.AddAsync(offering);
			await _context.SaveChangesAsync();
		}

		public async Task UpdateAsync(int id, Offering updated)
		{
			var existing = await _context.Offering.FindAsync(id);
			if (existing == null) return;

			existing.Name = updated.Name;
			existing.Code = updated.Code;
			existing.SupplierItemNumber = updated.SupplierItemNumber;
			existing.PublicDescription = updated.PublicDescription;
			existing.InternalDescription = updated.InternalDescription;
			existing.DisplayOrder = updated.DisplayOrder;
			existing.UnitChargedId = updated.UnitChargedId;
			existing.IsUnitTypeEach = updated.IsUnitTypeEach;
			existing.IsAddOn = updated.IsAddOn;
			existing.IsForSmOnly = updated.IsForSmOnly;
			existing.IsInternalOnly = updated.IsInternalOnly;
			existing.Image = updated.Image;
			existing.IsActive = updated.IsActive;
			existing.IsObsolete = updated.IsObsolete;
			existing.UpdatedById = updated.UpdatedById;
			existing.UpdatedAt = updated.UpdatedAt;

			await _context.SaveChangesAsync();
		}

		public async Task<IEnumerable<OfferingTax>> GetTaxesByOfferingIdAsync(int offeringId)
		{
			return await _context.OfferingTax
				.Include(x => x.TaxType)
				.Where(x => x.OfferingId == offeringId)
				.ToListAsync();
		}

		public async Task AddTaxToOfferingAsync(OfferingTax tax)
		{
			await _context.OfferingTax.AddAsync(tax);
			await _context.SaveChangesAsync();
		}

		//************************************************* Property *********************************************
		public async Task<IEnumerable<OfferingProperty>> GetOfferingPropertiesAsync(int offeringId)
		{
			return await _context.OfferingProperty
				.Include(op => op.Property1)
				.Include(op => op.Property2)
				.Include(op => op.PropertyOption1)
				.Include(op => op.PropertyOption2)
				.Where(op => op.OfferingId == offeringId)
				.ToListAsync();
		}

		public async Task<List<OfferingProperty>> AddOfferingPropertiesAsync(IEnumerable<OfferingProperty> properties)
		{
			var propertyList = properties.ToList();

			await _context.OfferingProperty.AddRangeAsync(propertyList);
			await _context.SaveChangesAsync();

			return propertyList;
		}


		public async Task<OfferingProperty> GetOfferingPropertyAsync(int propertyId)
		{
			return await _context.OfferingProperty.Include(x => x.Offering).Include(x => x.PropertyOption1).Include(x => x.PropertyOption2).FirstOrDefaultAsync(c => c.Id == propertyId);
		}

		public async Task UpdateOfferingPropertyAsync(int propertyId, OfferingProperty updated)
		{
			var existing = await _context.OfferingProperty.FirstOrDefaultAsync(x => x.Id == propertyId);
			if (existing == null) return;

			existing.Code = updated.Code;
			existing.SupplierItemNumber = updated.SupplierItemNumber;
			existing.IsForSmOnly = updated.IsForSmOnly;
			existing.IsInternalOnly = updated.IsInternalOnly;
			existing.Image = updated.Image;
			existing.IsActive = updated.IsActive;
			existing.UpdatedById = updated.UpdatedById;
			existing.UpdatedAt = updated.UpdatedAt;

			await _context.SaveChangesAsync();
		}

		public async Task UpdateOfferingPropertiesAsync(IEnumerable<OfferingProperty> properties)
		{
			if (properties == null || !properties.Any())
				return;

			foreach (var property in properties)
			{
				_context.OfferingProperty.Update(property);
			}

			await _context.SaveChangesAsync();
		}

		public async Task DeleteAddonsByOfferingIdAsync(int offeringId)
		{
			var addons = await _context.OfferingAddOn
									   .Where(oa => oa.OfferingId == offeringId)
									   .ToListAsync();
			_context.OfferingAddOn.RemoveRange(addons);
			await _context.SaveChangesAsync();
		}

		public async Task AddAddonsAsync(List<OfferingAddOn> addons)
		{
			await _context.OfferingAddOn.AddRangeAsync(addons);
			await _context.SaveChangesAsync();
		}
	}
}
