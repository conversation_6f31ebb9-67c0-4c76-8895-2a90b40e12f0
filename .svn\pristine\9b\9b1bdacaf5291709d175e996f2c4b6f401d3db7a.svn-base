﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class ShowSchedules
    {
        public int Id { get; set; }
        public DateOnly? ShowScheduleDate { get; set; }
        public TimeOnly? TimeStart { get; set; }
        public TimeOnly? TimeEnd { get; set; }
        public int? ShowId { get; set; }
        public bool? ShowScheduleConfirmed { get; set; }
        public string ShowScheduleComments { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? CreatedBy { get; set; }
        public bool? ApplyScheduleToServiceForm { get; set; }

        public virtual AuthUser CreatedByNavigation { get; set; }
        public virtual Shows Show { get; set; }
    }
}