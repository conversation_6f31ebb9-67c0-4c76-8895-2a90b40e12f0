using OfficeOpenXml;
using goodkey_cms.DTO.ExhibitorImport;
using System.Text.RegularExpressions;

namespace goodkey_common.Services
{
    public interface IExcelImportService
    {
        List<ExcelRowData> ReadExcelFile(Stream fileStream, string fileName);
        ExcelTemplateInfoDto GetTemplateInfo();
        List<ExhibitorImportValidationMessageDto> ValidateRowData(ExcelRowData rowData, int rowNumber);
        List<string> ParseBoothNumbers(string boothNumbersText);
        bool IsValidEmail(string email);
        bool IsValidPhoneNumber(string phoneNumber);
        string CleanPhoneNumber(string phoneNumber);
        string GenerateValidationSummary(List<ExcelRowData> rows, List<ExhibitorImportValidationMessageDto> messages);
    }

    public class ExcelImportService : IExcelImportService
    {
        public ExcelImportService()
        {
            // Set EPPlus license context
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        public List<ExcelRowData> ReadExcelFile(Stream fileStream, string fileName)
        {
            var rows = new List<ExcelRowData>();

            using (var package = new ExcelPackage(fileStream))
            {
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                if (worksheet == null)
                    throw new InvalidOperationException("Excel file contains no worksheets");

                // Expected column headers (case-insensitive)
                var expectedHeaders = new Dictionary<string, int>
                {
                    { "company name", -1 },
                    { "contact first name", -1 },
                    { "contact last name", -1 },
                    { "contact email", -1 },
                    { "contact phone", -1 },
                    { "contact mobile", -1 },
                    { "contact ext", -1 },
                    { "booth numbers", -1 },
                    { "contact type", -1 }
                };

                // Find header row and map columns
                var headerRow = 1;
                var maxCol = worksheet.Dimension?.Columns ?? 0;

                for (int col = 1; col <= maxCol; col++)
                {
                    var headerValue = worksheet.Cells[headerRow, col].Text?.Trim().ToLower();
                    if (!string.IsNullOrEmpty(headerValue))
                    {
                        foreach (var expectedHeader in expectedHeaders.Keys.ToList())
                        {
                            if (headerValue.Contains(expectedHeader.Replace(" ", "")) || 
                                headerValue.Replace(" ", "").Contains(expectedHeader.Replace(" ", "")))
                            {
                                expectedHeaders[expectedHeader] = col;
                                break;
                            }
                        }
                    }
                }

                // Validate required columns are found
                var missingColumns = expectedHeaders.Where(h => h.Key == "company name" || h.Key == "contact first name")
                                                  .Where(h => h.Value == -1)
                                                  .Select(h => h.Key)
                                                  .ToList();

                if (missingColumns.Any())
                {
                    throw new InvalidOperationException($"Required columns not found: {string.Join(", ", missingColumns)}");
                }

                // Read data rows
                var maxRow = worksheet.Dimension?.Rows ?? 0;
                for (int row = headerRow + 1; row <= maxRow; row++)
                {
                    // Skip empty rows
                    var companyName = GetCellValue(worksheet, row, expectedHeaders["company name"]);
                    if (string.IsNullOrWhiteSpace(companyName))
                        continue;

                    var rowData = new ExcelRowData
                    {
                        RowNumber = row,
                        CompanyName = companyName,
                        ContactFirstName = GetCellValue(worksheet, row, expectedHeaders["contact first name"]),
                        ContactLastName = GetCellValue(worksheet, row, expectedHeaders["contact last name"]),
                        ContactEmail = GetCellValue(worksheet, row, expectedHeaders["contact email"]),
                        ContactPhone = GetCellValue(worksheet, row, expectedHeaders["contact phone"]),
                        ContactMobile = GetCellValue(worksheet, row, expectedHeaders["contact mobile"]),
                        ContactExt = GetCellValue(worksheet, row, expectedHeaders["contact ext"]),
                        BoothNumbers = GetCellValue(worksheet, row, expectedHeaders["booth numbers"]),
                        ContactType = GetCellValue(worksheet, row, expectedHeaders["contact type"])
                    };

                    rows.Add(rowData);
                }
            }

            return rows;
        }

        private string GetCellValue(ExcelWorksheet worksheet, int row, int col)
        {
            if (col == -1) return string.Empty;
            return worksheet.Cells[row, col].Text?.Trim() ?? string.Empty;
        }

        public ExcelTemplateInfoDto GetTemplateInfo()
        {
            return new ExcelTemplateInfoDto
            {
                Columns = new List<ExcelColumnInfo>
                {
                    new ExcelColumnInfo
                    {
                        Name = "Company Name",
                        Description = "Name of the exhibitor company",
                        IsRequired = true,
                        DataType = "Text",
                        Format = "Plain text",
                        Example = "Tech Solutions Inc."
                    },
                    new ExcelColumnInfo
                    {
                        Name = "Contact First Name",
                        Description = "First name of the primary contact",
                        IsRequired = true,
                        DataType = "Text",
                        Format = "Plain text",
                        Example = "John"
                    },
                    new ExcelColumnInfo
                    {
                        Name = "Contact Last Name",
                        Description = "Last name of the primary contact",
                        IsRequired = false,
                        DataType = "Text",
                        Format = "Plain text",
                        Example = "Doe"
                    },
                    new ExcelColumnInfo
                    {
                        Name = "Contact Email",
                        Description = "Email address of the contact",
                        IsRequired = false,
                        DataType = "Email",
                        Format = "Valid email format",
                        Example = "<EMAIL>"
                    },
                    new ExcelColumnInfo
                    {
                        Name = "Contact Phone",
                        Description = "Primary phone number",
                        IsRequired = false,
                        DataType = "Phone",
                        Format = "Any phone format",
                        Example = "************"
                    },
                    new ExcelColumnInfo
                    {
                        Name = "Contact Mobile",
                        Description = "Mobile phone number",
                        IsRequired = false,
                        DataType = "Phone",
                        Format = "Any phone format",
                        Example = "************"
                    },
                    new ExcelColumnInfo
                    {
                        Name = "Contact Ext",
                        Description = "Phone extension",
                        IsRequired = false,
                        DataType = "Text",
                        Format = "Numbers only",
                        Example = "123"
                    },
                    new ExcelColumnInfo
                    {
                        Name = "Booth Numbers",
                        Description = "Comma-separated list of booth numbers",
                        IsRequired = false,
                        DataType = "Text",
                        Format = "Comma-separated values",
                        Example = "A1, A2, B3"
                    },
                    new ExcelColumnInfo
                    {
                        Name = "Contact Type",
                        Description = "Type of contact (Primary, Manager, Sales, etc.)",
                        IsRequired = false,
                        DataType = "Text",
                        Format = "Predefined values",
                        Example = "Primary"
                    }
                },
                RequiredColumns = new List<string> { "Company Name", "Contact First Name" },
                OptionalColumns = new List<string> 
                { 
                    "Contact Last Name", "Contact Email", "Contact Phone", 
                    "Contact Mobile", "Contact Ext", "Booth Numbers", "Contact Type" 
                },
                ValidValues = new Dictionary<string, List<string>>
                {
                    { "Contact Type", new List<string> 
                        { "Primary", "Manager", "Sales", "Marketing", "Technical", "Admin", "Billing", "Support", "General" } 
                    }
                }
            };
        }

        public List<ExhibitorImportValidationMessageDto> ValidateRowData(ExcelRowData rowData, int rowNumber)
        {
            var messages = new List<ExhibitorImportValidationMessageDto>();

            // Required field validations
            if (string.IsNullOrWhiteSpace(rowData.CompanyName))
            {
                messages.Add(CreateValidationMessage(rowNumber, "CompanyName", rowData.CompanyName,
                    "Error", "Required", "COMPANY_NAME_REQUIRED", "Company Name is required"));
            }

            if (string.IsNullOrWhiteSpace(rowData.ContactFirstName))
            {
                messages.Add(CreateValidationMessage(rowNumber, "ContactFirstName", rowData.ContactFirstName,
                    "Error", "Required", "CONTACT_FIRST_NAME_REQUIRED", "Contact First Name is required"));
            }

            // Email validation
            if (!string.IsNullOrWhiteSpace(rowData.ContactEmail) && !IsValidEmail(rowData.ContactEmail))
            {
                messages.Add(CreateValidationMessage(rowNumber, "ContactEmail", rowData.ContactEmail,
                    "Error", "Format", "INVALID_EMAIL_FORMAT", "Invalid email format"));
            }

            // Phone number validation
            if (!string.IsNullOrWhiteSpace(rowData.ContactPhone) && !IsValidPhoneNumber(rowData.ContactPhone))
            {
                messages.Add(CreateValidationMessage(rowNumber, "ContactPhone", rowData.ContactPhone,
                    "Warning", "Format", "INVALID_PHONE_FORMAT", "Phone number format may be invalid"));
            }

            if (!string.IsNullOrWhiteSpace(rowData.ContactMobile) && !IsValidPhoneNumber(rowData.ContactMobile))
            {
                messages.Add(CreateValidationMessage(rowNumber, "ContactMobile", rowData.ContactMobile,
                    "Warning", "Format", "INVALID_MOBILE_FORMAT", "Mobile number format may be invalid"));
            }

            // Extension validation
            if (!string.IsNullOrWhiteSpace(rowData.ContactExt) && !Regex.IsMatch(rowData.ContactExt, @"^\d+$"))
            {
                messages.Add(CreateValidationMessage(rowNumber, "ContactExt", rowData.ContactExt,
                    "Warning", "Format", "INVALID_EXT_FORMAT", "Extension should contain only numbers"));
            }

            // Booth numbers validation
            if (!string.IsNullOrWhiteSpace(rowData.BoothNumbers))
            {
                var boothNumbers = ParseBoothNumbers(rowData.BoothNumbers);
                if (!boothNumbers.Any())
                {
                    messages.Add(CreateValidationMessage(rowNumber, "BoothNumbers", rowData.BoothNumbers,
                        "Warning", "Format", "INVALID_BOOTH_FORMAT", "Could not parse booth numbers"));
                }
            }

            // Length validations
            if (rowData.CompanyName?.Length > 255)
            {
                messages.Add(CreateValidationMessage(rowNumber, "CompanyName", rowData.CompanyName,
                    "Error", "Format", "COMPANY_NAME_TOO_LONG", "Company name exceeds 255 characters"));
            }

            if (rowData.ContactEmail?.Length > 255)
            {
                messages.Add(CreateValidationMessage(rowNumber, "ContactEmail", rowData.ContactEmail,
                    "Error", "Format", "EMAIL_TOO_LONG", "Email exceeds 255 characters"));
            }

            return messages;
        }

        public List<string> ParseBoothNumbers(string boothNumbersText)
        {
            if (string.IsNullOrWhiteSpace(boothNumbersText))
                return new List<string>();

            return boothNumbersText
                .Split(new char[] { ',', ';', '|', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(b => b.Trim())
                .Where(b => !string.IsNullOrWhiteSpace(b))
                .Distinct()
                .ToList();
        }

        public bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", RegexOptions.IgnoreCase);
                return emailRegex.IsMatch(email);
            }
            catch
            {
                return false;
            }
        }

        public bool IsValidPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            // Remove common phone number formatting
            var cleaned = Regex.Replace(phoneNumber, @"[^\d]", "");
            
            // Accept phone numbers with 7-15 digits
            return cleaned.Length >= 7 && cleaned.Length <= 15;
        }

        public string CleanPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return string.Empty;

            // Keep original format but trim whitespace
            return phoneNumber.Trim();
        }

        public string GenerateValidationSummary(List<ExcelRowData> rows, List<ExhibitorImportValidationMessageDto> messages)
        {
            var errorCount = messages.Count(m => m.MessageType == "Error");
            var warningCount = messages.Count(m => m.MessageType == "Warning");
            var validRows = rows.Count - messages.GroupBy(m => m.RowNumber).Count(g => g.Any(m => m.MessageType == "Error"));

            return $"Total Rows: {rows.Count}, Valid: {validRows}, Errors: {errorCount}, Warnings: {warningCount}";
        }

        private ExhibitorImportValidationMessageDto CreateValidationMessage(int rowNumber, string fieldName, 
            string fieldValue, string messageType, string validationRule, string messageCode, string message)
        {
            return new ExhibitorImportValidationMessageDto
            {
                RowNumber = rowNumber,
                FieldName = fieldName,
                FieldValue = fieldValue,
                MessageType = messageType,
                ValidationRule = validationRule,
                MessageCode = messageCode,
                Message = message
            };
        }
    }
}
