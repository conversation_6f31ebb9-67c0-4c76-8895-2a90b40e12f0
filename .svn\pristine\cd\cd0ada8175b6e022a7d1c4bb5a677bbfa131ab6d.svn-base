
using FileSignatures;
using HeyRed.Mime;
using PdfiumViewer;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Formats.Png;
using SixLabors.ImageSharp.Processing;
using System.Drawing.Imaging;
using Image = SixLabors.ImageSharp.Image;

namespace goodkey_cms.Services
{

    public enum Visibility
    {
        Protected,
        Public
    }
    public enum FileType
    {
        Image,
        Document
    }
    public class StorageSettings
    {
        public string PublicPath { get; set; }
        public string Protected { get; set; }

    }
    public class FileUploadReturn
    {
        public string Path { get; set; }
        public string RelativePath { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
    }
    public class FileDownloadReturn
    {
        public byte[] FileBytes { get; set; }
        public string ContentType { get; set; }
    }
    public class StorageService
    {
        private readonly StorageSettings _storageSettings;
        private readonly FileFormatInspector _inspector;
        public StorageService(IHttpContextAccessor httpContextAccessor, IWebHostEnvironment env)
        {
            _storageSettings = new StorageSettings
            {
                PublicPath = Path.Combine(env.ContentRootPath, "Storage", "public"),
                Protected = Path.Combine(env.ContentRootPath, "Storage", "protected")
            };
            _inspector = new FileFormatInspector();
        }
        public FileUploadReturn UploadFile(IFormFile file, FileType fileType = FileType.Image, string folderName = "Others", Visibility visibility = Visibility.Public, bool generateName = false, string? newName = "")
        {
            var root = visibility == Visibility.Public ? _storageSettings.PublicPath : _storageSettings.Protected;
            var folderType = fileType == FileType.Image ? "img" : "doc";
            var uploadFolder = Path.Combine(root, folderType, folderName);
            if (!Directory.Exists(uploadFolder))
            {
                Directory.CreateDirectory(uploadFolder);
            }
            var fileName = generateName ? (newName == "" ? Guid.NewGuid().ToString() : newName + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + Path.GetExtension(file.FileName)) : file.FileName;
            var filePath = Path.Combine(uploadFolder, fileName);
            using (var fileStream = new FileStream(filePath, FileMode.Create))
            {
                file.CopyTo(fileStream);
            }
            // Construct the relative path
            string relativePath = Path.Combine(folderName, fileName).Replace("\\", "/");
            if (!relativePath.StartsWith("/"))
            {
                relativePath = "/" + relativePath;
            }

            return new()
            {
                Name = fileName,
                Path = filePath,
                Type = file.ContentType,
                RelativePath = relativePath
            };
        }
        public byte[] DownloadFile(string fileName, FileType fileType = FileType.Image, string folderName = "Others", Visibility visibility = Visibility.Public)
        {
            var root = visibility == Visibility.Public ? _storageSettings.PublicPath : _storageSettings.Protected;
            var folderType = fileType == FileType.Image ? "img" : "doc";
            var uploadFolder = Path.Combine(root, folderType, folderName);

            var filePath = Path.Combine(uploadFolder, fileName);
            if (File.Exists(filePath))

            {
                return File.ReadAllBytes(filePath);
            }
            else throw new FileNotFoundException();

        }
        public byte[] LoadFile(string filePath, Visibility visibility = Visibility.Public)
        {
            var root = visibility == Visibility.Public ? _storageSettings.PublicPath : _storageSettings.Protected;

            // Clean up the file path
            filePath = filePath.TrimStart('/');

            var fullPath = Path.Combine(root, filePath);
            if (File.Exists(fullPath))
            {
                return File.ReadAllBytes(fullPath);
            }
            else
            {
                throw new FileNotFoundException($"File not found: {fullPath}");
            }
        }

        public FileDownloadReturn DownloadImage(string filePath, int? width, int? height, double quality = 0.75)
        {

            var fileBytes = LoadFile(Path.Combine("img", filePath));
            string? contentType = GetFileType(filePath);

            using (MemoryStream stream = new(fileBytes))
            {
                contentType = contentType ?? GetContentType(stream) ?? "image/jpeg";
                if (contentType.Contains("svg"))
                {
                    return new() { FileBytes = fileBytes, ContentType = contentType };
                }
                if (contentType != "image/png" && contentType != "image/jpeg")
                    throw new Exception("Trying to load an unsupported file format");
                using Image image = Image.Load(stream);
                int newWidth = width ?? image.Width;
                int newHeight = height ?? image.Height;

                image.Mutate(x => x.Resize(new ResizeOptions
                {
                    Size = new Size(newWidth, newHeight),
                    Mode = ResizeMode.Max
                }));

                using MemoryStream resizedStream = new();
                image.Save(resizedStream, contentType.Contains("png") ?
                    new PngEncoder
                    {
                        CompressionLevel = PngCompressionLevel.BestSpeed,
                    }
                    : new JpegEncoder { Quality = (int)(quality * 100) }); // Save as PNG
                fileBytes = resizedStream.ToArray();
            }
            return new() { FileBytes = fileBytes, ContentType = contentType };

        }
        public FileDownloadReturn DownloadDocument(string filePath, Visibility visibility = Visibility.Public)
        {
            try
            {
                // Clean up the file path
                filePath = filePath.TrimStart('/');

                var fileBytes = LoadFile(Path.Combine("doc", filePath), visibility);
                string contentType = GetFileType(filePath) ?? "application/octet-stream";
                return new() { FileBytes = fileBytes, ContentType = contentType };
            }
            catch (Exception ex)
            {
                throw new FileNotFoundException($"Error downloading document: {ex.Message}", ex);
            }
        }

        public bool DeleteFile(string fileName, FileType fileType = FileType.Image, string folderName = "Others", Visibility visibility = Visibility.Public)
        {
            var root = visibility == Visibility.Public ? _storageSettings.PublicPath : _storageSettings.Protected;
            var folderType = fileType == FileType.Image ? "img" : "doc";
            var uploadFolder = Path.Combine(root, folderType, folderName);

            var filePath = Path.Combine(uploadFolder, fileName);
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
            return true;
        }
        public bool DeleteFileSpecific(string relativePath, FileType fileType = FileType.Image, Visibility visibility = Visibility.Public)
        {
            var root = visibility == Visibility.Public ? _storageSettings.PublicPath : _storageSettings.Protected;
            var folderType = fileType == FileType.Image ? "img" : "doc";
            var path = Path.Combine(root, folderType, relativePath.TrimStart('/'));

            if (File.Exists(path))

            {
                File.Delete(path);
            }
            return true;
        }
        static string? GetFileType(string filePath)
        {
            string extension = Path.GetExtension(filePath);
            if (string.IsNullOrEmpty(extension)) return null;
            string mimeType = MimeTypesMap.GetMimeType(extension);
            return mimeType;
        }
        public string? GetContentType(MemoryStream stream)
        {
            return _inspector.DetermineFileFormat(stream)?.MediaType;

        }

        public FileDownloadReturn GeneratePreview(string path, Visibility visibility = Visibility.Public, int pageNumber = 0)
        {
            var doc = DownloadDocument(path, visibility);

            using var pdfStream = new MemoryStream(doc.FileBytes);
            using var document = PdfDocument.Load(pdfStream);
            if (pageNumber < 0 || pageNumber >= document.PageCount)
                throw new FileNotFoundException();

            using var page = document.Render(pageNumber, 300, 300, true);
            using var memoryStream = new MemoryStream();
            page.Save(memoryStream, ImageFormat.Png);
            return new() { FileBytes = memoryStream.ToArray(), ContentType = "image/png" };
        }

        public string GetFilePath(string relativePath, FileType fileType = FileType.Image, Visibility visibility = Visibility.Public)
        {
            var root = visibility == Visibility.Public ? _storageSettings.PublicPath : _storageSettings.Protected;
            var folderType = fileType == FileType.Image ? "img" : "doc";
            return Path.Combine(root, folderType, relativePath.TrimStart('/'));
        }
    }
}
