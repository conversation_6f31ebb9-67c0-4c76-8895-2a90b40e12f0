﻿namespace goodkey_cms.DTO
{
    public class RoleDetail : BasicDetail
    {
        public string? Description { get; set; }
        public int Level { get; set; }
        public int? RoleGroupId { get; set; }
        public string? RoleGroupName { get; set; }
        public IEnumerable<string>? Permission { get; set; } = Enumerable.Empty<string>();
    }

    public class BasicRoleDetail
    {
        public int Id { get; set; }
        public string Name { get; set; } = null!;
        public int Level { get; set; }
        public int UserCount { get; set; }
    }
}
