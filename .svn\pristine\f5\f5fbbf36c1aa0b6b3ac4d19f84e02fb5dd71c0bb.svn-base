﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class Contact
    {
        public Contact()
        {
            Shows = new HashSet<Shows>();
        }

        public int ContactId { get; set; }
        public int ContactTypeId { get; set; }
        public int? CompanyId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Telephone { get; set; }
        public string Ext { get; set; }
        public string Cellphone { get; set; }
        public int? CreatedById { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool? IsArchived { get; set; }
        public DateTime? ArchivedAt { get; set; }
        public int? ArchivedById { get; set; }
        public int? Authuserid { get; set; }
        public int? LocationId { get; set; }

        public virtual AuthUser ArchivedBy { get; set; }
        public virtual AuthUser Authuser { get; set; }
        public virtual Company Company { get; set; }
        public virtual ContactType ContactType { get; set; }
        public virtual AuthUser CreatedBy { get; set; }
        public virtual ShowLocations Location { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
        public virtual ICollection<Shows> Shows { get; set; }
    }
}