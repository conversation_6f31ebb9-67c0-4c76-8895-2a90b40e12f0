using goodkey_common.DTO;
using goodkey_common.DTO.Gallery;
using goodkey_common.Models;
using goodkey_common.Repositories;
using goodkey_common.Services;
using Microsoft.AspNetCore.Mvc;
using System.Linq;

namespace goodkey_public.Controllers
{
      [Route("api/[controller]")]
    [ApiController]
    public class GalleryController : ControllerBase
    {
        private readonly IGalleryRepository _repository;
        private readonly StorageService _storageService;

        public GalleryController(IGalleryRepository repository, StorageService storageService)
        {
            _repository = repository;
            _storageService = storageService;
        }

        // 1. Get all categories
        [HttpGet("categories")]
        public GenericRespond<IEnumerable<GalleryCategoryDto>> GetAllCategories()
        {
            var categories = _repository.GetAllCategories();
            var result = categories
                .OrderBy(c => c.DisplayOrder ?? int.MaxValue)
                .ThenBy(c => c.Id)
                .Select(c => new GalleryCategoryDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    CreatedAt = c.<PERSON>t,
                    UpdatedAt = c.UpdatedAt,
                    DisplayOrder = c.DisplayOrder
                });
            return new GenericRespond<IEnumerable<GalleryCategoryDto>>
            {
                Data = result,
                StatusCode = 200,
                Message = "Categories retrieved successfully"
            };
        }

        // 2. Get subcategories by category
        [HttpGet("categories/{categoryId}/subcategories")]
        public GenericRespond<IEnumerable<GallerySubcategoryDto>> GetSubcategoriesByCategory(int categoryId)
        {
            var subcategories = _repository.GetSubcategoriesByCategoryId(categoryId);
            var result = subcategories
                .OrderBy(s => s.DisplayOrder ?? int.MaxValue)
                .ThenBy(s => s.Id)
                .Select(s => new GallerySubcategoryDto
                {
                    Id = s.Id,
                    CategoryId = s.CategoryId,
                    Name = s.Name,
                    Description = s.Description,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.UpdatedAt,
                    CategoryName = s.Category?.Name,
                    DisplayOrder = s.DisplayOrder
                });
            return new GenericRespond<IEnumerable<GallerySubcategoryDto>>
            {
                Data = result,
                StatusCode = 200,
                Message = "Subcategories retrieved successfully"
            };
        }

        // 3. Get images by subcategory
        [HttpGet("subcategories/{subcategoryId}/images")]
        public GenericRespond<IEnumerable<GalleryImageDto>> GetImagesBySubcategory(int subcategoryId)
        {
            var images = _repository.GetImagesBySubcategoryId(subcategoryId);
            var result = images
                .OrderBy(i => i.DisplayOrder ?? int.MaxValue)
                .ThenBy(i => i.Id)
                .Select(i => new GalleryImageDto
                {
                    Id = i.Id,
                    SubcategoryId = i.SubcategoryId,
                    Name = i.Name,
                    Alt = i.Alt,
                    Url = i.Url,
                    CreatedAt = i.CreatedAt,
                    SubcategoryName = i.Subcategory?.Name,
                    CategoryName = i.Subcategory?.Category?.Name,
                    DisplayOrder = i.DisplayOrder
                });
            return new GenericRespond<IEnumerable<GalleryImageDto>>
            {
                Data = result,
                StatusCode = 200,
                Message = "Images retrieved successfully"
            };
        }

        // 4. Get single image info
        [HttpGet("images/{id}")]
        public GenericRespond<GalleryImageDto> GetImageById(int id)
        {
            var image = _repository.GetImageById(id);
            if (image == null)
            {
                return new GenericRespond<GalleryImageDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Image not found"
                };
            }
            var dto = new GalleryImageDto
            {
                Id = image.Id,
                SubcategoryId = image.SubcategoryId,
                Name = image.Name,
                Alt = image.Alt,
                Url = image.Url,
                CreatedAt = image.CreatedAt,
                SubcategoryName = image.Subcategory?.Name,
                CategoryName = image.Subcategory?.Category?.Name,
                DisplayOrder = image.DisplayOrder
            };
            return new GenericRespond<GalleryImageDto>
            {
                Data = dto,
                StatusCode = 200,
                Message = "Image retrieved successfully"
            };
        }

        // 5. Download image
        [HttpGet("images/{id}/download")]
      public IActionResult DownloadImage(int id)
        {
            try
            {
                var image = _repository.GetImageById(id);
                if (image == null)
                {
                    return NotFound(new GenericRespond<bool>
                    {
                        Data = false,
                        StatusCode = 404,
                        Message = "Image not found"
                    });
                }

                if (string.IsNullOrEmpty(image.Url))
                {
                    return NotFound(new GenericRespond<bool>
                    {
                        Data = false,
                        StatusCode = 404,
                        Message = "Image URL is empty"
                    });
                }

                // Clean up the URL path and remove the leading slash
                var cleanUrl = image.Url.TrimStart('/');
                
                try
                {
                    // Get the file path from the storage service
                    var filePath = _storageService.GetFilePath(cleanUrl, FileType.Image, Visibility.Public);
                    if (!System.IO.File.Exists(filePath))
                    {
                        return NotFound(new GenericRespond<bool>
                        {
                            Data = false,
                            StatusCode = 404,
                            Message = "Image file not found on disk"
                        });
                    }

                    var downloadedFile = _storageService.DownloadImage(cleanUrl, null, null);
                    if (downloadedFile == null || downloadedFile.FileBytes == null || downloadedFile.FileBytes.Length == 0)
                    {
                        return NotFound(new GenericRespond<bool>
                        {
                            Data = false,
                            StatusCode = 404,
                            Message = "Image file is empty or not found"
                        });
                    }

                    return File(downloadedFile.FileBytes, downloadedFile.ContentType, Path.GetFileName(image.Url));
                }
                catch (FileNotFoundException ex)
                {
                    return NotFound(new GenericRespond<bool>
                    {
                        Data = false,
                        StatusCode = 404,
                        Message = $"Image file not found: {ex.Message}"
                    });
                }
                catch (Exception ex)
                {
                    return StatusCode(500, new GenericRespond<bool>
                    {
                        Data = false,
                        StatusCode = 500,
                        Message = $"Error downloading image: {ex.Message}"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 500,
                    Message = $"Unexpected error: {ex.Message}"
                });
            }
        }
    }
} 