using System;

public class CreateShowGeneralInfoDto
{
    public string Code { get; set; }
    public string Name { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public DateTime? DisplayDate { get; set; }
    public DateTime? OrderDeadlineDate { get; set; }
    public decimal? LateChargePercentage { get; set; }
    public string Link { get; set; }
    public string Description { get; set; }
    public bool? Display { get; set; }
    public int? LocationId { get; set; }
    public DateTime? KioskPrintingQueueDate { get; set; }
    public int? View { get; set; }
    public int? ProvinceId { get; set; }
}

public class UpdateShowGeneralInfoDto
{
    public string Code { get; set; }
    public string Name { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public DateTime? DisplayDate { get; set; }
    public DateTime? OrderDeadlineDate { get; set; }
    public decimal? LateChargePercentage { get; set; }
    public string Link { get; set; }
    public string Description { get; set; }
    public bool? Display { get; set; }
    public int? LocationId { get; set; }
    public DateTime? KioskPrintingQueueDate { get; set; }
    public int? View { get; set; }
    public int? ProvinceId { get; set; }
}

public class ShowGeneralInfoDto
{
    public int Id { get; set; }
    public bool Archive { get; set; }
    public string Code { get; set; }
    public string Name { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public DateTime? DisplayDate { get; set; }
    public DateTime? OrderDeadlineDate { get; set; }
    public decimal? LateChargePercentage { get; set; }
    public string Link { get; set; }
    public string Description { get; set; }
    public bool? Display { get; set; }
    public int? LocationId { get; set; }
    public DateTime? KioskPrintingQueueDate { get; set; }
    public int? View { get; set; }
    public int? ProvinceId { get; set; }
    public DateTime? CreatedAt { get; set; }
    public int? CreatedBy { get; set; }
    public string CreatedByUsername { get; set; }
    public string LocationName { get; set; }
} 