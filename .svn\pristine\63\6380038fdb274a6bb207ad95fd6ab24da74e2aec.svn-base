﻿

using goodkey_cms.DTO;

namespace goodkey_public.Dto.Application
{
    public class MenuBasicDetail : BasicDetail
    {
        public IEnumerable<MenuBasicDetail> Children { get; set; }
        public string? Href { get; set; }
        public string? Target { get; set; }
        public string? Image { get; set; }
        public string? Description { get; set; }
        public string? MetaDescription { get; set; }
        public string? Keywords { get; set; }
        public string? Permission { get; set; }
        public int? Level { get; set; }



    }
    public class MenuDetail : MenuBasicDetail
    {


    }

}
