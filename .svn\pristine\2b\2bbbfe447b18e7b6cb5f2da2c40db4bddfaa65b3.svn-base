using goodkey_common.Models;
using goodkey_common.Context;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
    public interface IGalleryRepository
    {
        // Category methods
        IEnumerable<GalleryCategories> GetAllCategories();
        GalleryCategories GetCategoryById(int id);
        GalleryCategories AddCategory(GalleryCategories category);
        GalleryCategories UpdateCategory(GalleryCategories category);
        bool DeleteCategory(int id);

        // Subcategory methods
        IEnumerable<GallerySubcategories> GetAllSubcategories();
        IEnumerable<GallerySubcategories> GetSubcategoriesByCategoryId(int categoryId);
        GallerySubcategories GetSubcategoryById(int id);
        GallerySubcategories AddSubcategory(GallerySubcategories subcategory);
        GallerySubcategories UpdateSubcategory(GallerySubcategories subcategory);
        bool DeleteSubcategory(int id);

        // Image methods
        IEnumerable<GalleryImages> GetAllImages();
        IEnumerable<GalleryImages> GetImagesBySubcategoryId(int subcategoryId);
        GalleryImages GetImageById(int id);
        GalleryImages AddImage(GalleryImages image);
        GalleryImages UpdateImage(GalleryImages image);
        bool DeleteImage(int id);
    }

    public class GalleryRepository : IGalleryRepository
    {
        private readonly GoodkeyContext _context;

        public GalleryRepository(GoodkeyContext context)
        {
            _context = context;
        }

        // Category methods
        public IEnumerable<GalleryCategories> GetAllCategories()
        {
            return _context.GalleryCategories.ToList();
        }

        public GalleryCategories GetCategoryById(int id)
        {
            return _context.GalleryCategories.FirstOrDefault(c => c.Id == id);
        }

        public GalleryCategories AddCategory(GalleryCategories category)
        {
            _context.GalleryCategories.Add(category);
            _context.SaveChanges();
            return category;
        }

        public GalleryCategories UpdateCategory(GalleryCategories category)
        {
            var existingCategory = _context.GalleryCategories.Find(category.Id);
            if (existingCategory == null)
                return null;

            _context.Entry(existingCategory).CurrentValues.SetValues(category);
            _context.SaveChanges();
            return category;
        }

        public bool DeleteCategory(int id)
        {
            var category = _context.GalleryCategories.Find(id);
            if (category == null)
                return false;

            _context.GalleryCategories.Remove(category);
            _context.SaveChanges();
            return true;
        }

        // Subcategory methods
        public IEnumerable<GallerySubcategories> GetAllSubcategories()
        {
            return _context.GallerySubcategories
                .Include(s => s.Category)
                .ToList();
        }

        public IEnumerable<GallerySubcategories> GetSubcategoriesByCategoryId(int categoryId)
        {
            return _context.GallerySubcategories
                .Include(s => s.Category)
                .Where(s => s.CategoryId == categoryId)
                .ToList();
        }

        public GallerySubcategories GetSubcategoryById(int id)
        {
            return _context.GallerySubcategories
                .Include(s => s.Category)
                .FirstOrDefault(s => s.Id == id);
        }

        public GallerySubcategories AddSubcategory(GallerySubcategories subcategory)
        {
            _context.GallerySubcategories.Add(subcategory);
            _context.SaveChanges();
            return subcategory;
        }

        public GallerySubcategories UpdateSubcategory(GallerySubcategories subcategory)
        {
            var existingSubcategory = _context.GallerySubcategories.Find(subcategory.Id);
            if (existingSubcategory == null)
                return null;

            _context.Entry(existingSubcategory).CurrentValues.SetValues(subcategory);
            _context.SaveChanges();
            return subcategory;
        }

        public bool DeleteSubcategory(int id)
        {
            var subcategory = _context.GallerySubcategories.Find(id);
            if (subcategory == null)
                return false;

            _context.GallerySubcategories.Remove(subcategory);
            _context.SaveChanges();
            return true;
        }

        // Image methods
        public IEnumerable<GalleryImages> GetAllImages()
        {
            return _context.GalleryImages
                .Include(i => i.Subcategory)
                    .ThenInclude(s => s.Category)
                .ToList();
        }

        public IEnumerable<GalleryImages> GetImagesBySubcategoryId(int subcategoryId)
        {
            return _context.GalleryImages
                .Include(i => i.Subcategory)
                    .ThenInclude(s => s.Category)
                .Where(i => i.SubcategoryId == subcategoryId)
                .ToList();
        }

        public GalleryImages GetImageById(int id)
        {
            return _context.GalleryImages
                .Include(i => i.Subcategory)
                    .ThenInclude(s => s.Category)
                .FirstOrDefault(i => i.Id == id);
        }

        public GalleryImages AddImage(GalleryImages image)
        {
            _context.GalleryImages.Add(image);
            _context.SaveChanges();
            return image;
        }

        public GalleryImages UpdateImage(GalleryImages image)
        {
            var existingImage = _context.GalleryImages.Find(image.Id);
            if (existingImage == null)
                return null;

            _context.Entry(existingImage).CurrentValues.SetValues(image);
            _context.SaveChanges();
            return image;
        }

        public bool DeleteImage(int id)
        {
            var image = _context.GalleryImages.Find(id);
            if (image == null)
                return false;

            _context.GalleryImages.Remove(image);
            _context.SaveChanges();
            return true;
        }
    }
} 