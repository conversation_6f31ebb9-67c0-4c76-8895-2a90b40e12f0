using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using goodkey_cms.Infrastructure.Extensions;
using goodkey_cms.Infrastructure.Utils;
using goodkey_cms.DTO.ExhibitorImport;
using goodkey_common.Repositories;
using goodkey_common.Services;
using goodkey_common.Models;
using goodkey_common.DTO;
using goodkey_cms.DTO.ShowExhibitor;
using goodkey_cms.Services;
using goodkey_cms.DTO.User;
using goodkey_cms.Repositories;

namespace goodkey_cms.Controllers
{
    [ApiController]
    [Route("[controller]")]
    [Authorize]
    public class ExhibitorImportController : ControllerBase
    {
        private readonly IExhibitorImportRepository _importRepository;
        private readonly ICompanyRepository _companyRepository;
        private readonly IShowExhibitorRepository _exhibitorRepository;
        private readonly IExcelImportService _excelService;
        private readonly goodkey_cms.Services.StorageService _storageService;
        private readonly IUserRepository _userRepository;
        private readonly AuthService _authService;

        public ExhibitorImportController(
            IExhibitorImportRepository importRepository,
            ICompanyRepository companyRepository,
            IShowExhibitorRepository exhibitorRepository,
            IExcelImportService excelService,
            goodkey_cms.Services.StorageService storageService,
            IUserRepository userRepository,
            AuthService authService)
        {
            _importRepository = importRepository;
            _companyRepository = companyRepository;
            _exhibitorRepository = exhibitorRepository;
            _excelService = excelService;
            _storageService = storageService;
            _userRepository = userRepository;
            _authService = authService;
        }

        // =====================================================
        // Phase 1: Upload and Validation
        // =====================================================

        /// <summary>
        /// Upload Excel file and validate exhibitor data
        /// </summary>
        [HttpPost("validate")]
        public GenericRespond<ExhibitorImportValidationResponseDto> ValidateExcelFile([FromForm] ExhibitorImportUploadDto dto)
        {
            var username = Request.HttpContext.GetUsername();
            var user = _userRepository.GetUserByUsername(username);

            if (user == null)
            {
                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "User not found"
                };
            }

            if (dto.ExcelFile == null || dto.ExcelFile.Length == 0)
            {
                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Excel file is required"
                };
            }

            // Validate file type
            var allowedExtensions = new[] { ".xlsx", ".xls" };
            var fileExtension = Path.GetExtension(dto.ExcelFile.FileName).ToLower();
            if (!allowedExtensions.Contains(fileExtension))
            {
                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Only Excel files (.xlsx, .xls) are allowed"
                };
            }

            // Validate file size (max 10MB)
            if (dto.ExcelFile.Length > 10 * 1024 * 1024)
            {
                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "File size cannot exceed 10MB"
                };
            }

            try
            {
                // Save file temporarily
                var fileName = $"import_{Guid.NewGuid()}{fileExtension}";
                var uploadResult = _storageService.UploadFile(dto.ExcelFile, goodkey_cms.Services.FileType.Document, "imports", goodkey_cms.Services.Visibility.Protected, true, fileName);
                var filePath = uploadResult.Path;

                // Create import session
                var session = _importRepository.CreateSession(
                    dto.ShowId,
                    fileName,
                    dto.ExcelFile.FileName,
                    filePath,
                    dto.ExcelFile.Length,
                    dto.ExcelFile.ContentType,
                    user.UserId
                );

                // Read Excel file
                List<ExcelRowData> excelRows;
                using (var stream = dto.ExcelFile.OpenReadStream())
                {
                    excelRows = _excelService.ReadExcelFile(stream, dto.ExcelFile.FileName);
                }

                if (!excelRows.Any())
                {
                    return new GenericRespond<ExhibitorImportValidationResponseDto>
                    {
                        Data = null,
                        StatusCode = 400,
                        Message = "Excel file contains no data rows"
                    };
                }

                // Process and validate each row
                var validationMessages = new List<ExhibitorImportValidationMessageDto>();
                var duplicates = new List<ExhibitorImportDuplicateDto>();
                var importRows = new List<ExhibitorImportRowDto>();

                foreach (var excelRow in excelRows)
                {
                    // Create import row record
                    var rowId = _importRepository.CreateImportRow(
                        session.SessionId,
                        excelRow.RowNumber,
                        excelRow.CompanyName,
                        excelRow.ContactFirstName,
                        excelRow.ContactLastName,
                        excelRow.ContactEmail,
                        excelRow.ContactPhone,
                        excelRow.ContactMobile,
                        excelRow.ContactExt,
                        excelRow.BoothNumbers,
                        excelRow.ContactType
                    );

                    // Validate row data
                    var rowMessages = _excelService.ValidateRowData(excelRow, excelRow.RowNumber);
                    validationMessages.AddRange(rowMessages);

                    // Store validation messages
                    foreach (var message in rowMessages)
                    {
                        _importRepository.CreateValidationMessage(
                            session.SessionId,
                            rowId,
                            message.RowNumber,
                            message.FieldName,
                            message.FieldValue,
                            message.MessageType,
                            message.ValidationRule,
                            message.MessageCode,
                            message.Message
                        );
                    }

                    // Resolve companies and contact types
                    var resolvedCompany = ResolveCompany(excelRow.CompanyName);
                    var resolvedContactType = _importRepository.FindContactTypeByText(excelRow.ContactType);
                    var resolvedBoothNumbers = _excelService.ParseBoothNumbers(excelRow.BoothNumbers);

                    // Update row with resolved values
                    _importRepository.UpdateRowResolvedValues(
                        rowId,
                        resolvedCompany?.CompanyId,
                        resolvedContactType?.Id,
                        resolvedBoothNumbers.ToArray(),
                        resolvedCompany == null,
                        true, // Always create new contact for imports
                        false // Will be set during duplicate detection
                    );

                    // Create DTO for response
                    var importRow = new ExhibitorImportRowDto
                    {
                        RowNumber = excelRow.RowNumber,
                        Status = rowMessages.Any(m => m.MessageType == "Error") ? "Error" :
                                rowMessages.Any(m => m.MessageType == "Warning") ? "Warning" : "Valid",
                        CompanyName = excelRow.CompanyName,
                        ContactFirstName = excelRow.ContactFirstName,
                        ContactLastName = excelRow.ContactLastName,
                        ContactEmail = excelRow.ContactEmail,
                        ContactPhone = excelRow.ContactPhone,
                        ContactMobile = excelRow.ContactMobile,
                        ContactExt = excelRow.ContactExt,
                        BoothNumbers = excelRow.BoothNumbers,
                        ContactType = excelRow.ContactType,
                        ResolvedCompanyId = resolvedCompany?.CompanyId,
                        ResolvedCompanyName = resolvedCompany?.CompanyName,
                        ResolvedContactTypeId = resolvedContactType?.Id,
                        ResolvedContactTypeName = resolvedContactType?.Name,
                        ResolvedBoothNumbers = resolvedBoothNumbers,
                        IsNewCompany = resolvedCompany == null,
                        IsNewContact = true,
                        HasErrors = rowMessages.Any(m => m.MessageType == "Error"),
                        HasWarnings = rowMessages.Any(m => m.MessageType == "Warning"),
                        ErrorCount = rowMessages.Count(m => m.MessageType == "Error"),
                        WarningCount = rowMessages.Count(m => m.MessageType == "Warning")
                    };

                    importRows.Add(importRow);

                    // Update row validation results
                    _importRepository.UpdateRowValidationResults(
                        rowId,
                        importRow.Status,
                        importRow.HasErrors,
                        importRow.HasWarnings,
                        importRow.ErrorCount,
                        importRow.WarningCount
                    );
                }

                // Detect duplicates
                duplicates = DetectDuplicates(session.SessionId, importRows);

                // Calculate summary
                var errorRows = importRows.Count(r => r.HasErrors);
                var warningRows = importRows.Count(r => r.HasWarnings && !r.HasErrors);
                var validRows = importRows.Count - errorRows;
                var canProceed = errorRows == 0;

                // Update session with counts
                _importRepository.UpdateSessionCounts(session.SessionId, importRows.Count, validRows, errorRows, warningRows);
                _importRepository.UpdateSessionStatus(session.SessionId, "Validated", canProceed);

                var summary = new ExhibitorImportSummaryDto
                {
                    TotalRows = importRows.Count,
                    ValidRows = validRows,
                    ErrorRows = errorRows,
                    WarningRows = warningRows,
                    NewCompanies = importRows.Count(r => r.IsNewCompany),
                    ExistingCompanies = importRows.Count(r => !r.IsNewCompany),
                    NewContacts = importRows.Count(r => r.IsNewContact),
                    DuplicateEmails = duplicates.Count(d => d.DuplicateType == "Email"),
                    DuplicateBooths = duplicates.Count(d => d.DuplicateType == "Booth")
                };

                var response = new ExhibitorImportValidationResponseDto
                {
                    SessionId = session.SessionId.ToString(),
                    ShowId = dto.ShowId,
                    FileName = dto.ExcelFile.FileName,
                    Summary = summary,
                    Rows = importRows,
                    ValidationMessages = validationMessages,
                    Duplicates = duplicates,
                    CanProceed = canProceed,
                    ExpiresAt = session.ExpiresAt
                };

                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    Data = response,
                    StatusCode = 200,
                    Message = canProceed ? "Validation completed successfully" : "Validation completed with errors"
                };
            }
            catch (Exception ex)
            {
                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    Data = null,
                    StatusCode = 500,
                    Message = $"Error processing Excel file: {ex.Message}"
                };
            }
        }

        // =====================================================
        // Helper Methods
        // =====================================================

        private Company? ResolveCompany(string companyName)
        {
            if (string.IsNullOrWhiteSpace(companyName))
                return null;

            var companies = _importRepository.FindCompaniesByName(companyName);

            // Try exact match first
            var exactMatch = companies.FirstOrDefault(c =>
                c.CompanyName.Equals(companyName, StringComparison.OrdinalIgnoreCase));

            if (exactMatch != null)
                return exactMatch;

            // Try partial match with high similarity
            var partialMatch = companies.FirstOrDefault(c =>
                c.CompanyName.ToLower().Contains(companyName.ToLower()) ||
                companyName.ToLower().Contains(c.CompanyName.ToLower()));

            return partialMatch;
        }

        private List<ExhibitorImportDuplicateDto> DetectDuplicates(Guid sessionId, List<ExhibitorImportRowDto> rows)
        {
            var duplicates = new List<ExhibitorImportDuplicateDto>();

            // Email duplicates
            var emailGroups = rows
                .Where(r => !string.IsNullOrWhiteSpace(r.ContactEmail))
                .GroupBy(r => r.ContactEmail.ToLower())
                .Where(g => g.Count() > 1);

            foreach (var group in emailGroups)
            {
                var rowNumbers = group.Select(r => r.RowNumber).ToList();
                var duplicate = new ExhibitorImportDuplicateDto
                {
                    DuplicateType = "Email",
                    DuplicateValue = group.Key,
                    RowNumbers = rowNumbers,
                    ConflictResolution = "Manual"
                };
                duplicates.Add(duplicate);

                // Store in database
                _importRepository.CreateDuplicate(
                    sessionId,
                    "Email",
                    group.Key,
                    string.Join(",", rowNumbers),
                    string.Join(",", rowNumbers), // Using row numbers as IDs for now
                    "Manual"
                );
            }

            // Booth number duplicates
            var boothGroups = rows
                .Where(r => r.ResolvedBoothNumbers.Any())
                .SelectMany(r => r.ResolvedBoothNumbers.Select(b => new { Booth = b, Row = r }))
                .GroupBy(x => x.Booth.ToLower())
                .Where(g => g.Count() > 1);

            foreach (var group in boothGroups)
            {
                var rowNumbers = group.Select(x => x.Row.RowNumber).Distinct().ToList();
                var duplicate = new ExhibitorImportDuplicateDto
                {
                    DuplicateType = "Booth",
                    DuplicateValue = group.Key,
                    RowNumbers = rowNumbers,
                    ConflictResolution = "Manual"
                };
                duplicates.Add(duplicate);

                // Store in database
                _importRepository.CreateDuplicate(
                    sessionId,
                    "Booth",
                    group.Key,
                    string.Join(",", rowNumbers),
                    string.Join(",", rowNumbers),
                    "Manual"
                );
            }

            // Company duplicates (same company name, different contacts)
            var companyGroups = rows
                .GroupBy(r => r.CompanyName.ToLower())
                .Where(g => g.Count() > 1);

            foreach (var group in companyGroups)
            {
                var rowNumbers = group.Select(r => r.RowNumber).ToList();
                var duplicate = new ExhibitorImportDuplicateDto
                {
                    DuplicateType = "Company",
                    DuplicateValue = group.Key,
                    RowNumbers = rowNumbers,
                    ConflictResolution = "Merge" // Default to merge for same company
                };
                duplicates.Add(duplicate);

                // Store in database
                _importRepository.CreateDuplicate(
                    sessionId,
                    "Company",
                    group.Key,
                    string.Join(",", rowNumbers),
                    string.Join(",", rowNumbers),
                    "Merge"
                );
            }

            return duplicates;
        }

        // =====================================================
        // Phase 2: Execution
        // =====================================================

        /// <summary>
        /// Execute the import after validation
        /// </summary>
        [HttpPost("execute")]
        public GenericRespond<ExhibitorImportExecutionResponseDto> ExecuteImport([FromBody] ExhibitorImportExecuteDto dto)
        {
            var username = Request.HttpContext.GetUsername();
            var user = _userRepository.GetUserByUsername(username);

            if (user == null)
            {
                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "User not found"
                };
            }

            var session = _importRepository.GetSessionBySessionId(dto.SessionId);
            if (session == null)
            {
                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Import session not found"
                };
            }

            if (session.Status != "Validated" || !session.CanProceed)
            {
                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Import session is not ready for execution"
                };
            }

            if (session.ExpiresAt < DateTime.UtcNow)
            {
                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Import session has expired"
                };
            }

            try
            {
                // Start execution
                _importRepository.SetSessionExecutionStart(session.SessionId, user.UserId);

                var rows = _importRepository.GetSessionRows(session.SessionId);
                var results = new List<ExhibitorImportExecutionResultDto>();
                var summary = new ExhibitorImportExecutionSummaryDto
                {
                    TotalRows = rows.Count
                };

                foreach (var row in rows.Where(r => r.Status == "Valid" || r.Status == "Warning"))
                {
                    try
                    {
                        var result = ProcessImportRow(row, dto.SendEmailInvites, user.Username);
                        results.Add(result);

                        // Update summary based on result
                        if (result.Status == "Processed")
                        {
                            summary.ProcessedRows++;
                            if (result.CreatedCompanyId.HasValue) summary.CreatedCompanies++;
                            if (result.CreatedContactId.HasValue) summary.CreatedContacts++;
                            if (result.CreatedExhibitorId.HasValue) summary.CreatedExhibitors++;
                            if (result.CreatedUserId.HasValue) summary.CreatedUsers++;
                            if (result.EmailInviteSent) summary.EmailInvitesSent++;
                        }
                        else if (result.Status == "Skipped")
                        {
                            summary.SkippedRows++;
                        }
                        else
                        {
                            summary.FailedRows++;
                        }
                    }
                    catch (Exception ex)
                    {
                        var errorResult = new ExhibitorImportExecutionResultDto
                        {
                            RowNumber = row.RowNumber,
                            Status = "Failed",
                            CompanyName = row.CompanyName,
                            ContactName = $"{row.ContactFirstName} {row.ContactLastName}".Trim(),
                            ErrorMessage = ex.Message,
                            ProcessedAt = DateTime.UtcNow
                        };
                        results.Add(errorResult);
                        summary.FailedRows++;
                    }
                }

                // Complete execution
                _importRepository.SetSessionExecutionComplete(session.SessionId);

                var response = new ExhibitorImportExecutionResponseDto
                {
                    SessionId = dto.SessionId,
                    Status = "Completed",
                    Summary = summary,
                    Results = results,
                    CompletedAt = DateTime.UtcNow
                };

                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    Data = response,
                    StatusCode = 200,
                    Message = $"Import completed successfully. Processed {summary.ProcessedRows} of {summary.TotalRows} rows."
                };
            }
            catch (Exception ex)
            {
                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    Data = null,
                    StatusCode = 500,
                    Message = $"Error executing import: {ex.Message}"
                };
            }
        }

        // =====================================================
        // Row Processing Method
        // =====================================================

        private ExhibitorImportExecutionResultDto ProcessImportRow(ExhibitorImportRows row, bool sendEmailInvites, string username)
        {
            var result = new ExhibitorImportExecutionResultDto
            {
                RowNumber = row.RowNumber,
                CompanyName = row.CompanyName,
                ContactName = $"{row.ContactFirstName} {row.ContactLastName}".Trim(),
                ProcessedAt = DateTime.UtcNow
            };

            try
            {
                // Step 1: Create or get company
                int companyId;
                if (row.ResolvedCompanyId.HasValue)
                {
                    companyId = row.ResolvedCompanyId.Value;
                }
                else
                {
                    // Create new company with minimal data
                    var success = _companyRepository.Add(
                        row.CompanyName,
                        "", // phone
                        "", // email
                        "", // address1
                        "", // address2
                        "", // postalCode
                        "", // city
                        1,  // provinceId (default)
                        1,  // countryId (default)
                        "", // websiteUrl
                        "", // accountNumber
                        "", // companyGroup
                        "", // note
                        false, // isArchived
                        username
                    );

                    if (!success)
                    {
                        result.Status = "Failed";
                        result.ErrorMessage = "Failed to create company";
                        return result;
                    }

                    // Get the created company ID
                    var companies = _importRepository.FindCompaniesByName(row.CompanyName);
                    var createdCompany = companies.FirstOrDefault(c => c.CompanyName == row.CompanyName);
                    if (createdCompany == null)
                    {
                        result.Status = "Failed";
                        result.ErrorMessage = "Failed to retrieve created company";
                        return result;
                    }

                    companyId = createdCompany.CompanyId;
                    result.CreatedCompanyId = companyId;
                }

                // Step 2: Create contact
                var contactId = _companyRepository.AddContact(
                    row.ResolvedContactTypeId ?? 1,
                    companyId,
                    row.ContactFirstName ?? "",
                    row.ContactLastName ?? "",
                    row.ContactEmail ?? "",
                    row.ContactPhone ?? "",
                    row.ContactExt ?? "",
                    row.ContactMobile ?? "",
                    false,
                    username
                );
                result.CreatedContactId = contactId;

                // Step 3: Create user account if contact has email
                int? userId = null;
                if (!string.IsNullOrWhiteSpace(row.ContactEmail))
                {
                    var baseUsername = row.ContactEmail;
                    var uniqueUsername = GenerateUniqueUsername(baseUsername);

                    var userDto = new CreateUserDto
                    {
                        FirstName = row.ContactFirstName ?? "Contact",
                        LastName = row.ContactLastName ?? "User",
                        Email = row.ContactEmail,
                        WorkEmail = row.ContactEmail,
                        VerificationEmail = uniqueUsername,
                        WorkPhoneNumber = row.ContactPhone ?? string.Empty,
                        MobileNumber = row.ContactMobile ?? string.Empty,
                        StatusId = 1,
                        RoleId = 4, // Exhibitor role
                        SalutationId = 1,
                        DepartmentId = 1
                    };

                    userId = _userRepository.CreateUser(username, userDto);

                    if (userId.HasValue)
                    {
                        // Set password and link contact
                        var password = "blue";
                        var hashedPassword = HashUtility.HashPassword(password);
                        _userRepository.SetPassword(userId.Value, hashedPassword);

                        var contact = _companyRepository.GetContact(contactId.Value);
                        if (contact != null)
                        {
                            contact.Authuserid = userId.Value;
                            _companyRepository.UpdateContact(
                                contactId.Value,
                                contact.ContactTypeId,
                                contact.CompanyId,
                                contact.FirstName,
                                contact.LastName,
                                contact.Email,
                                contact.Telephone,
                                contact.Ext,
                                contact.Cellphone,
                                contact.IsArchived ?? false,
                                username
                            );
                        }

                        result.CreatedUserId = userId;

                        // Send invitation email if requested
                        if (sendEmailInvites)
                        {
                            var verificationToken = _userRepository.SetInvitationTokenForUser(userId.Value);
                            if (!string.IsNullOrEmpty(verificationToken))
                            {
                                _authService.SendInviteEmail(row.ContactEmail, verificationToken);
                                result.EmailInviteSent = true;
                            }
                        }
                    }
                    else
                    {
                        // Rollback: Delete contact if user creation failed
                        _companyRepository.DeleteContact(contactId.Value, username);
                        result.Status = "Failed";
                        result.ErrorMessage = "Failed to create user account for contact";
                        return result;
                    }
                }

                // Step 4: Create exhibitor
                var exhibitor = new ShowExhibitors
                {
                    ShowId = row.Session.ShowId,
                    CompanyId = companyId,
                    ContactId = contactId,
                    BoothNumber = row.ResolvedBoothNumbersArray ?? Array.Empty<string>(),
                    CreatedById = _userRepository.GetUserByUsername(username)?.UserId ?? 0
                };

                var createdExhibitor = _exhibitorRepository.CreateShowExhibitor(exhibitor);
                result.CreatedExhibitorId = createdExhibitor.Id;

                // Step 5: Update row with processing results
                _importRepository.UpdateRowProcessingResults(
                    row.Id,
                    result.CreatedCompanyId,
                    result.CreatedContactId,
                    result.CreatedExhibitorId,
                    result.CreatedUserId
                );

                result.Status = "Processed";
                return result;
            }
            catch (Exception ex)
            {
                result.Status = "Failed";
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        private string GenerateUniqueUsername(string baseUsername)
        {
            var username = baseUsername;
            var counter = 1;

            while (_userRepository.GetUserByUsername(username) != null)
            {
                username = $"{baseUsername}_{counter}";
                counter++;
            }

            return username;
        }
    }
}
