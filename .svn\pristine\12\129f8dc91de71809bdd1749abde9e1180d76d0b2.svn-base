using goodkey_cms.Services;
using goodkey_common.DTO;
using goodkey_common.DTO.Gallery;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;
using FileType = goodkey_cms.Services.FileType;
using StorageService = goodkey_cms.Services.StorageService;
using Visibility = goodkey_cms.Services.Visibility;
using System.Linq;

namespace goodkey_cms.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class GalleryController : ControllerBase
    {
        private readonly IGalleryRepository _repository;
        private readonly AuthService _authService;
        private readonly StorageService _storageService;

        public GalleryController(
            IGalleryRepository repository,
            AuthService authService,
            StorageService storageService)
        {
            _repository = repository;
            _authService = authService;
            _storageService = storageService;
        }

        // Category endpoints
        [HttpGet("categories")]
        public GenericRespond<IEnumerable<GalleryCategoryDto>> GetAllCategories()
        {
            var categories = _repository.GetAllCategories();
            var result = categories
                .OrderBy(c => c.DisplayOrder ?? int.MaxValue)
                .ThenBy(c => c.Id)
                .Select(c => new GalleryCategoryDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    CreatedAt = c.CreatedAt,
                    UpdatedAt = c.UpdatedAt,
                    DisplayOrder = c.DisplayOrder
                });

            return new GenericRespond<IEnumerable<GalleryCategoryDto>>
            {
                Data = result,
                StatusCode = 200,
                Message = "Categories retrieved successfully"
            };
        }

        [HttpPost("categories")]
        public GenericRespond<GalleryCategoryDto> CreateCategory([FromBody] CreateGalleryCategoryDto dto)
        {
            var user = _authService.Current;
            if (user == null)
            {
                return new GenericRespond<GalleryCategoryDto>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            var category = new GalleryCategories
            {
                Name = dto.Name,
                Description = dto.Description,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                DisplayOrder = dto.DisplayOrder
            };

            var result = _repository.AddCategory(category);
            return new GenericRespond<GalleryCategoryDto>
            {
                Data = new GalleryCategoryDto
                {
                    Id = result.Id,
                    Name = result.Name,
                    Description = result.Description,
                    CreatedAt = result.CreatedAt,
                    UpdatedAt = result.UpdatedAt,
                    DisplayOrder = result.DisplayOrder
                },
                StatusCode = 200,
                Message = "Category created successfully"
            };
        }

        [HttpPut("categories/{id}")]
        public GenericRespond<GalleryCategoryDto> UpdateCategory(int id, [FromBody] UpdateGalleryCategoryDto dto)
        {
            var user = _authService.Current;
            if (user == null)
            {
                return new GenericRespond<GalleryCategoryDto>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            var category = _repository.GetCategoryById(id);
            if (category == null)
            {
                return new GenericRespond<GalleryCategoryDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Category not found"
                };
            }

            category.Name = dto.Name;
            category.Description = dto.Description;
            category.UpdatedAt = DateTime.UtcNow;
            category.DisplayOrder = dto.DisplayOrder;

            var result = _repository.UpdateCategory(category);
            return new GenericRespond<GalleryCategoryDto>
            {
                Data = new GalleryCategoryDto
                {
                    Id = result.Id,
                    Name = result.Name,
                    Description = result.Description,
                    CreatedAt = result.CreatedAt,
                    UpdatedAt = result.UpdatedAt,
                    DisplayOrder = result.DisplayOrder
                },
                StatusCode = 200,
                Message = "Category updated successfully"
            };
        }

        [HttpDelete("categories/{id}")]
        public GenericRespond<bool> DeleteCategory(int id)
        {
            var user = _authService.Current;
            if (user == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            var result = _repository.DeleteCategory(id);
            return new GenericRespond<bool>
            {
                Data = result,
                StatusCode = result ? 200 : 404,
                Message = result ? "Category deleted successfully" : "Category not found"
            };
        }

        // Subcategory endpoints
        [HttpGet("subcategories")]
        public GenericRespond<IEnumerable<GallerySubcategoryDto>> GetAllSubcategories()
        {
            var subcategories = _repository.GetAllSubcategories();
            var result = subcategories
                .OrderBy(s => s.DisplayOrder ?? int.MaxValue)
                .ThenBy(s => s.Id)
                .Select(s => new GallerySubcategoryDto
                {
                    Id = s.Id,
                    CategoryId = s.CategoryId,
                    Name = s.Name,
                    Description = s.Description,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.UpdatedAt,
                    CategoryName = s.Category?.Name,
                    DisplayOrder = s.DisplayOrder
                });

            return new GenericRespond<IEnumerable<GallerySubcategoryDto>>
            {
                Data = result,
                StatusCode = 200,
                Message = "Subcategories retrieved successfully"
            };
        }

        [HttpGet("categories/{categoryId}/subcategories")]
        public GenericRespond<IEnumerable<GallerySubcategoryDto>> GetSubcategoriesByCategory(int categoryId)
        {
            var subcategories = _repository.GetSubcategoriesByCategoryId(categoryId);
            var result = subcategories
                .OrderBy(s => s.DisplayOrder ?? int.MaxValue)
                .ThenBy(s => s.Id)
                .Select(s => new GallerySubcategoryDto
                {
                    Id = s.Id,
                    CategoryId = s.CategoryId,
                    Name = s.Name,
                    Description = s.Description,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.UpdatedAt,
                    CategoryName = s.Category?.Name,
                    DisplayOrder = s.DisplayOrder
                });

            return new GenericRespond<IEnumerable<GallerySubcategoryDto>>
            {
                Data = result,
                StatusCode = 200,
                Message = "Subcategories retrieved successfully"
            };
        }

        [HttpPost("subcategories")]
        public GenericRespond<GallerySubcategoryDto> CreateSubcategory([FromBody] CreateGallerySubcategoryDto dto)
        {
            var user = _authService.Current;
            if (user == null)
            {
                return new GenericRespond<GallerySubcategoryDto>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            var subcategory = new GallerySubcategories
            {
                CategoryId = dto.CategoryId,
                Name = dto.Name,
                Description = dto.Description,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                DisplayOrder = dto.DisplayOrder
            };

            var result = _repository.AddSubcategory(subcategory);
            return new GenericRespond<GallerySubcategoryDto>
            {
                Data = new GallerySubcategoryDto
                {
                    Id = result.Id,
                    CategoryId = result.CategoryId,
                    Name = result.Name,
                    Description = result.Description,
                    CreatedAt = result.CreatedAt,
                    UpdatedAt = result.UpdatedAt,
                    CategoryName = result.Category?.Name,
                    DisplayOrder = result.DisplayOrder
                },
                StatusCode = 200,
                Message = "Subcategory created successfully"
            };
        }

        [HttpPut("subcategories/{id}")]
        public GenericRespond<GallerySubcategoryDto> UpdateSubcategory(int id, [FromBody] UpdateGallerySubcategoryDto dto)
        {
            var user = _authService.Current;
            if (user == null)
            {
                return new GenericRespond<GallerySubcategoryDto>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            var subcategory = _repository.GetSubcategoryById(id);
            if (subcategory == null)
            {
                return new GenericRespond<GallerySubcategoryDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Subcategory not found"
                };
            }

            subcategory.Name = dto.Name;
            subcategory.Description = dto.Description;
            subcategory.UpdatedAt = DateTime.UtcNow;
            subcategory.DisplayOrder = dto.DisplayOrder;

            var result = _repository.UpdateSubcategory(subcategory);
            return new GenericRespond<GallerySubcategoryDto>
            {
                Data = new GallerySubcategoryDto
                {
                    Id = result.Id,
                    CategoryId = result.CategoryId,
                    Name = result.Name,
                    Description = result.Description,
                    CreatedAt = result.CreatedAt,
                    UpdatedAt = result.UpdatedAt,
                    CategoryName = result.Category?.Name,
                    DisplayOrder = result.DisplayOrder
                },
                StatusCode = 200,
                Message = "Subcategory updated successfully"
            };
        }

        [HttpDelete("subcategories/{id}")]
        public GenericRespond<bool> DeleteSubcategory(int id)
        {
            var user = _authService.Current;
            if (user == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            var result = _repository.DeleteSubcategory(id);
            return new GenericRespond<bool>
            {
                Data = result,
                StatusCode = result ? 200 : 404,
                Message = result ? "Subcategory deleted successfully" : "Subcategory not found"
            };
        }

        // Image endpoints
        [HttpGet("images")]
        public GenericRespond<IEnumerable<GalleryImageDto>> GetAllImages()
        {
            var images = _repository.GetAllImages();
            var result = images
                .OrderBy(i => i.DisplayOrder ?? int.MaxValue)
                .ThenBy(i => i.Id)
                .Select(i => new GalleryImageDto
                {
                    Id = i.Id,
                    SubcategoryId = i.SubcategoryId,
                    Name = i.Name,
                    Alt = i.Alt,
                    Url = i.Url,
                    CreatedAt = i.CreatedAt,
                    SubcategoryName = i.Subcategory?.Name,
                    CategoryName = i.Subcategory?.Category?.Name,
                    DisplayOrder = i.DisplayOrder
                });

            return new GenericRespond<IEnumerable<GalleryImageDto>>
            {
                Data = result,
                StatusCode = 200,
                Message = "Images retrieved successfully"
            };
        }

        [HttpGet("subcategories/{subcategoryId}/images")]
        public GenericRespond<IEnumerable<GalleryImageDto>> GetImagesBySubcategory(int subcategoryId)
        {
            var images = _repository.GetImagesBySubcategoryId(subcategoryId);
            var result = images
                .OrderBy(i => i.DisplayOrder ?? int.MaxValue)
                .ThenBy(i => i.Id)
                .Select(i => new GalleryImageDto
                {
                    Id = i.Id,
                    SubcategoryId = i.SubcategoryId,
                    Name = i.Name,
                    Alt = i.Alt,
                    Url = i.Url,
                    CreatedAt = i.CreatedAt,
                    SubcategoryName = i.Subcategory?.Name,
                    CategoryName = i.Subcategory?.Category?.Name,
                    DisplayOrder = i.DisplayOrder
                });

            return new GenericRespond<IEnumerable<GalleryImageDto>>
            {
                Data = result,
                StatusCode = 200,
                Message = "Images retrieved successfully"
            };
        }

        [HttpGet("images/{id}")]
        public GenericRespond<GalleryImageDto> GetImageById(int id)
        {
            var image = _repository.GetImageById(id);
            if (image == null)
            {
                return new GenericRespond<GalleryImageDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Image not found"
                };
            }

            return new GenericRespond<GalleryImageDto>
            {
                Data = new GalleryImageDto
                {
                    Id = image.Id,
                    SubcategoryId = image.SubcategoryId,
                    Name = image.Name,
                    Alt = image.Alt,
                    Url = image.Url,
                    CreatedAt = image.CreatedAt,
                    SubcategoryName = image.Subcategory?.Name,
                    CategoryName = image.Subcategory?.Category?.Name,
                    DisplayOrder = image.DisplayOrder
                },
                StatusCode = 200,
                Message = "Image retrieved successfully"
            };
        }

        [HttpGet("images/{id}/download")]
        public IActionResult DownloadImage(int id)
        {
            try
            {
                var image = _repository.GetImageById(id);
                if (image == null)
                {
                    return NotFound(new GenericRespond<bool>
                    {
                        Data = false,
                        StatusCode = 404,
                        Message = "Image not found"
                    });
                }

                if (string.IsNullOrEmpty(image.Url))
                {
                    return NotFound(new GenericRespond<bool>
                    {
                        Data = false,
                        StatusCode = 404,
                        Message = "Image URL is empty"
                    });
                }

                // Clean up the URL path and remove the leading slash
                var cleanUrl = image.Url.TrimStart('/');
                
                try
                {
                    // Get the file path from the storage service
                    var filePath = _storageService.GetFilePath(cleanUrl, FileType.Image, Visibility.Public);
                    if (!System.IO.File.Exists(filePath))
                    {
                        return NotFound(new GenericRespond<bool>
                        {
                            Data = false,
                            StatusCode = 404,
                            Message = "Image file not found on disk"
                        });
                    }

                    var downloadedFile = _storageService.DownloadImage(cleanUrl, null, null);
                    if (downloadedFile == null || downloadedFile.FileBytes == null || downloadedFile.FileBytes.Length == 0)
                    {
                        return NotFound(new GenericRespond<bool>
                        {
                            Data = false,
                            StatusCode = 404,
                            Message = "Image file is empty or not found"
                        });
                    }

                    return File(downloadedFile.FileBytes, downloadedFile.ContentType, Path.GetFileName(image.Url));
                }
                catch (FileNotFoundException ex)
                {
                    return NotFound(new GenericRespond<bool>
                    {
                        Data = false,
                        StatusCode = 404,
                        Message = $"Image file not found: {ex.Message}"
                    });
                }
                catch (Exception ex)
                {
                    return StatusCode(500, new GenericRespond<bool>
                    {
                        Data = false,
                        StatusCode = 500,
                        Message = $"Error downloading image: {ex.Message}"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 500,
                    Message = $"Unexpected error: {ex.Message}"
                });
            }
        }

        private string GetContentType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".webp" => "image/webp",
                _ => "application/octet-stream"
            };
        }

        [HttpPost("images")]
        public GenericRespond<GalleryImageDto> CreateImage([FromForm] CreateGalleryImageDto dto)
        {
            var user = _authService.Current;
            if (user == null)
            {
                return new GenericRespond<GalleryImageDto>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            if (dto.Image == null || dto.Image.Length == 0)
            {
                return new GenericRespond<GalleryImageDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "No image file provided"
                };
            }

            var file = _storageService.UploadFile(
                dto.Image,
                FileType.Image,
                "Gallery",
                Visibility.Public,
                true,
                "Gallery_Image");

            var image = new GalleryImages
            {
                SubcategoryId = dto.SubcategoryId,
                Name = dto.Name,
                Alt = dto.Alt,
                Url = file.RelativePath,
                CreatedAt = DateTime.UtcNow,
                DisplayOrder = dto.DisplayOrder
            };

            var result = _repository.AddImage(image);
            return new GenericRespond<GalleryImageDto>
            {
                Data = new GalleryImageDto
                {
                    Id = result.Id,
                    SubcategoryId = result.SubcategoryId,
                    Name = result.Name,
                    Alt = result.Alt,
                    Url = result.Url,
                    CreatedAt = result.CreatedAt,
                    SubcategoryName = result.Subcategory?.Name,
                    CategoryName = result.Subcategory?.Category?.Name,
                    DisplayOrder = result.DisplayOrder
                },
                StatusCode = 200,
                Message = "Image uploaded successfully"
            };
        }

        [HttpPut("images/{id}")]
        public GenericRespond<GalleryImageDto> UpdateImage(int id, [FromForm] UpdateGalleryImageDto dto)
        {
            var user = _authService.Current;
            if (user == null)
            {
                return new GenericRespond<GalleryImageDto>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            var image = _repository.GetImageById(id);
            if (image == null)
            {
                return new GenericRespond<GalleryImageDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Image not found"
                };
            }

            if (dto.Image != null && dto.Image.Length > 0)
            {
                // Delete old image
                _storageService.DeleteFileSpecific(image.Url);

                // Upload new image
                var file = _storageService.UploadFile(
                    dto.Image,
                    FileType.Image,
                    "Gallery",
                    Visibility.Public,
                    true,
                    "Gallery_Image");

                image.Url = file.RelativePath;
            }

            image.Name = dto.Name;
            image.Alt = dto.Alt;
            image.DisplayOrder = dto.DisplayOrder;

            var result = _repository.UpdateImage(image);
            return new GenericRespond<GalleryImageDto>
            {
                Data = new GalleryImageDto
                {
                    Id = result.Id,
                    SubcategoryId = result.SubcategoryId,
                    Name = result.Name,
                    Alt = result.Alt,
                    Url = result.Url,
                    CreatedAt = result.CreatedAt,
                    SubcategoryName = result.Subcategory?.Name,
                    CategoryName = result.Subcategory?.Category?.Name,
                    DisplayOrder = result.DisplayOrder
                },
                StatusCode = 200,
                Message = "Image updated successfully"
            };
        }

        [HttpDelete("images/{id}")]
        public GenericRespond<bool> DeleteImage(int id)
        {
            var user = _authService.Current;
            if (user == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            var image = _repository.GetImageById(id);
            if (image == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 404,
                    Message = "Image not found"
                };
            }

            // Delete the image file first
            _storageService.DeleteFileSpecific(image.Url);

            var result = _repository.DeleteImage(id);
            return new GenericRespond<bool>
            {
                Data = result,
                StatusCode = 200,
                Message = "Image deleted successfully"
            };
        }
    }
} 